{"name": "camera-controls", "version": "3.1.0", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "main": "dist/camera-controls.cjs", "module": "dist/camera-controls.module.js", "types": "dist/index.d.ts", "files": ["dist"], "repository": "yomotsu/camera-controls", "devDependencies": {"@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-typescript": "^12.1.2", "@stylistic/eslint-plugin": "^4.2.0", "@types/three": "^0.174.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "conventional-changelog-conventionalcommits": "^9.0.0", "downlevel-dts": "^0.11.0", "eslint": "^9.22.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "npm-run-all": "^4.1.5", "open-cli": "^8.0.0", "rollup": "^4.35.0", "semantic-release": "^24.2.6", "serve": "^14.2.4", "terser": "^5.39.0", "three": "^0.174.0", "tslib": "^2.8.1", "typedoc": "^0.27.9", "typescript": "^5.8.2"}, "peerDependencies": {"three": ">=0.126.1"}, "scripts": {"prepare": "husky install", "dev": "npm-run-all -p dev:*", "dev:rollup": "rollup --config --watch", "dev:serve": "serve -S -p 3000 ./ && kill $!", "dev:open": "open-cli http://localhost:3000/examples/", "build": "rollup --config && terser dist/camera-controls.module.js -o dist/camera-controls.module.min.js --comments '/^!/' && downlevel-dts . .", "prepack": "npm run build", "lint": "eslint src", "typedoc": "typedoc", "release": "semantic-release"}, "browserslist": ["> 1%", "last 3 versions", "not dead"], "typesVersions": {"<=3.4.0-0": {"*": ["./"]}}, "keywords": ["three", "three.js", "orbit", "controls", "OrbitControls", "camera"], "packageManager": "npm@10.8.2+sha256.c8c61ba0fa0ab3b5120efd5ba97fdaf0e0b495eef647a97c4413919eda0a878b", "engines": {"node": ">=20.11.0", "npm": ">=10.8.2"}}