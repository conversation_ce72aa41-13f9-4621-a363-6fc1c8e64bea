{"name": "jsx-runtime", "amdName": "jsxRuntime", "version": "1.0.0", "private": true, "description": "Preact JSX runtime", "main": "dist/jsxRuntime.js", "module": "dist/jsxRuntime.module.js", "umd:main": "dist/jsxRuntime.umd.js", "source": "src/index.js", "types": "src/index.d.ts", "license": "MIT", "peerDependencies": {"preact": "^10.0.0"}, "mangle": {"regex": "^_"}, "exports": {".": {"types": "./src/index.d.ts", "browser": "./dist/jsxRuntime.module.js", "umd": "./dist/jsxRuntime.umd.js", "import": "./dist/jsxRuntime.mjs", "require": "./dist/jsxRuntime.js"}}}