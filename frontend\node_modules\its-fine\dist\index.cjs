"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const m=require("react");function y(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const n in e)if(n!=="default"){const r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:()=>e[n]})}}return t.default=e,Object.freeze(t)}const o=y(m),f=(()=>{var e,t;return typeof window!="undefined"&&(((e=window.document)==null?void 0:e.createElement)||((t=window.navigator)==null?void 0:t.product)==="ReactNative")})()?o.useLayoutEffect:o.useEffect;function i(e,t,n){if(!e)return;if(n(e)===!0)return e;let r=t?e.return:e.child;for(;r;){const u=i(r,t,n);if(u)return u;r=t?null:r.sibling}}function l(e){try{return Object.defineProperties(e,{_currentRenderer:{get(){return null},set(){}},_currentRenderer2:{get(){return null},set(){}}})}catch(t){return e}}const a=l(o.createContext(null));class d extends o.Component{render(){return o.createElement(a.Provider,{value:this._reactInternals},this.props.children)}}function s(){const e=o.useContext(a);if(e===null)throw new Error("its-fine: useFiber must be called within a <FiberProvider />!");const t=o.useId();return o.useMemo(()=>{for(const r of[e,e==null?void 0:e.alternate]){if(!r)continue;const u=i(r,!1,p=>{let c=p.memoizedState;for(;c;){if(c.memoizedState===t)return!0;c=c.next}});if(u)return u}},[e,t])}function h(){const e=s(),t=o.useMemo(()=>i(e,!0,n=>{var r;return((r=n.stateNode)==null?void 0:r.containerInfo)!=null}),[e]);return t==null?void 0:t.stateNode.containerInfo}function v(e){const t=s(),n=o.useRef(void 0);return f(()=>{var r;n.current=(r=i(t,!1,u=>typeof u.type=="string"&&(e===void 0||u.type===e)))==null?void 0:r.stateNode},[t]),n}function C(e){const t=s(),n=o.useRef(void 0);return f(()=>{var r;n.current=(r=i(t,!0,u=>typeof u.type=="string"&&(e===void 0||u.type===e)))==null?void 0:r.stateNode},[t]),n}const w=Symbol.for("react.context"),g=e=>e!==null&&typeof e=="object"&&"$$typeof"in e&&e.$$typeof===w;function b(){const e=s(),[t]=o.useState(()=>new Map);t.clear();let n=e;for(;n;){const r=n.type;g(r)&&r!==a&&!t.has(r)&&t.set(r,o.use(l(r))),n=n.return}return t}function x(){const e=b();return o.useMemo(()=>Array.from(e.keys()).reduce((t,n)=>r=>o.createElement(t,null,o.createElement(n.Provider,{...r,value:e.get(n)})),t=>o.createElement(d,{...t})),[e])}exports.FiberProvider=d;exports.traverseFiber=i;exports.useContainer=h;exports.useContextBridge=x;exports.useContextMap=b;exports.useFiber=s;exports.useNearestChild=v;exports.useNearestParent=C;
//# sourceMappingURL=index.cjs.map
