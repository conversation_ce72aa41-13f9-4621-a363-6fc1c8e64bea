import{Fragment as t,options as e}from"preact";if("function"!=typeof Symbol){let t=0;Symbol=function(e){return`@@${e}${++t}`},Symbol.for=t=>`@@${t}`}const n=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,r=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,o=/[\s\n\\/='"\0<>]/,i=/^xlink:?./,l=/["&<]/;function s(t){if(!1===l.test(t+=""))return t;let e=0,n=0,r="",o="";for(;n<t.length;n++){switch(t.charCodeAt(n)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 60:o="&lt;";break;default:continue}n!==e&&(r+=t.slice(e,n)),r+=o,e=n+1}return n!==e&&(r+=t.slice(e,n)),r}let c=(t,e)=>String(t).replace(/(\n+)/g,"$1"+(e||"\t")),f=(t,e,n)=>String(t).length>(e||40)||!n&&-1!==String(t).indexOf("\n")||-1!==String(t).indexOf("<");const u={},a=/([A-Z])/g;function p(t){let e="";for(let r in t){let o=t[r];null!=o&&""!==o&&(e&&(e+=" "),e+="-"==r[0]?r:u[r]||(u[r]=r.replace(a,"-$1").toLowerCase()),e="number"==typeof o&&!1===n.test(r)?e+": "+o+"px;":e+": "+o+";")}return e||void 0}function d(t,e){return Array.isArray(e)?e.reduce(d,t):null!=e&&!1!==e&&t.push(e),t}function y(){this.__d=!0}function _(t,e){return{__v:t,context:e,props:t.props,setState:y,forceUpdate:y,__d:!0,__h:[]}}function g(t,e){let n=t.contextType,r=n&&e[n.__c];return null!=n?r?r.props.value:n.__:e}const b=[];function m(n,l,u,a,y,h){if(null==n||"boolean"==typeof n)return"";if("object"!=typeof n)return s(n);let j=u.pretty,x=j&&"string"==typeof j?j:"\t";if(Array.isArray(n)){let t="";for(let e=0;e<n.length;e++)j&&e>0&&(t+="\n"),t+=m(n[e],l,u,a,y,h);return t}let S=n.type,v=n.props,k=!1;if("function"==typeof S){if(k=!0,!u.shallow||!a&&!1!==u.renderRootComponent){if(S===t){const t=[];return d(t,n.props.children),m(t,l,u,!1!==u.shallowHighOrder,y,h)}{let t,r=n.__c=_(n,l);e.__b&&e.__b(n);let o=e.__r;if(S.prototype&&"function"==typeof S.prototype.render){let e=g(S,l);r=n.__c=new S(v,e),r.__v=n,r._dirty=r.__d=!0,r.props=v,null==r.state&&(r.state={}),null==r._nextState&&null==r.__s&&(r._nextState=r.__s=r.state),r.context=e,S.getDerivedStateFromProps?r.state=Object.assign({},r.state,S.getDerivedStateFromProps(r.props,r.state)):r.componentWillMount&&(r.componentWillMount(),r.state=r._nextState!==r.state?r._nextState:r.__s!==r.state?r.__s:r.state),o&&o(n),t=r.render(r.props,r.state,r.context)}else{let e=g(S,l),i=0;for(;r.__d&&i++<25;)r.__d=!1,o&&o(n),t=S.call(n.__c,v,e)}return r.getChildContext&&(l=Object.assign({},l,r.getChildContext())),e.diffed&&e.diffed(n),m(t,l,u,!1!==u.shallowHighOrder,y,h)}}S=(A=S).displayName||A!==Function&&A.name||function(t){let e=(Function.prototype.toString.call(t).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!e){let n=-1;for(let e=b.length;e--;)if(b[e]===t){n=e;break}n<0&&(n=b.push(t)-1),e=`UnnamedComponent${n}`}return e}(A)}var A;let O,w,$="<"+S;if(v){let t=Object.keys(v);u&&!0===u.sortAttributes&&t.sort();for(let e=0;e<t.length;e++){let n=t[e],r=v[n];if("children"===n){O=r;continue}if(o.test(n))continue;if(!(u&&u.allAttributes||"key"!==n&&"ref"!==n&&"__self"!==n&&"__source"!==n))continue;if("defaultValue"===n)n="value";else if("defaultChecked"===n)n="checked";else if("defaultSelected"===n)n="selected";else if("className"===n){if(void 0!==v.class)continue;n="class"}else y&&i.test(n)&&(n=n.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===n){if(v.for)continue;n="for"}"style"===n&&r&&"object"==typeof r&&(r=p(r)),"a"===n[0]&&"r"===n[1]&&"boolean"==typeof r&&(r=String(r));let c=u.attributeHook&&u.attributeHook(n,r,l,u,k);if(c||""===c)$+=c;else if("dangerouslySetInnerHTML"===n)w=r&&r.__html;else if("textarea"===S&&"value"===n)O=r;else if((r||0===r||""===r)&&"function"!=typeof r){if(!(!0!==r&&""!==r||(r=n,u&&u.xml))){$=$+" "+n;continue}if("value"===n){if("select"===S){h=r;continue}"option"===S&&h==r&&void 0===v.selected&&($+=" selected")}$+=` ${n}="${s(r)}"`}}}if(j){let t=$.replace(/\n\s*/," ");t===$||~t.indexOf("\n")?j&&~$.indexOf("\n")&&($+="\n"):$=t}if($+=">",o.test(S))throw new Error(`${S} is not a valid HTML tag name in ${$}`);let F,C=r.test(S)||u.voidElements&&u.voidElements.test(S),E=[];if(w)j&&f(w)&&(w="\n"+x+c(w,x)),$+=w;else if(null!=O&&d(F=[],O).length){let t=j&&~$.indexOf("\n"),e=!1;for(let n=0;n<F.length;n++){let r=F[n];if(null!=r&&!1!==r){let n=m(r,l,u,!0,"svg"===S||"foreignObject"!==S&&y,h);if(j&&!t&&f(n)&&(t=!0),n)if(j){let t=n.length>0&&"<"!=n[0];e&&t?E[E.length-1]+=n:E.push(n),e=t}else E.push(n)}}if(j&&t)for(let t=E.length;t--;)E[t]="\n"+x+c(E[t],x)}if(E.length||w)$+=E.join("");else if(u&&u.xml)return $.substring(0,$.length-1)+" />";return!C||F||w?(j&&~$.indexOf("\n")&&($+="\n"),$+=`</${S}>`):$=$.replace(/>$/," />"),$}const h={shallow:!0};x.render=x;const j=[];function x(t,n,r){n=n||{};const o=e.__s;let i;return e.__s=!0,i=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?m(t,n,r):O(t,n,!1,void 0),e.__c&&e.__c(t,j),e.__s=o,j.length=0,i}function S(t,e){return"className"===t?"class":"htmlFor"===t?"for":"defaultValue"===t?"value":"defaultChecked"===t?"checked":"defaultSelected"===t?"selected":e&&i.test(t)?t.toLowerCase().replace(/^xlink:?/,"xlink:"):t}function v(t,e){return"style"===t&&null!=e&&"object"==typeof e?p(e):"a"===t[0]&&"r"===t[1]&&"boolean"==typeof e?String(e):e}const k=Array.isArray,A=Object.assign;function O(n,i,l,c){if(null==n||!0===n||!1===n||""===n)return"";if("object"!=typeof n)return s(n);if(k(n)){let t="";for(let e=0;e<n.length;e++)t+=O(n[e],i,l,c);return t}e.__b&&e.__b(n);let f=n.type,u=n.props;if("function"==typeof f){if(f===t)return O(n.props.children,i,l,c);let r;r=f.prototype&&"function"==typeof f.prototype.render?function(t,n){let r=t.type,o=g(r,n),i=new r(t.props,o);t.__c=i,i.__v=t,i.__d=!0,i.props=t.props,null==i.state&&(i.state={}),null==i.__s&&(i.__s=i.state),i.context=o,r.getDerivedStateFromProps?i.state=A({},i.state,r.getDerivedStateFromProps(i.props,i.state)):i.componentWillMount&&(i.componentWillMount(),i.state=i.__s!==i.state?i.__s:i.state);let l=e.__r;return l&&l(t),i.render(i.props,i.state,i.context)}(n,i):function(t,n){let r,o=_(t,n),i=g(t.type,n);t.__c=o;let l=e.__r,s=0;for(;o.__d&&s++<25;)o.__d=!1,l&&l(t),r=t.type.call(o,t.props,i);return r}(n,i);let o=n.__c;o.getChildContext&&(i=A({},i,o.getChildContext()));const s=O(r,i,l,c);return e.diffed&&e.diffed(n),s}let a,p,d="<";if(d+=f,u){a=u.children;for(let t in u){let e=u[t];if(!("key"===t||"ref"===t||"__self"===t||"__source"===t||"children"===t||"className"===t&&"class"in u||"htmlFor"===t&&"for"in u||o.test(t)))if(t=S(t,l),e=v(t,e),"dangerouslySetInnerHTML"===t)p=e&&e.__html;else if("textarea"===f&&"value"===t)a=e;else if((e||0===e||""===e)&&"function"!=typeof e){if(!0===e||""===e){e=t,d=d+" "+t;continue}if("value"===t){if("select"===f){c=e;continue}"option"!==f||c!=e||"selected"in u||(d+=" selected")}d=d+" "+t+'="'+s(e)+'"'}}}let y=d;if(d+=">",o.test(f))throw new Error(`${f} is not a valid HTML tag name in ${d}`);let b="",m=!1;if(p)b+=p,m=!0;else if("string"==typeof a)b+=s(a),m=!0;else if(k(a))for(let t=0;t<a.length;t++){let e=a[t];if(null!=e&&!1!==e){let t=O(e,i,"svg"===f||"foreignObject"!==f&&l,c);t&&(b+=t,m=!0)}}else if(null!=a&&!1!==a&&!0!==a){let t=O(a,i,"svg"===f||"foreignObject"!==f&&l,c);t&&(b+=t,m=!0)}if(e.diffed&&e.diffed(n),m)d+=b;else if(r.test(f))return y+" />";return d+"</"+f+">"}x.shallowRender=(t,e)=>x(t,e,h);const w=/(\\|\"|\')/g,$=Object.prototype.toString,F=Date.prototype.toISOString,C=Error.prototype.toString,E=RegExp.prototype.toString,M=Symbol.prototype.toString,H=/^Symbol\((.*)\)(.*)$/,N=/\n/gi,D=Object.getOwnPropertySymbols||(t=>[]);function I(t){return"[object Array]"===t||"[object ArrayBuffer]"===t||"[object DataView]"===t||"[object Float32Array]"===t||"[object Float64Array]"===t||"[object Int8Array]"===t||"[object Int16Array]"===t||"[object Int32Array]"===t||"[object Uint8Array]"===t||"[object Uint8ClampedArray]"===t||"[object Uint16Array]"===t||"[object Uint32Array]"===t}function L(t){return""===t.name?"[Function anonymous]":"[Function "+t.name+"]"}function W(t){return M.call(t).replace(H,"Symbol($1)")}function P(t){return"["+C.call(t)+"]"}function T(t){if(!0===t||!1===t)return""+t;if(void 0===t)return"undefined";if(null===t)return"null";const e=typeof t;if("number"===e)return function(t){return t!=+t?"NaN":0===t&&1/t<0?"-0":""+t}(t);if("string"===e)return'"'+function(t){return t.replace(w,"\\$1")}(t)+'"';if("function"===e)return L(t);if("symbol"===e)return W(t);const n=$.call(t);return"[object WeakMap]"===n?"WeakMap {}":"[object WeakSet]"===n?"WeakSet {}":"[object Function]"===n||"[object GeneratorFunction]"===n?L(t,min):"[object Symbol]"===n?W(t):"[object Date]"===n?F.call(t):"[object Error]"===n?P(t):"[object RegExp]"===n?E.call(t):"[object Arguments]"===n&&0===t.length?"Arguments []":I(n)&&0===t.length?t.constructor.name+" []":t instanceof Error&&P(t)}function U(t,e,n,r,o,i,l,s,c,f){let u="";if(t.length){u+=o;const a=n+e;for(let n=0;n<t.length;n++)u+=a+V(t[n],e,a,r,o,i,l,s,c,f),n<t.length-1&&(u+=","+r);u+=o+n}return"["+u+"]"}function R(t,e,n,r,o,i,l,s,c,f){if((i=i.slice()).indexOf(t)>-1)return"[Circular]";i.push(t);const u=++s>l;if(!u&&t.toJSON&&"function"==typeof t.toJSON)return V(t.toJSON(),e,n,r,o,i,l,s,c,f);const a=$.call(t);return"[object Arguments]"===a?u?"[Arguments]":function(t,e,n,r,o,i,l,s,c,f){return(f?"":"Arguments ")+U(t,e,n,r,o,i,l,s,c,f)}(t,e,n,r,o,i,l,s,c,f):I(a)?u?"[Array]":function(t,e,n,r,o,i,l,s,c,f){return(f?"":t.constructor.name+" ")+U(t,e,n,r,o,i,l,s,c,f)}(t,e,n,r,o,i,l,s,c,f):"[object Map]"===a?u?"[Map]":function(t,e,n,r,o,i,l,s,c,f){let u="Map {";const a=t.entries();let p=a.next();if(!p.done){u+=o;const t=n+e;for(;!p.done;)u+=t+V(p.value[0],e,t,r,o,i,l,s,c,f)+" => "+V(p.value[1],e,t,r,o,i,l,s,c,f),p=a.next(),p.done||(u+=","+r);u+=o+n}return u+"}"}(t,e,n,r,o,i,l,s,c,f):"[object Set]"===a?u?"[Set]":function(t,e,n,r,o,i,l,s,c,f){let u="Set {";const a=t.entries();let p=a.next();if(!p.done){u+=o;const t=n+e;for(;!p.done;)u+=t+V(p.value[1],e,t,r,o,i,l,s,c,f),p=a.next(),p.done||(u+=","+r);u+=o+n}return u+"}"}(t,e,n,r,o,i,l,s,c,f):"object"==typeof t?u?"[Object]":function(t,e,n,r,o,i,l,s,c,f){let u=(f?"":t.constructor?t.constructor.name+" ":"Object ")+"{",a=Object.keys(t).sort();const p=D(t);if(p.length&&(a=a.filter(t=>!("symbol"==typeof t||"[object Symbol]"===$.call(t))).concat(p)),a.length){u+=o;const p=n+e;for(let n=0;n<a.length;n++){const d=a[n];u+=p+V(d,e,p,r,o,i,l,s,c,f)+": "+V(t[d],e,p,r,o,i,l,s,c,f),n<a.length-1&&(u+=","+r)}u+=o+n}return u+"}"}(t,e,n,r,o,i,l,s,c,f):void 0}function J(t,e,n,r,o,i,l,s,c,f){let u,a=!1;for(let e=0;e<c.length;e++)if(u=c[e],u.test(t)){a=!0;break}return!!a&&u.print(t,function(t){return V(t,e,n,r,o,i,l,s,c,f)},function(t){const r=n+e;return r+t.replace(N,"\n"+r)},{edgeSpacing:o,spacing:r})}function V(t,e,n,r,o,i,l,s,c,f){return T(t)||J(t,e,n,r,o,i,l,s,c,f)||R(t,e,n,r,o,i,l,s,c,f)}const q={indent:2,min:!1,maxDepth:Infinity,plugins:[]};function z(t){return new Array(t+1).join(" ")}let B={test:t=>t&&"object"==typeof t&&"type"in t&&"props"in t&&"key"in t,print:(t,e,n)=>x(t,B.context,B.opts)},G={plugins:[B]},Z={attributeHook:function(t,e,n,r,o){let i=typeof e;if("dangerouslySetInnerHTML"===t)return!1;if(null==e||"function"===i&&!r.functions)return"";if(r.skipFalseAttributes&&!o&&(!1===e||("class"===t||"style"===t)&&""===e))return"";let l="string"==typeof r.pretty?r.pretty:"\t";return"string"!==i?("function"!==i||r.functionNames?(B.context=n,B.opts=r,~(e=function(t,e){let n,r;e?(function(t){if(Object.keys(t).forEach(t=>{if(!q.hasOwnProperty(t))throw new Error("prettyFormat: Invalid option: "+t)}),t.min&&void 0!==t.indent&&0!==t.indent)throw new Error("prettyFormat: Cannot run with min option and indent")}(e),e=function(t){const e={};return Object.keys(q).forEach(n=>e[n]=t.hasOwnProperty(n)?t[n]:q[n]),e.min&&(e.indent=0),e}(e)):e=q;const o=e.min?" ":"\n",i=e.min?"":"\n";if(e&&e.plugins.length){n=z(e.indent),r=[];var l=J(t,n,"",o,i,r,e.maxDepth,0,e.plugins,e.min);if(l)return l}return T(t)||(n||(n=z(e.indent)),r||(r=[]),R(t,n,"",o,i,r,e.maxDepth,0,e.plugins,e.min))}(e,G)).indexOf("\n")&&(e=`${c("\n"+e,l)}\n`)):e="Function",c(`\n${t}={${e}}`,l)):`\n${l}${t}="${s(e)}"`},jsx:!0,xml:!1,functions:!0,functionNames:!0,skipFalseAttributes:!0,pretty:"  "};function K(t,e,n,r){return x(t,e,n=Object.assign({},Z,n||{}))}export default K;export{K as render};
//# sourceMappingURL=jsx.modern.js.map
