{"name": "@types/three", "version": "0.179.0", "description": "TypeScript definitions for three", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/three", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "josh<PERSON><PERSON><PERSON>", "url": "https://github.com/joshua<PERSON>s"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}], "type": "module", "main": "", "types": "index.d.ts", "exports": {".": {"import": "./build/three.module.js", "require": "./build/three.cjs"}, "./examples/fonts/*": "./examples/fonts/*", "./examples/jsm/*": "./examples/jsm/*", "./addons": "./examples/jsm/Addons.js", "./addons/*": "./examples/jsm/*", "./src/*": "./src/*", "./webgpu": "./build/three.webgpu.js", "./tsl": "./build/three.tsl.js", "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/three"}, "scripts": {}, "dependencies": {"@dimforge/rapier3d-compat": "~0.12.0", "@tweenjs/tween.js": "~23.1.3", "@types/stats.js": "*", "@types/webxr": "*", "@webgpu/types": "*", "fflate": "~0.8.2", "meshoptimizer": "~0.22.0"}, "peerDependencies": {}, "typesPublisherContentHash": "184261baf6703d118d65b2d005bcc72af9877981f78da02aa9756329a95df8a8", "typeScriptVersion": "5.2"}