{"name": "is-promise", "version": "2.2.2", "description": "Test whether an object looks like a promises-a+ promise", "main": "./index.js", "scripts": {"test": "mocha -R spec"}, "files": ["index.js", "index.mjs"], "repository": {"type": "git", "url": "https://github.com/then/is-promise.git"}, "author": "ForbesLindesay", "license": "MIT", "devDependencies": {"better-assert": "^1.0.2", "mocha": "~1.7.4"}}