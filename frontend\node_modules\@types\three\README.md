# Installation
> `npm install --save @types/three`

# Summary
This package contains type definitions for three (https://threejs.org/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/three.

### Additional Details
 * Last updated: Mon, 04 Aug 2025 15:38:34 GMT
 * Dependencies: [@dimforge/rapier3d-compat](https://npmjs.com/package/@dimforge/rapier3d-compat), [@tweenjs/tween.js](https://npmjs.com/package/@tweenjs/tween.js), [@types/stats.js](https://npmjs.com/package/@types/stats.js), [@types/webxr](https://npmjs.com/package/@types/webxr), [@webgpu/types](https://npmjs.com/package/@webgpu/types), [fflate](https://npmjs.com/package/fflate), [meshoptimizer](https://npmjs.com/package/meshoptimizer)

# Credits
These definitions were written by [<PERSON>](https://github.com/joshua<PERSON><PERSON>), and [<PERSON>](https://github.com/Methuselah96).
