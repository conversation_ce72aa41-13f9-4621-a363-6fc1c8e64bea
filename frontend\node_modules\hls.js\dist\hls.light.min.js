!function e(t){var r,i;r=this,i=function(){"use strict";function r(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,f(i.key),i)}}function i(e,t,i){return t&&r(e.prototype,t),i&&r(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e,t,r){return(t=f(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)({}).hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e},n.apply(null,arguments)}function s(e){return s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},s(e)}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,h(e,t)}function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(l=function(){return!!e})()}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,i)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function h(e,t){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},h(e,t)}function f(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t);if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}function c(e){var t="function"==typeof Map?new Map:void 0;return c=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(e,t,r){if(l())return Reflect.construct.apply(null,arguments);var i=[null];i.push.apply(i,t);var a=new(e.bind.apply(e,i));return r&&h(a,r.prototype),a}(e,arguments,s(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),h(r,e)},c(e)}function v(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var g,m,p={exports:{}},y=(g||(g=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function i(){}function a(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function n(e,t,i,n,s){if("function"!=typeof i)throw new TypeError("The listener must be a function");var o=new a(i,n||e,s),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new i:delete e._events[t]}function o(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,i,a=[];if(0===this._eventsCount)return a;for(i in e=this._events)t.call(e,i)&&a.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?a.concat(Object.getOwnPropertySymbols(e)):a},o.prototype.listeners=function(e){var t=r?r+e:e,i=this._events[t];if(!i)return[];if(i.fn)return[i.fn];for(var a=0,n=i.length,s=new Array(n);a<n;a++)s[a]=i[a].fn;return s},o.prototype.listenerCount=function(e){var t=r?r+e:e,i=this._events[t];return i?i.fn?1:i.length:0},o.prototype.emit=function(e,t,i,a,n,s){var o=r?r+e:e;if(!this._events[o])return!1;var l,u,d=this._events[o],h=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),h){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,i),!0;case 4:return d.fn.call(d.context,t,i,a),!0;case 5:return d.fn.call(d.context,t,i,a,n),!0;case 6:return d.fn.call(d.context,t,i,a,n,s),!0}for(u=1,l=new Array(h-1);u<h;u++)l[u-1]=arguments[u];d.fn.apply(d.context,l)}else{var f,c=d.length;for(u=0;u<c;u++)switch(d[u].once&&this.removeListener(e,d[u].fn,void 0,!0),h){case 1:d[u].fn.call(d[u].context);break;case 2:d[u].fn.call(d[u].context,t);break;case 3:d[u].fn.call(d[u].context,t,i);break;case 4:d[u].fn.call(d[u].context,t,i,a);break;default:if(!l)for(f=1,l=new Array(h-1);f<h;f++)l[f-1]=arguments[f];d[u].fn.apply(d[u].context,l)}}return!0},o.prototype.on=function(e,t,r){return n(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return n(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,i,a){var n=r?r+e:e;if(!this._events[n])return this;if(!t)return s(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||a&&!o.once||i&&o.context!==i||s(this,n);else{for(var l=0,u=[],d=o.length;l<d;l++)(o[l].fn!==t||a&&!o[l].once||i&&o[l].context!==i)&&u.push(o[l]);u.length?this._events[n]=1===u.length?u[0]:u:s(this,n)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&s(this,t)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o}(p)),p.exports),E=v(y),T={exports:{}},S=(m||(m=1,function(e,t){var r,i,a,n,s;r=/^(?=((?:[a-zA-Z0-9+\-.]+:)?))\1(?=((?:\/\/[^\/?#]*)?))\2(?=((?:(?:[^?#\/]*\/)*[^;?#\/]*)?))\3((?:;[^?#]*)?)(\?[^#]*)?(#[^]*)?$/,i=/^(?=([^\/?#]*))\1([^]*)$/,a=/(?:\/|^)\.(?=\/)/g,n=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,s={buildAbsoluteURL:function(e,t,r){if(r=r||{},e=e.trim(),!(t=t.trim())){if(!r.alwaysNormalize)return e;var a=s.parseURL(e);if(!a)throw new Error("Error trying to parse base URL.");return a.path=s.normalizePath(a.path),s.buildURLFromParts(a)}var n=s.parseURL(t);if(!n)throw new Error("Error trying to parse relative URL.");if(n.scheme)return r.alwaysNormalize?(n.path=s.normalizePath(n.path),s.buildURLFromParts(n)):t;var o=s.parseURL(e);if(!o)throw new Error("Error trying to parse base URL.");if(!o.netLoc&&o.path&&"/"!==o.path[0]){var l=i.exec(o.path);o.netLoc=l[1],o.path=l[2]}o.netLoc&&!o.path&&(o.path="/");var u={scheme:o.scheme,netLoc:n.netLoc,path:null,params:n.params,query:n.query,fragment:n.fragment};if(!n.netLoc&&(u.netLoc=o.netLoc,"/"!==n.path[0]))if(n.path){var d=o.path,h=d.substring(0,d.lastIndexOf("/")+1)+n.path;u.path=s.normalizePath(h)}else u.path=o.path,n.params||(u.params=o.params,n.query||(u.query=o.query));return null===u.path&&(u.path=r.alwaysNormalize?s.normalizePath(n.path):n.path),s.buildURLFromParts(u)},parseURL:function(e){var t=r.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(a,"");e.length!==(e=e.replace(n,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}},e.exports=s}(T)),T.exports),L=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},R=Number.isSafeInteger||function(e){return"number"==typeof e&&Math.abs(e)<=A},A=Number.MAX_SAFE_INTEGER||9007199254740991,b=function(e){return e.NETWORK_ERROR="networkError",e.MEDIA_ERROR="mediaError",e.KEY_SYSTEM_ERROR="keySystemError",e.MUX_ERROR="muxError",e.OTHER_ERROR="otherError",e}({}),k=function(e){return e.KEY_SYSTEM_NO_KEYS="keySystemNoKeys",e.KEY_SYSTEM_NO_ACCESS="keySystemNoAccess",e.KEY_SYSTEM_NO_SESSION="keySystemNoSession",e.KEY_SYSTEM_NO_CONFIGURED_LICENSE="keySystemNoConfiguredLicense",e.KEY_SYSTEM_LICENSE_REQUEST_FAILED="keySystemLicenseRequestFailed",e.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED="keySystemServerCertificateRequestFailed",e.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED="keySystemServerCertificateUpdateFailed",e.KEY_SYSTEM_SESSION_UPDATE_FAILED="keySystemSessionUpdateFailed",e.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED="keySystemStatusOutputRestricted",e.KEY_SYSTEM_STATUS_INTERNAL_ERROR="keySystemStatusInternalError",e.KEY_SYSTEM_DESTROY_MEDIA_KEYS_ERROR="keySystemDestroyMediaKeysError",e.KEY_SYSTEM_DESTROY_CLOSE_SESSION_ERROR="keySystemDestroyCloseSessionError",e.KEY_SYSTEM_DESTROY_REMOVE_SESSION_ERROR="keySystemDestroyRemoveSessionError",e.MANIFEST_LOAD_ERROR="manifestLoadError",e.MANIFEST_LOAD_TIMEOUT="manifestLoadTimeOut",e.MANIFEST_PARSING_ERROR="manifestParsingError",e.MANIFEST_INCOMPATIBLE_CODECS_ERROR="manifestIncompatibleCodecsError",e.LEVEL_EMPTY_ERROR="levelEmptyError",e.LEVEL_LOAD_ERROR="levelLoadError",e.LEVEL_LOAD_TIMEOUT="levelLoadTimeOut",e.LEVEL_PARSING_ERROR="levelParsingError",e.LEVEL_SWITCH_ERROR="levelSwitchError",e.AUDIO_TRACK_LOAD_ERROR="audioTrackLoadError",e.AUDIO_TRACK_LOAD_TIMEOUT="audioTrackLoadTimeOut",e.SUBTITLE_LOAD_ERROR="subtitleTrackLoadError",e.SUBTITLE_TRACK_LOAD_TIMEOUT="subtitleTrackLoadTimeOut",e.FRAG_LOAD_ERROR="fragLoadError",e.FRAG_LOAD_TIMEOUT="fragLoadTimeOut",e.FRAG_DECRYPT_ERROR="fragDecryptError",e.FRAG_PARSING_ERROR="fragParsingError",e.FRAG_GAP="fragGap",e.REMUX_ALLOC_ERROR="remuxAllocError",e.KEY_LOAD_ERROR="keyLoadError",e.KEY_LOAD_TIMEOUT="keyLoadTimeOut",e.BUFFER_ADD_CODEC_ERROR="bufferAddCodecError",e.BUFFER_INCOMPATIBLE_CODECS_ERROR="bufferIncompatibleCodecsError",e.BUFFER_APPEND_ERROR="bufferAppendError",e.BUFFER_APPENDING_ERROR="bufferAppendingError",e.BUFFER_STALLED_ERROR="bufferStalledError",e.BUFFER_FULL_ERROR="bufferFullError",e.BUFFER_SEEK_OVER_HOLE="bufferSeekOverHole",e.BUFFER_NUDGE_ON_STALL="bufferNudgeOnStall",e.ASSET_LIST_LOAD_ERROR="assetListLoadError",e.ASSET_LIST_LOAD_TIMEOUT="assetListLoadTimeout",e.ASSET_LIST_PARSING_ERROR="assetListParsingError",e.INTERSTITIAL_ASSET_ITEM_ERROR="interstitialAssetItemError",e.INTERNAL_EXCEPTION="internalException",e.INTERNAL_ABORTED="aborted",e.ATTACH_MEDIA_ERROR="attachMediaError",e.UNKNOWN="unknown",e}({}),D=function(e){return e.MEDIA_ATTACHING="hlsMediaAttaching",e.MEDIA_ATTACHED="hlsMediaAttached",e.MEDIA_DETACHING="hlsMediaDetaching",e.MEDIA_DETACHED="hlsMediaDetached",e.MEDIA_ENDED="hlsMediaEnded",e.STALL_RESOLVED="hlsStallResolved",e.BUFFER_RESET="hlsBufferReset",e.BUFFER_CODECS="hlsBufferCodecs",e.BUFFER_CREATED="hlsBufferCreated",e.BUFFER_APPENDING="hlsBufferAppending",e.BUFFER_APPENDED="hlsBufferAppended",e.BUFFER_EOS="hlsBufferEos",e.BUFFERED_TO_END="hlsBufferedToEnd",e.BUFFER_FLUSHING="hlsBufferFlushing",e.BUFFER_FLUSHED="hlsBufferFlushed",e.MANIFEST_LOADING="hlsManifestLoading",e.MANIFEST_LOADED="hlsManifestLoaded",e.MANIFEST_PARSED="hlsManifestParsed",e.LEVEL_SWITCHING="hlsLevelSwitching",e.LEVEL_SWITCHED="hlsLevelSwitched",e.LEVEL_LOADING="hlsLevelLoading",e.LEVEL_LOADED="hlsLevelLoaded",e.LEVEL_UPDATED="hlsLevelUpdated",e.LEVEL_PTS_UPDATED="hlsLevelPtsUpdated",e.LEVELS_UPDATED="hlsLevelsUpdated",e.AUDIO_TRACKS_UPDATED="hlsAudioTracksUpdated",e.AUDIO_TRACK_SWITCHING="hlsAudioTrackSwitching",e.AUDIO_TRACK_SWITCHED="hlsAudioTrackSwitched",e.AUDIO_TRACK_LOADING="hlsAudioTrackLoading",e.AUDIO_TRACK_LOADED="hlsAudioTrackLoaded",e.AUDIO_TRACK_UPDATED="hlsAudioTrackUpdated",e.SUBTITLE_TRACKS_UPDATED="hlsSubtitleTracksUpdated",e.SUBTITLE_TRACKS_CLEARED="hlsSubtitleTracksCleared",e.SUBTITLE_TRACK_SWITCH="hlsSubtitleTrackSwitch",e.SUBTITLE_TRACK_LOADING="hlsSubtitleTrackLoading",e.SUBTITLE_TRACK_LOADED="hlsSubtitleTrackLoaded",e.SUBTITLE_TRACK_UPDATED="hlsSubtitleTrackUpdated",e.SUBTITLE_FRAG_PROCESSED="hlsSubtitleFragProcessed",e.CUES_PARSED="hlsCuesParsed",e.NON_NATIVE_TEXT_TRACKS_FOUND="hlsNonNativeTextTracksFound",e.INIT_PTS_FOUND="hlsInitPtsFound",e.FRAG_LOADING="hlsFragLoading",e.FRAG_LOAD_EMERGENCY_ABORTED="hlsFragLoadEmergencyAborted",e.FRAG_LOADED="hlsFragLoaded",e.FRAG_DECRYPTED="hlsFragDecrypted",e.FRAG_PARSING_INIT_SEGMENT="hlsFragParsingInitSegment",e.FRAG_PARSING_USERDATA="hlsFragParsingUserdata",e.FRAG_PARSING_METADATA="hlsFragParsingMetadata",e.FRAG_PARSED="hlsFragParsed",e.FRAG_BUFFERED="hlsFragBuffered",e.FRAG_CHANGED="hlsFragChanged",e.FPS_DROP="hlsFpsDrop",e.FPS_DROP_LEVEL_CAPPING="hlsFpsDropLevelCapping",e.MAX_AUTO_LEVEL_UPDATED="hlsMaxAutoLevelUpdated",e.ERROR="hlsError",e.DESTROYING="hlsDestroying",e.KEY_LOADING="hlsKeyLoading",e.KEY_LOADED="hlsKeyLoaded",e.LIVE_BACK_BUFFER_REACHED="hlsLiveBackBufferReached",e.BACK_BUFFER_REACHED="hlsBackBufferReached",e.STEERING_MANIFEST_LOADED="hlsSteeringManifestLoaded",e.ASSET_LIST_LOADING="hlsAssetListLoading",e.ASSET_LIST_LOADED="hlsAssetListLoaded",e.INTERSTITIALS_UPDATED="hlsInterstitialsUpdated",e.INTERSTITIALS_BUFFERED_TO_BOUNDARY="hlsInterstitialsBufferedToBoundary",e.INTERSTITIAL_ASSET_PLAYER_CREATED="hlsInterstitialAssetPlayerCreated",e.INTERSTITIAL_STARTED="hlsInterstitialStarted",e.INTERSTITIAL_ASSET_STARTED="hlsInterstitialAssetStarted",e.INTERSTITIAL_ASSET_ENDED="hlsInterstitialAssetEnded",e.INTERSTITIAL_ASSET_ERROR="hlsInterstitialAssetError",e.INTERSTITIAL_ENDED="hlsInterstitialEnded",e.INTERSTITIALS_PRIMARY_RESUMED="hlsInterstitialsPrimaryResumed",e.PLAYOUT_LIMIT_REACHED="hlsPlayoutLimitReached",e.EVENT_CUE_ENTER="hlsEventCueEnter",e}({}),_="manifest",I="level",C="audioTrack",P="subtitleTrack",x="main",w="audio",O="subtitle",F=function(){function e(e,t,r){void 0===t&&(t=0),void 0===r&&(r=0),this.halfLife=void 0,this.alpha_=void 0,this.estimate_=void 0,this.totalWeight_=void 0,this.halfLife=e,this.alpha_=e?Math.exp(Math.log(.5)/e):0,this.estimate_=t,this.totalWeight_=r}var t=e.prototype;return t.sample=function(e,t){var r=Math.pow(this.alpha_,e);this.estimate_=t*(1-r)+r*this.estimate_,this.totalWeight_+=e},t.getTotalWeight=function(){return this.totalWeight_},t.getEstimate=function(){if(this.alpha_){var e=1-Math.pow(this.alpha_,this.totalWeight_);if(e)return this.estimate_/e}return this.estimate_},e}(),M=function(){function e(e,t,r,i){void 0===i&&(i=100),this.defaultEstimate_=void 0,this.minWeight_=void 0,this.minDelayMs_=void 0,this.slow_=void 0,this.fast_=void 0,this.defaultTTFB_=void 0,this.ttfb_=void 0,this.defaultEstimate_=r,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new F(e),this.fast_=new F(t),this.defaultTTFB_=i,this.ttfb_=new F(e)}var t=e.prototype;return t.update=function(e,t){var r=this.slow_,i=this.fast_,a=this.ttfb_;r.halfLife!==e&&(this.slow_=new F(e,r.getEstimate(),r.getTotalWeight())),i.halfLife!==t&&(this.fast_=new F(t,i.getEstimate(),i.getTotalWeight())),a.halfLife!==e&&(this.ttfb_=new F(e,a.getEstimate(),a.getTotalWeight()))},t.sample=function(e,t){var r=(e=Math.max(e,this.minDelayMs_))/1e3,i=8*t/r;this.fast_.sample(r,i),this.slow_.sample(r,i)},t.sampleTTFB=function(e){var t=e/1e3,r=Math.sqrt(2)*Math.exp(-Math.pow(t,2)/2);this.ttfb_.sample(r,Math.max(e,5))},t.canEstimate=function(){return this.fast_.getTotalWeight()>=this.minWeight_},t.getEstimate=function(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_},t.getEstimateTTFB=function(){return this.ttfb_.getTotalWeight()>=this.minWeight_?this.ttfb_.getEstimate():this.defaultTTFB_},t.destroy=function(){},i(e,[{key:"defaultEstimate",get:function(){return this.defaultEstimate_}}])}(),N=function(e,t){this.trace=void 0,this.debug=void 0,this.log=void 0,this.warn=void 0,this.info=void 0,this.error=void 0;var r="["+e+"]:";this.trace=B,this.debug=t.debug.bind(null,r),this.log=t.log.bind(null,r),this.warn=t.warn.bind(null,r),this.info=t.info.bind(null,r),this.error=t.error.bind(null,r)},B=function(){},U={trace:B,debug:B,log:B,warn:B,info:B,error:B};function G(){return n({},U)}function V(e,t,r){return t[e]?t[e].bind(t):function(e,t){var r=self.console[e];return r?r.bind(self.console,(t?"["+t+"] ":"")+"["+e+"] >"):B}(e,r)}var H=G();function K(e,t,r){var i=G();if("object"==typeof console&&!0===e||"object"==typeof e){var a=["debug","log","info","warn","error"];a.forEach((function(t){i[t]=V(t,e,r)}));try{i.log('Debug logs enabled for "'+t+'" in hls.js version 1.6.9')}catch(e){return G()}a.forEach((function(t){H[t]=V(t,e)}))}else n(H,i);return i}var W,Y,j=H,q=Y?W:(Y=1,W=void 0),X=v(q);function z(e){if(void 0===e&&(e=!0),"undefined"!=typeof self)return(e||!self.MediaSource)&&self.ManagedMediaSource||self.MediaSource||self.WebKitMediaSource}function Q(e,t){if(void 0===t&&(t=!1),"undefined"!=typeof TextDecoder){var r=new TextDecoder("utf-8").decode(e);if(t){var i=r.indexOf("\0");return-1!==i?r.substring(0,i):r}return r.replace(/\0/g,"")}for(var a,n,s,o=e.length,l="",u=0;u<o;){if(0===(a=e[u++])&&t)return l;if(0!==a&&3!==a)switch(a>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:l+=String.fromCharCode(a);break;case 12:case 13:n=e[u++],l+=String.fromCharCode((31&a)<<6|63&n);break;case 14:n=e[u++],s=e[u++],l+=String.fromCharCode((15&a)<<12|(63&n)<<6|(63&s)<<0)}}return l}var $=function(e){for(var t="",r=0;r<e.length;r++){var i=e[r].toString(16);i.length<2&&(i="0"+i),t+=i}return t};function Z(e){return Uint8Array.from(e.replace(/^0x/,"").replace(/([\da-fA-F]{2}) ?/g,"0x$1 ").replace(/ +$/,"").split(" ")).buffer}var J=function(){this.aborted=!1,this.loaded=0,this.retry=0,this.total=0,this.chunkCount=0,this.bwEstimate=0,this.loading={start:0,first:0,end:0},this.parsing={start:0,end:0},this.buffering={start:0,first:0,end:0}},ee="audio",te="video",re="audiovideo",ie=function(){function e(e){var t,r,i;this._byteRange=null,this._url=null,this._stats=null,this._streams=null,this.base=void 0,this.relurl=void 0,"string"==typeof e&&(e={url:e}),this.base=e,(i=oe(t=this,r="stats"))&&(i.enumerable=!0,Object.defineProperty(t,r,i))}var t=e.prototype;return t.setByteRange=function(e,t){var r,i=e.split("@",2);r=1===i.length?(null==t?void 0:t.byteRangeEndOffset)||0:parseInt(i[1]),this._byteRange=[r,parseInt(i[0])+r]},t.clearElementaryStreamInfo=function(){var e=this.elementaryStreams;e[ee]=null,e[te]=null,e[re]=null},i(e,[{key:"baseurl",get:function(){return this.base.url}},{key:"byteRange",get:function(){return null===this._byteRange?[]:this._byteRange}},{key:"byteRangeStartOffset",get:function(){return this.byteRange[0]}},{key:"byteRangeEndOffset",get:function(){return this.byteRange[1]}},{key:"elementaryStreams",get:function(){var e;return null===this._streams&&(this._streams=((e={})[ee]=null,e[te]=null,e[re]=null,e)),this._streams},set:function(e){this._streams=e}},{key:"hasStats",get:function(){return null!==this._stats}},{key:"hasStreams",get:function(){return null!==this._streams}},{key:"stats",get:function(){return null===this._stats&&(this._stats=new J),this._stats},set:function(e){this._stats=e}},{key:"url",get:function(){return!this._url&&this.baseurl&&this.relurl&&(this._url=S.buildAbsoluteURL(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url||""},set:function(e){this._url=e}}])}();function ae(e){return"initSegment"!==e.sn}var ne=function(e){function t(t,r){var i;return(i=e.call(this,r)||this)._decryptdata=null,i._programDateTime=null,i._ref=null,i._bitrate=void 0,i.rawProgramDateTime=null,i.tagList=[],i.duration=0,i.sn=0,i.levelkeys=void 0,i.type=void 0,i.loader=null,i.keyLoader=null,i.level=-1,i.cc=0,i.startPTS=void 0,i.endPTS=void 0,i.startDTS=void 0,i.endDTS=void 0,i.start=0,i.playlistOffset=0,i.deltaPTS=void 0,i.maxStartPTS=void 0,i.minEndPTS=void 0,i.data=void 0,i.bitrateTest=!1,i.title=null,i.initSegment=null,i.endList=void 0,i.gap=void 0,i.urlId=0,i.type=t,i}o(t,e);var r=t.prototype;return r.addStart=function(e){this.setStart(this.start+e)},r.setStart=function(e){this.start=e,this._ref&&(this._ref.start=e)},r.setDuration=function(e){this.duration=e,this._ref&&(this._ref.duration=e)},r.setKeyFormat=function(e){if(this.levelkeys){var t=this.levelkeys[e];t&&!this._decryptdata&&(this._decryptdata=t.getDecryptData(this.sn))}},r.abortRequests=function(){var e,t;null==(e=this.loader)||e.abort(),null==(t=this.keyLoader)||t.abort()},r.setElementaryStreamInfo=function(e,t,r,i,a,n){void 0===n&&(n=!1);var s=this.elementaryStreams,o=s[e];o?(o.startPTS=Math.min(o.startPTS,t),o.endPTS=Math.max(o.endPTS,r),o.startDTS=Math.min(o.startDTS,i),o.endDTS=Math.max(o.endDTS,a)):s[e]={startPTS:t,endPTS:r,startDTS:i,endDTS:a,partial:n}},i(t,[{key:"byteLength",get:function(){if(this.hasStats){var e=this.stats.total;if(e)return e}if(this.byteRange.length){var t=this.byteRange[0],r=this.byteRange[1];if(L(t)&&L(r))return r-t}return null}},{key:"bitrate",get:function(){return this.byteLength?8*this.byteLength/this.duration:this._bitrate?this._bitrate:null},set:function(e){this._bitrate=e}},{key:"decryptdata",get:function(){if(!this.levelkeys&&!this._decryptdata)return null;if(!this._decryptdata&&this.levelkeys&&!this.levelkeys.NONE){var e=this.levelkeys.identity;if(e)this._decryptdata=e.getDecryptData(this.sn);else{var t=Object.keys(this.levelkeys);if(1===t.length){var r=this._decryptdata=this.levelkeys[t[0]]||null;if(r)return r.getDecryptData(this.sn)}}}return this._decryptdata}},{key:"end",get:function(){return this.start+this.duration}},{key:"endProgramDateTime",get:function(){if(null===this.programDateTime)return null;var e=L(this.duration)?this.duration:0;return this.programDateTime+1e3*e}},{key:"encrypted",get:function(){var e;if(null!=(e=this._decryptdata)&&e.encrypted)return!0;if(this.levelkeys){var t,r=Object.keys(this.levelkeys),i=r.length;if(i>1||1===i&&null!=(t=this.levelkeys[r[0]])&&t.encrypted)return!0}return!1}},{key:"programDateTime",get:function(){return null===this._programDateTime&&this.rawProgramDateTime&&(this.programDateTime=Date.parse(this.rawProgramDateTime)),this._programDateTime},set:function(e){L(e)?this._programDateTime=e:this._programDateTime=this.rawProgramDateTime=null}},{key:"ref",get:function(){return ae(this)?(this._ref||(this._ref={base:this.base,start:this.start,duration:this.duration,sn:this.sn,programDateTime:this.programDateTime}),this._ref):null}}])}(ie),se=function(e){function t(t,r,i,a,n){var s;(s=e.call(this,i)||this).fragOffset=0,s.duration=0,s.gap=!1,s.independent=!1,s.relurl=void 0,s.fragment=void 0,s.index=void 0,s.duration=t.decimalFloatingPoint("DURATION"),s.gap=t.bool("GAP"),s.independent=t.bool("INDEPENDENT"),s.relurl=t.enumeratedString("URI"),s.fragment=r,s.index=a;var o=t.enumeratedString("BYTERANGE");return o&&s.setByteRange(o,n),n&&(s.fragOffset=n.fragOffset+n.duration),s}return o(t,e),i(t,[{key:"start",get:function(){return this.fragment.start+this.fragOffset}},{key:"end",get:function(){return this.start+this.duration}},{key:"loaded",get:function(){var e=this.elementaryStreams;return!!(e.audio||e.video||e.audiovideo)}}])}(ie);function oe(e,t){var r=Object.getPrototypeOf(e);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i||oe(r,t)}}var le=Math.pow(2,32)-1,ue=[].push,de={video:1,audio:2,id3:3,text:4};function he(e){return String.fromCharCode.apply(null,e)}function fe(e,t){var r=e[t]<<8|e[t+1];return r<0?65536+r:r}function ce(e,t){var r=ge(e,t);return r<0?4294967296+r:r}function ve(e,t){var r=ce(e,t);return r*=Math.pow(2,32),r+=ce(e,t+4)}function ge(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]}function me(e,t){var r=[];if(!t.length)return r;for(var i=e.byteLength,a=0;a<i;){var n=ce(e,a),s=n>1?a+n:i;if(he(e.subarray(a+4,a+8))===t[0])if(1===t.length)r.push(e.subarray(a+8,s));else{var o=me(e.subarray(a+8,s),t.slice(1));o.length&&ue.apply(r,o)}a=s}return r}function pe(e){var t=[],r=e[0],i=8,a=ce(e,i);i+=4;var n=0,s=0;0===r?(n=ce(e,i),s=ce(e,i+4),i+=8):(n=ve(e,i),s=ve(e,i+8),i+=16),i+=2;var o=e.length+s,l=fe(e,i);i+=2;for(var u=0;u<l;u++){var d=i,h=ce(e,d);d+=4;var f=2147483647&h;if(1==(2147483648&h)>>>31)return j.warn("SIDX has hierarchical references (not supported)"),null;var c=ce(e,d);d+=4,t.push({referenceSize:f,subsegmentDuration:c,info:{duration:c/a,start:o,end:o+f-1}}),o+=f,i=d+=4}return{earliestPresentationTime:n,timescale:a,version:r,referencesCount:l,references:t}}function ye(e){for(var t=[],r=me(e,["moov","trak"]),i=0;i<r.length;i++){var a=r[i],n=me(a,["tkhd"])[0];if(n){var s=n[0],o=ce(n,0===s?12:20),l=me(a,["mdia","mdhd"])[0];if(l){var u=ce(l,0===(s=l[0])?12:20),h=me(a,["mdia","hdlr"])[0];if(h){var f=he(h.subarray(8,12)),c={soun:ee,vide:te}[f],v=Ee(me(a,["mdia","minf","stbl","stsd"])[0]);c?(t[o]={timescale:u,type:c,stsd:v},t[c]=d({timescale:u,id:o},v)):t[o]={timescale:u,type:f,stsd:v}}}}}return me(e,["moov","mvex","trex"]).forEach((function(e){var r=ce(e,4),i=t[r];i&&(i.default={duration:ce(e,12),flags:ce(e,20)})})),t}function Ee(e){var t,r=e.subarray(8),i=r.subarray(86),a=he(r.subarray(4,8)),n=a,s="enca"===a||"encv"===a;if(s){var o=me(r,[a])[0];me(o.subarray("enca"===a?28:78),["sinf"]).forEach((function(e){var t=me(e,["schm"])[0];if(t){var r=he(t.subarray(4,8));if("cbcs"===r||"cenc"===r){var i=me(e,["frma"])[0];i&&(n=he(i))}}}))}var l=n;switch(n){case"avc1":case"avc2":case"avc3":case"avc4":var u=me(i,["avcC"])[0];u&&u.length>3&&(n+="."+Le(u[1])+Le(u[2])+Le(u[3]),t=Te("avc1"===l?"dva1":"dvav",i));break;case"mp4a":var d=me(r,[a])[0],h=me(d.subarray(28),["esds"])[0];if(h&&h.length>7){var f=4;if(3!==h[f++])break;f=Se(h,f),f+=2;var c=h[f++];if(128&c&&(f+=2),64&c&&(f+=h[f++]),4!==h[f++])break;f=Se(h,f);var v=h[f++];if(64!==v)break;if(n+="."+Le(v),f+=12,5!==h[f++])break;f=Se(h,f);var g=h[f++],m=(248&g)>>3;31===m&&(m+=1+((7&g)<<3)+((224&h[f])>>5)),n+="."+m}break;case"hvc1":case"hev1":var p=me(i,["hvcC"])[0];if(p&&p.length>12){var y=p[1],E=["","A","B","C"][y>>6],T=31&y,S=ce(p,2),L=(32&y)>>5?"H":"L",R=p[12],A=p.subarray(6,12);n+="."+E+T,n+="."+function(e){for(var t=0,r=0;r<32;r++)t|=(e>>r&1)<<31-r;return t>>>0}(S).toString(16).toUpperCase(),n+="."+L+R;for(var b="",k=A.length;k--;){var D=A[k];(D||b)&&(b="."+D.toString(16).toUpperCase()+b)}n+=b}t=Te("hev1"==l?"dvhe":"dvh1",i);break;case"dvh1":case"dvhe":case"dvav":case"dva1":case"dav1":n=Te(n,i)||n;break;case"vp09":var _=me(i,["vpcC"])[0];if(_&&_.length>6){var I=_[4],C=_[5],P=_[6]>>4&15;n+="."+Re(I)+"."+Re(C)+"."+Re(P)}break;case"av01":var x=me(i,["av1C"])[0];if(x&&x.length>2){var w=x[1]>>>5,O=31&x[1],F=x[2]>>>7?"H":"M",M=(64&x[2])>>6,N=(32&x[2])>>5,B=2===w&&M?N?12:10:M?10:8,U=(16&x[2])>>4,G=(8&x[2])>>3,V=(4&x[2])>>2,H=3&x[2];n+="."+w+"."+Re(O)+F+"."+Re(B)+"."+U+"."+G+V+H+"."+Re(1)+"."+Re(1)+"."+Re(1)+".0",t=Te("dav1",i)}}return{codec:n,encrypted:s,supplemental:t}}function Te(e,t){var r=me(t,["dvvC"]),i=r.length?r[0]:me(t,["dvcC"])[0];if(i){var a=i[2]>>1&127,n=i[2]<<5&32|i[3]>>3&31;return e+"."+Re(a)+"."+Re(n)}}function Se(e,t){for(var r=t+5;128&e[t++]&&t<r;);return t}function Le(e){return("0"+e.toString(16).toUpperCase()).slice(-2)}function Re(e){return(e<10?"0":"")+e}function Ae(e,t){if(e&&t){var r=t.keyId;r&&t.isCommonEncryption&&me(e,["moov","trak"]).forEach((function(e){var t=me(e,["mdia","minf","stbl","stsd"])[0].subarray(8),i=me(t,["enca"]),a=i.length>0;a||(i=me(t,["encv"])),i.forEach((function(e){me(a?e.subarray(28):e.subarray(78),["sinf"]).forEach((function(e){var t=function(e){var t=me(e,["schm"])[0];if(t){var r=he(t.subarray(4,8));if("cbcs"===r||"cenc"===r)return me(e,["schi","tenc"])[0]}return null}(e);if(t){var i=t.subarray(8,24);i.some((function(e){return 0!==e}))||(j.log("[eme] Patching keyId in 'enc"+(a?"a":"v")+">sinf>>tenc' box: "+$(i)+" -> "+$(r)),t.set(r,8))}}))}))}))}}function be(e,t){var r=new Uint8Array(e.length+t.length);return r.set(e),r.set(t,e.length),r}function ke(e,t){var r=[],i=t.samples,a=t.timescale,n=t.id,s=!1;return me(i,["moof"]).map((function(o){var l=o.byteOffset-8;me(o,["traf"]).map((function(o){var u=me(o,["tfdt"]).map((function(e){var t=e[0],r=ce(e,4);return 1===t&&(r*=Math.pow(2,32),r+=ce(e,8)),r/a}))[0];return void 0!==u&&(e=u),me(o,["tfhd"]).map((function(u){var d=ce(u,4),h=16777215&ce(u,0),f=0,c=0!=(16&h),v=0,g=0!=(32&h),m=8;d===n&&(0!=(1&h)&&(m+=8),0!=(2&h)&&(m+=4),0!=(8&h)&&(f=ce(u,m),m+=4),c&&(v=ce(u,m),m+=4),g&&(m+=4),"video"===t.type&&(s=De(t.codec)),me(o,["trun"]).map((function(n){var o=n[0],u=16777215&ce(n,0),d=0!=(1&u),h=0,c=0!=(4&u),g=0!=(256&u),m=0,p=0!=(512&u),y=0,E=0!=(1024&u),T=0!=(2048&u),S=0,L=ce(n,4),R=8;d&&(h=ce(n,R),R+=4),c&&(R+=4);for(var A=h+l,b=0;b<L;b++){if(g?(m=ce(n,R),R+=4):m=f,p?(y=ce(n,R),R+=4):y=v,E&&(R+=4),T&&(S=0===o?ce(n,R):ge(n,R),R+=4),t.type===te)for(var k=0;k<y;){var D=ce(i,A);_e(s,i[A+=4])&&Ie(i.subarray(A,A+D),s?2:1,e+S/a,r),A+=D,k+=D+4}e+=m/a}})))}))}))})),r}function De(e){if(!e)return!1;var t=e.substring(0,4);return"hvc1"===t||"hev1"===t||"dvh1"===t||"dvhe"===t}function _e(e,t){if(e){var r=t>>1&63;return 39===r||40===r}return 6==(31&t)}function Ie(e,t,r,i){var a=Ce(e),n=0;n+=t;for(var s=0,o=0,l=0;n<a.length;){s=0;do{if(n>=a.length)break;s+=l=a[n++]}while(255===l);o=0;do{if(n>=a.length)break;o+=l=a[n++]}while(255===l);var u=a.length-n,d=n;if(o<u)n+=o;else if(o>u){j.error("Malformed SEI payload. "+o+" is too small, only "+u+" bytes left to parse.");break}if(4===s){if(181===a[d++]){var h=fe(a,d);if(d+=2,49===h){var f=ce(a,d);if(d+=4,1195456820===f){var c=a[d++];if(3===c){var v=a[d++],g=64&v,m=g?2+3*(31&v):0,p=new Uint8Array(m);if(g){p[0]=v;for(var y=1;y<m;y++)p[y]=a[d++]}i.push({type:c,payloadType:s,pts:r,bytes:p})}}}}}else if(5===s&&o>16){for(var E=[],T=0;T<16;T++){var S=a[d++].toString(16);E.push(1==S.length?"0"+S:S),3!==T&&5!==T&&7!==T&&9!==T||E.push("-")}for(var L=o-16,R=new Uint8Array(L),A=0;A<L;A++)R[A]=a[d++];i.push({payloadType:s,pts:r,uuid:E.join(""),userData:Q(R),userDataBytes:R})}}}function Ce(e){for(var t=e.byteLength,r=[],i=1;i<t-2;)0===e[i]&&0===e[i+1]&&3===e[i+2]?(r.push(i+2),i+=2):i++;if(0===r.length)return e;var a=t-r.length,n=new Uint8Array(a),s=0;for(i=0;i<a;s++,i++)s===r[0]&&(s++,r.shift()),n[i]=e[s];return n}var Pe=function(){return/\(Windows.+Firefox\//i.test(navigator.userAgent)},xe={audio:{a3ds:1,"ac-3":.95,"ac-4":1,alac:.9,alaw:1,dra1:1,"dts+":1,"dts-":1,dtsc:1,dtse:1,dtsh:1,"ec-3":.9,enca:1,fLaC:.9,flac:.9,FLAC:.9,g719:1,g726:1,m4ae:1,mha1:1,mha2:1,mhm1:1,mhm2:1,mlpa:1,mp4a:1,"raw ":1,Opus:1,opus:1,samr:1,sawb:1,sawp:1,sevc:1,sqcp:1,ssmv:1,twos:1,ulaw:1},video:{avc1:1,avc2:1,avc3:1,avc4:1,avcp:1,av01:.8,dav1:.8,drac:1,dva1:1,dvav:1,dvh1:.7,dvhe:.7,encv:1,hev1:.75,hvc1:.75,mjp2:1,mp4v:1,mvc1:1,mvc2:1,mvc3:1,mvc4:1,resv:1,rv60:1,s263:1,svc1:1,svc2:1,"vc-1":1,vp08:1,vp09:.9},text:{stpp:1,wvtt:1}};function we(e,t){var r=xe[t];return!!r&&!!r[e.slice(0,4)]}function Oe(e,t,r){return void 0===r&&(r=!0),!e.split(",").some((function(e){return!Fe(e,t,r)}))}function Fe(e,t,r){var i;void 0===r&&(r=!0);var a=z(r);return null!=(i=null==a?void 0:a.isTypeSupported(Me(e,t)))&&i}function Me(e,t){return t+"/mp4;codecs="+e}function Ne(e){if(e){var t=e.substring(0,4);return xe.video[t]}return 2}function Be(e){var t=Pe();return e.split(",").reduce((function(e,r){var i=t&&De(r)?9:xe.video[r];return i?(2*i+e)/(e?3:2):(xe.audio[r]+e)/(e?2:1)}),0)}var Ue={},Ge=/flac|opus|mp4a\.40\.34/i;function Ve(e,t){return void 0===t&&(t=!0),e.replace(Ge,(function(e){return function(e,t){if(void 0===t&&(t=!0),Ue[e])return Ue[e];for(var r={flac:["flac","fLaC","FLAC"],opus:["opus","Opus"],"mp4a.40.34":["mp3"]}[e],i=0;i<r.length;i++){var a;if(Fe(r[i],"audio",t))return Ue[e]=r[i],r[i];if("mp3"===r[i]&&null!=(a=z(t))&&a.isTypeSupported("audio/mpeg"))return""}return e}(e.toLowerCase(),t)}))}function He(e,t){if(e&&(e.length>4||-1!==["ac-3","ec-3","alac","fLaC","Opus"].indexOf(e))&&(Ke(e,"audio")||Ke(e,"video")))return e;if(t){var r=t.split(",");if(r.length>1){if(e)for(var i=r.length;i--;)if(r[i].substring(0,4)===e.substring(0,4))return r[i];return r[0]}}return t||e}function Ke(e,t){return we(e,t)&&Fe(e,t)}function We(e){if(e.startsWith("av01.")){for(var t=e.split("."),r=["0","111","01","01","01","0"],i=t.length;i>4&&i<10;i++)t[i]=r[i-4];return t.join(".")}return e}function Ye(e){var t=z(e)||{isTypeSupported:function(){return!1}};return{mpeg:t.isTypeSupported("audio/mpeg"),mp3:t.isTypeSupported('audio/mp4; codecs="mp3"'),ac3:!1}}function je(e){return e.replace(/^.+codecs=["']?([^"']+).*$/,"$1")}var qe=["NONE","TYPE-0","TYPE-1",null],Xe=["SDR","PQ","HLG"],ze="",Qe="YES",$e="v2";function Ze(e){var t=e.canSkipUntil,r=e.canSkipDateRanges,i=e.age;return t&&i<t/2?r?$e:Qe:ze}var Je=function(){function e(e,t,r){this.msn=void 0,this.part=void 0,this.skip=void 0,this.msn=e,this.part=t,this.skip=r}return e.prototype.addDirectives=function(e){var t=new self.URL(e);return void 0!==this.msn&&t.searchParams.set("_HLS_msn",this.msn.toString()),void 0!==this.part&&t.searchParams.set("_HLS_part",this.part.toString()),this.skip&&t.searchParams.set("_HLS_skip",this.skip),t.href},e}(),et=function(){function e(e){if(this._attrs=void 0,this.audioCodec=void 0,this.bitrate=void 0,this.codecSet=void 0,this.url=void 0,this.frameRate=void 0,this.height=void 0,this.id=void 0,this.name=void 0,this.supplemental=void 0,this.videoCodec=void 0,this.width=void 0,this.details=void 0,this.fragmentError=0,this.loadError=0,this.loaded=void 0,this.realBitrate=0,this.supportedPromise=void 0,this.supportedResult=void 0,this._avgBitrate=0,this._audioGroups=void 0,this._subtitleGroups=void 0,this._urlId=0,this.url=[e.url],this._attrs=[e.attrs],this.bitrate=e.bitrate,e.details&&(this.details=e.details),this.id=e.id||0,this.name=e.name,this.width=e.width||0,this.height=e.height||0,this.frameRate=e.attrs.optionalFloat("FRAME-RATE",0),this._avgBitrate=e.attrs.decimalInteger("AVERAGE-BANDWIDTH"),this.audioCodec=e.audioCodec,this.videoCodec=e.videoCodec,this.codecSet=[e.videoCodec,e.audioCodec].filter((function(e){return!!e})).map((function(e){return e.substring(0,4)})).join(","),"supplemental"in e){var t;this.supplemental=e.supplemental;var r=null==(t=e.supplemental)?void 0:t.videoCodec;r&&r!==e.videoCodec&&(this.codecSet+=","+r.substring(0,4))}this.addGroupId("audio",e.attrs.AUDIO),this.addGroupId("text",e.attrs.SUBTITLES)}var t=e.prototype;return t.hasAudioGroup=function(e){return tt(this._audioGroups,e)},t.hasSubtitleGroup=function(e){return tt(this._subtitleGroups,e)},t.addGroupId=function(e,t){if(t)if("audio"===e){var r=this._audioGroups;r||(r=this._audioGroups=[]),-1===r.indexOf(t)&&r.push(t)}else if("text"===e){var i=this._subtitleGroups;i||(i=this._subtitleGroups=[]),-1===i.indexOf(t)&&i.push(t)}},t.addFallback=function(){},i(e,[{key:"maxBitrate",get:function(){return Math.max(this.realBitrate,this.bitrate)}},{key:"averageBitrate",get:function(){return this._avgBitrate||this.realBitrate||this.bitrate}},{key:"attrs",get:function(){return this._attrs[0]}},{key:"codecs",get:function(){return this.attrs.CODECS||""}},{key:"pathwayId",get:function(){return this.attrs["PATHWAY-ID"]||"."}},{key:"videoRange",get:function(){return this.attrs["VIDEO-RANGE"]||"SDR"}},{key:"score",get:function(){return this.attrs.optionalFloat("SCORE",0)}},{key:"uri",get:function(){return this.url[0]||""}},{key:"audioGroups",get:function(){return this._audioGroups}},{key:"subtitleGroups",get:function(){return this._subtitleGroups}},{key:"urlId",get:function(){return 0},set:function(e){}},{key:"audioGroupIds",get:function(){return this.audioGroups?[this.audioGroupId]:void 0}},{key:"textGroupIds",get:function(){return this.subtitleGroups?[this.textGroupId]:void 0}},{key:"audioGroupId",get:function(){var e;return null==(e=this.audioGroups)?void 0:e[0]}},{key:"textGroupId",get:function(){var e;return null==(e=this.subtitleGroups)?void 0:e[0]}}])}();function tt(e,t){return!(!t||!e)&&-1!==e.indexOf(t)}function rt(e,t){var r=!1,i=[];if(e&&(r="SDR"!==e,i=[e]),t){var a="SDR"!==(i=t.allowedVideoRanges||Xe.slice(0)).join("")&&!t.videoCodec;(r=void 0!==t.preferHDR?t.preferHDR:a&&function(){if("function"==typeof matchMedia){var e=matchMedia("(dynamic-range: high)"),t=matchMedia("bad query");if(e.media!==t.media)return!0===e.matches}return!1}())||(i=["SDR"])}return{preferHDR:r,allowedVideoRanges:i}}var it=function(e,t){return JSON.stringify(e,function(e){var t=new WeakSet;return function(r,i){if(e&&(i=e(r,i)),"object"==typeof i&&null!==i){if(t.has(i))return;t.add(i)}return i}}(t))};function at(e,t){j.log('[abr] start candidates with "'+e+'" ignored because '+t)}function nt(e){return e.reduce((function(e,t){var r=e.groups[t.groupId];r||(r=e.groups[t.groupId]={tracks:[],channels:{2:0},hasDefault:!1,hasAutoSelect:!1}),r.tracks.push(t);var i=t.channels||"2";return r.channels[i]=(r.channels[i]||0)+1,r.hasDefault=r.hasDefault||t.default,r.hasAutoSelect=r.hasAutoSelect||t.autoselect,r.hasDefault&&(e.hasDefaultAudio=!0),r.hasAutoSelect&&(e.hasAutoSelectAudio=!0),e}),{hasDefaultAudio:!1,hasAutoSelectAudio:!1,groups:{}})}function st(e,t){var r;return!!e&&e!==(null==(r=t.loadLevelObj)?void 0:r.uri)}var ot=function(e){function t(t){var r;return(r=e.call(this,"abr",t.logger)||this).hls=void 0,r.lastLevelLoadSec=0,r.lastLoadedFragLevel=-1,r.firstSelection=-1,r._nextAutoLevel=-1,r.nextAutoLevelKey="",r.audioTracksByGroup=null,r.codecTiers=null,r.timer=-1,r.fragCurrent=null,r.partCurrent=null,r.bitrateTestDelay=0,r.rebufferNotice=-1,r.supportedCache={},r.bwEstimator=void 0,r._abandonRulesCheck=function(e){var t,i=r,a=i.fragCurrent,n=i.partCurrent,s=i.hls,o=s.autoLevelEnabled,l=s.media;if(a&&l){var u=performance.now(),d=n?n.stats:a.stats,h=n?n.duration:a.duration,f=u-d.loading.start,c=s.minAutoLevel,v=a.level,g=r._nextAutoLevel;if(d.aborted||d.loaded&&d.loaded===d.total||v<=c)return r.clearTimer(),void(r._nextAutoLevel=-1);if(o){var m=g>-1&&g!==v,p=!!e||m;if(p||!l.paused&&l.playbackRate&&l.readyState){var y=s.mainForwardBufferInfo;if(p||null!==y){var E=r.bwEstimator.getEstimateTTFB(),T=Math.abs(l.playbackRate);if(!(f<=Math.max(E,h/(2*T)*1e3))){var S=y?y.len/T:0,R=d.loading.first?d.loading.first-d.loading.start:-1,A=d.loaded&&R>-1,b=r.getBwEstimate(),k=s.levels,_=k[v],I=Math.max(d.loaded,Math.round(h*(a.bitrate||_.averageBitrate)/8)),C=A?f-R:f;C<1&&A&&(C=Math.min(f,8*d.loaded/b));var P=A?1e3*d.loaded/C:0,x=E/1e3,w=P?(I-d.loaded)/P:8*I/b+x;if(!(w<=S)){var O,F=P?8*P:b,M=!0===(null==(t=(null==e?void 0:e.details)||r.hls.latestLevelDetails)?void 0:t.live),N=r.hls.config.abrBandWidthUpFactor,B=Number.POSITIVE_INFINITY;for(O=v-1;O>c;O--){var U=k[O].maxBitrate,G=!k[O].details||M;if((B=r.getTimeToLoadFrag(x,F,h*U,G))<Math.min(S,h+x))break}if(!(B>=w||B>10*h)){A?r.bwEstimator.sample(f-Math.min(E,R),d.loaded):r.bwEstimator.sampleTTFB(f);var V=k[O].maxBitrate;r.getBwEstimate()*N>V&&r.resetEstimator(V);var H=r.findBestLevel(V,c,O,0,S,1,1);H>-1&&(O=H),r.warn("Fragment "+a.sn+(n?" part "+n.index:"")+" of level "+v+" is loading too slowly;\n      Fragment duration: "+a.duration.toFixed(3)+"\n      Time to underbuffer: "+S.toFixed(3)+" s\n      Estimated load time for current fragment: "+w.toFixed(3)+" s\n      Estimated load time for down switch fragment: "+B.toFixed(3)+" s\n      TTFB estimate: "+(0|R)+" ms\n      Current BW estimate: "+(L(b)?0|b:"Unknown")+" bps\n      New BW estimate: "+(0|r.getBwEstimate())+" bps\n      Switching to level "+O+" @ "+(0|V)+" bps"),s.nextLoadLevel=s.nextAutoLevel=O,r.clearTimer();var K=function(){if(r.clearTimer(),r.fragCurrent===a&&r.hls.loadLevel===O&&O>0){var e=r.getStarvationDelay();if(r.warn("Aborting inflight request "+(O>0?"and switching down":"")+"\n      Fragment duration: "+a.duration.toFixed(3)+" s\n      Time to underbuffer: "+e.toFixed(3)+" s"),a.abortRequests(),r.fragCurrent=r.partCurrent=null,O>c){var t=r.findBestLevel(r.hls.levels[c].bitrate,c,O,0,e,1,1);-1===t&&(t=c),r.hls.nextLoadLevel=r.hls.nextAutoLevel=t,r.resetEstimator(r.hls.levels[t].bitrate)}}};m||w>2*B?K():r.timer=self.setInterval(K,1e3*B),s.trigger(D.FRAG_LOAD_EMERGENCY_ABORTED,{frag:a,part:n,stats:d})}}}}}}}},r.hls=t,r.bwEstimator=r.initEstimator(),r.registerListeners(),r}o(t,e);var r=t.prototype;return r.resetEstimator=function(e){e&&(this.log("setting initial bwe to "+e),this.hls.config.abrEwmaDefaultEstimate=e),this.firstSelection=-1,this.bwEstimator=this.initEstimator()},r.initEstimator=function(){var e=this.hls.config;return new M(e.abrEwmaSlowVoD,e.abrEwmaFastVoD,e.abrEwmaDefaultEstimate)},r.registerListeners=function(){var e=this.hls;e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.FRAG_LOADING,this.onFragLoading,this),e.on(D.FRAG_LOADED,this.onFragLoaded,this),e.on(D.FRAG_BUFFERED,this.onFragBuffered,this),e.on(D.LEVEL_SWITCHING,this.onLevelSwitching,this),e.on(D.LEVEL_LOADED,this.onLevelLoaded,this),e.on(D.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(D.MAX_AUTO_LEVEL_UPDATED,this.onMaxAutoLevelUpdated,this),e.on(D.ERROR,this.onError,this)},r.unregisterListeners=function(){var e=this.hls;e&&(e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.FRAG_LOADING,this.onFragLoading,this),e.off(D.FRAG_LOADED,this.onFragLoaded,this),e.off(D.FRAG_BUFFERED,this.onFragBuffered,this),e.off(D.LEVEL_SWITCHING,this.onLevelSwitching,this),e.off(D.LEVEL_LOADED,this.onLevelLoaded,this),e.off(D.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(D.MAX_AUTO_LEVEL_UPDATED,this.onMaxAutoLevelUpdated,this),e.off(D.ERROR,this.onError,this))},r.destroy=function(){this.unregisterListeners(),this.clearTimer(),this.hls=this._abandonRulesCheck=this.supportedCache=null,this.fragCurrent=this.partCurrent=null},r.onManifestLoading=function(e,t){this.lastLoadedFragLevel=-1,this.firstSelection=-1,this.lastLevelLoadSec=0,this.supportedCache={},this.fragCurrent=this.partCurrent=null,this.onLevelsUpdated(),this.clearTimer()},r.onLevelsUpdated=function(){this.lastLoadedFragLevel>-1&&this.fragCurrent&&(this.lastLoadedFragLevel=this.fragCurrent.level),this._nextAutoLevel=-1,this.onMaxAutoLevelUpdated(),this.codecTiers=null,this.audioTracksByGroup=null},r.onMaxAutoLevelUpdated=function(){this.firstSelection=-1,this.nextAutoLevelKey=""},r.onFragLoading=function(e,t){var r,i=t.frag;this.ignoreFragment(i)||(i.bitrateTest||(this.fragCurrent=i,this.partCurrent=null!=(r=t.part)?r:null),this.clearTimer(),this.timer=self.setInterval(this._abandonRulesCheck,100))},r.onLevelSwitching=function(e,t){this.clearTimer()},r.onError=function(e,t){if(!t.fatal)switch(t.details){case k.BUFFER_ADD_CODEC_ERROR:case k.BUFFER_APPEND_ERROR:this.lastLoadedFragLevel=-1,this.firstSelection=-1;break;case k.FRAG_LOAD_TIMEOUT:var r=t.frag,i=this.fragCurrent,a=this.partCurrent;if(r&&i&&r.sn===i.sn&&r.level===i.level){var n=performance.now(),s=a?a.stats:r.stats,o=n-s.loading.start,l=s.loading.first?s.loading.first-s.loading.start:-1;if(s.loaded&&l>-1){var u=this.bwEstimator.getEstimateTTFB();this.bwEstimator.sample(o-Math.min(u,l),s.loaded)}else this.bwEstimator.sampleTTFB(o)}}},r.getTimeToLoadFrag=function(e,t,r,i){return e+r/t+(i?e+this.lastLevelLoadSec:0)},r.onLevelLoaded=function(e,t){var r=this.hls.config,i=t.stats.loading,a=i.end-i.first;L(a)&&(this.lastLevelLoadSec=a/1e3),t.details.live?this.bwEstimator.update(r.abrEwmaSlowLive,r.abrEwmaFastLive):this.bwEstimator.update(r.abrEwmaSlowVoD,r.abrEwmaFastVoD),this.timer>-1&&this._abandonRulesCheck(t.levelInfo)},r.onFragLoaded=function(e,t){var r=t.frag,i=t.part,a=i?i.stats:r.stats;if(r.type===x&&this.bwEstimator.sampleTTFB(a.loading.first-a.loading.start),!this.ignoreFragment(r)){if(this.clearTimer(),r.level===this._nextAutoLevel&&(this._nextAutoLevel=-1),this.firstSelection=-1,this.hls.config.abrMaxWithRealBitrate){var n=i?i.duration:r.duration,s=this.hls.levels[r.level],o=(s.loaded?s.loaded.bytes:0)+a.loaded,l=(s.loaded?s.loaded.duration:0)+n;s.loaded={bytes:o,duration:l},s.realBitrate=Math.round(8*o/l)}if(r.bitrateTest){var u={stats:a,frag:r,part:i,id:r.type};this.onFragBuffered(D.FRAG_BUFFERED,u),r.bitrateTest=!1}else this.lastLoadedFragLevel=r.level}},r.onFragBuffered=function(e,t){var r=t.frag,i=t.part,a=null!=i&&i.stats.loaded?i.stats:r.stats;if(!a.aborted&&!this.ignoreFragment(r)){var n=a.parsing.end-a.loading.start-Math.min(a.loading.first-a.loading.start,this.bwEstimator.getEstimateTTFB());this.bwEstimator.sample(n,a.loaded),a.bwEstimate=this.getBwEstimate(),r.bitrateTest?this.bitrateTestDelay=n/1e3:this.bitrateTestDelay=0}},r.ignoreFragment=function(e){return e.type!==x||"initSegment"===e.sn},r.clearTimer=function(){this.timer>-1&&(self.clearInterval(this.timer),this.timer=-1)},r.getAutoLevelKey=function(){return this.getBwEstimate()+"_"+this.getStarvationDelay().toFixed(2)},r.getNextABRAutoLevel=function(){var e=this.fragCurrent,t=this.partCurrent,r=this.hls;if(r.levels.length<=1)return r.loadLevel;var i=r.maxAutoLevel,a=r.config,n=r.minAutoLevel,s=t?t.duration:e?e.duration:0,o=this.getBwEstimate(),l=this.getStarvationDelay(),u=a.abrBandWidthFactor,d=a.abrBandWidthUpFactor;if(l){var h=this.findBestLevel(o,n,i,l,0,u,d);if(h>=0)return this.rebufferNotice=-1,h}var f=s?Math.min(s,a.maxStarvationDelay):a.maxStarvationDelay;if(!l){var c=this.bitrateTestDelay;c&&(f=(s?Math.min(s,a.maxLoadingDelay):a.maxLoadingDelay)-c,this.info("bitrate test took "+Math.round(1e3*c)+"ms, set first fragment max fetchDuration to "+Math.round(1e3*f)+" ms"),u=d=1)}var v=this.findBestLevel(o,n,i,l,f,u,d);if(this.rebufferNotice!==v&&(this.rebufferNotice=v,this.info((l?"rebuffering expected":"buffer is empty")+", optimal quality level "+v)),v>-1)return v;var g=r.levels[n],m=r.loadLevelObj;return m&&(null==g?void 0:g.bitrate)<m.bitrate?n:r.loadLevel},r.getStarvationDelay=function(){var e=this.hls,t=e.media;if(!t)return 1/0;var r=t&&0!==t.playbackRate?Math.abs(t.playbackRate):1,i=e.mainForwardBufferInfo;return(i?i.len:0)/r},r.getBwEstimate=function(){return this.bwEstimator.canEstimate()?this.bwEstimator.getEstimate():this.hls.config.abrEwmaDefaultEstimate},r.findBestLevel=function(e,t,r,i,a,n,s){var o,l=this,u=i+a,d=this.lastLoadedFragLevel,h=-1===d?this.hls.firstLevel:d,f=this.fragCurrent,c=this.partCurrent,v=this.hls,g=v.levels,m=v.allAudioTracks,p=v.loadLevel,y=v.config;if(1===g.length)return 0;var E,T=g[h],S=!(null==(o=this.hls.latestLevelDetails)||!o.live),R=-1===p||-1===d,A="SDR",b=(null==T?void 0:T.frameRate)||0,k=y.audioPreference,D=y.videoPreference,_=(this.audioTracksByGroup||(this.audioTracksByGroup=nt(m)),-1);if(R){if(-1!==this.firstSelection)return this.firstSelection;var I=this.codecTiers||(this.codecTiers=function(e,t,r,i){return e.slice(r,i+1).reduce((function(e,t,r){if(!t.codecSet)return e;var i=t.audioGroups,a=e[t.codecSet];a||(e[t.codecSet]=a={minBitrate:1/0,minHeight:1/0,minFramerate:1/0,minIndex:r,maxScore:0,videoRanges:{SDR:0},channels:{2:0},hasDefaultAudio:!i,fragmentError:0}),a.minBitrate=Math.min(a.minBitrate,t.bitrate);var n=Math.min(t.height,t.width);return a.minHeight=Math.min(a.minHeight,n),a.minFramerate=Math.min(a.minFramerate,t.frameRate),a.minIndex=Math.min(a.minIndex,r),a.maxScore=Math.max(a.maxScore,t.score),a.fragmentError+=t.fragmentError,a.videoRanges[t.videoRange]=(a.videoRanges[t.videoRange]||0)+1,e}),{})}(g,0,t,r)),C=function(e,t,r,i,a){for(var n=Object.keys(e),s=null==i?void 0:i.channels,o=null==i?void 0:i.audioCodec,l=null==a?void 0:a.videoCodec,u=s&&2===parseInt(s),d=!1,h=!1,f=1/0,c=1/0,v=1/0,g=1/0,m=0,p=[],y=rt(t,a),E=y.preferHDR,T=y.allowedVideoRanges,S=function(){var t=e[n[R]];d||(d=t.channels[2]>0),f=Math.min(f,t.minHeight),c=Math.min(c,t.minFramerate),v=Math.min(v,t.minBitrate),T.filter((function(e){return t.videoRanges[e]>0})).length>0&&(h=!0)},R=n.length;R--;)S();f=L(f)?f:0,c=L(c)?c:0;var A=Math.max(1080,f),b=Math.max(30,c);v=L(v)?v:r,r=Math.max(v,r),h||(t=void 0);var k=n.length>1;return{codecSet:n.reduce((function(t,i){var a=e[i];if(i===t)return t;if(p=h?T.filter((function(e){return a.videoRanges[e]>0})):[],k){if(a.minBitrate>r)return at(i,"min bitrate of "+a.minBitrate+" > current estimate of "+r),t;if(!a.hasDefaultAudio)return at(i,"no renditions with default or auto-select sound found"),t;if(o&&i.indexOf(o.substring(0,4))%5!=0)return at(i,'audio codec preference "'+o+'" not found'),t;if(s&&!u){if(!a.channels[s])return at(i,"no renditions with "+s+" channel sound found (channels options: "+Object.keys(a.channels)+")"),t}else if((!o||u)&&d&&0===a.channels[2])return at(i,"no renditions with stereo sound found"),t;if(a.minHeight>A)return at(i,"min resolution of "+a.minHeight+" > maximum of "+A),t;if(a.minFramerate>b)return at(i,"min framerate of "+a.minFramerate+" > maximum of "+b),t;if(!p.some((function(e){return a.videoRanges[e]>0})))return at(i,"no variants with VIDEO-RANGE of "+it(p)+" found"),t;if(l&&i.indexOf(l.substring(0,4))%5!=0)return at(i,'video codec preference "'+l+'" not found'),t;if(a.maxScore<m)return at(i,"max score of "+a.maxScore+" < selected max of "+m),t}return t&&(Be(i)>=Be(t)||a.fragmentError>e[t].fragmentError)?t:(g=a.minIndex,m=a.maxScore,i)}),void 0),videoRanges:p,preferHDR:E,minFramerate:c,minBitrate:v,minIndex:g}}(I,A,e,k,D),P=C.codecSet,x=C.videoRanges,w=C.minFramerate,O=C.minBitrate,F=C.minIndex,M=C.preferHDR;_=F,E=P,A=M?x[x.length-1]:x[0],b=w,e=Math.max(e,O),this.log("picked start tier "+it(C))}else E=null==T?void 0:T.codecSet,A=null==T?void 0:T.videoRange;for(var N,B=c?c.duration:f?f.duration:0,U=this.bwEstimator.getEstimateTTFB()/1e3,G=[],V=function(){var t,o=g[H],f=H>h;if(!o)return 0;if((E&&o.codecSet!==E||A&&o.videoRange!==A||f&&b>o.frameRate||!f&&b>0&&b<o.frameRate||null!=(t=o.supportedResult)&&null!=(t=t.decodingInfoResults)&&t.some((function(e){return!1===e.smooth})))&&(!R||H!==_))return G.push(H),0;var v,m=o.details,y=(c?null==m?void 0:m.partTarget:null==m?void 0:m.averagetargetduration)||B;v=f?s*e:n*e;var T=B&&i>=2*B&&0===a?o.averageBitrate:o.maxBitrate,k=l.getTimeToLoadFrag(U,v,T*y,void 0===m);if(v>=T&&(H===d||0===o.loadError&&0===o.fragmentError)&&(k<=U||!L(k)||S&&!l.bitrateTestDelay||k<u)){var D=l.forcedAutoLevel;return H===p||-1!==D&&D===p||(G.length&&l.trace("Skipped level(s) "+G.join(",")+" of "+r+' max with CODECS and VIDEO-RANGE:"'+g[G[0]].codecs+'" '+g[G[0]].videoRange+'; not compatible with "'+E+'" '+A),l.info("switch candidate:"+h+"->"+H+" adjustedbw("+Math.round(v)+")-bitrate="+Math.round(v-T)+" ttfb:"+U.toFixed(1)+" avgDuration:"+y.toFixed(1)+" maxFetchDuration:"+u.toFixed(1)+" fetchDuration:"+k.toFixed(1)+" firstSelection:"+R+" codecSet:"+o.codecSet+" videoRange:"+o.videoRange+" hls.loadLevel:"+p)),R&&(l.firstSelection=H),{v:H}}},H=r;H>=t;H--)if(0!==(N=V())&&N)return N.v;return-1},r.deriveNextAutoLevel=function(e){var t=this.hls,r=t.maxAutoLevel,i=t.minAutoLevel;return Math.min(Math.max(e,i),r)},i(t,[{key:"firstAutoLevel",get:function(){var e=this.hls,t=e.maxAutoLevel,r=e.minAutoLevel,i=this.getBwEstimate(),a=this.hls.config.maxStarvationDelay,n=this.findBestLevel(i,r,t,0,a,1,1);if(n>-1)return n;var s=this.hls.firstLevel,o=Math.min(Math.max(s,r),t);return this.warn("Could not find best starting auto level. Defaulting to first in playlist "+s+" clamped to "+o),o}},{key:"forcedAutoLevel",get:function(){return this.nextAutoLevelKey?-1:this._nextAutoLevel}},{key:"nextAutoLevel",get:function(){var e=this.forcedAutoLevel,t=this.bwEstimator.canEstimate(),r=this.lastLoadedFragLevel>-1;if(!(-1===e||t&&r&&this.nextAutoLevelKey!==this.getAutoLevelKey()))return e;var i=t&&r?this.getNextABRAutoLevel():this.firstAutoLevel;if(-1!==e){var a=this.hls.levels;if(a.length>Math.max(e,i)&&a[e].loadError<=a[i].loadError)return e}return this._nextAutoLevel=i,this.nextAutoLevelKey=this.getAutoLevelKey(),i},set:function(e){var t=this.deriveNextAutoLevel(e);this._nextAutoLevel!==t&&(this.nextAutoLevelKey="",this._nextAutoLevel=t)}}])}(N),lt=function(){function e(e){this.tracks=void 0,this.queues={video:[],audio:[],audiovideo:[]},this.tracks=e}var t=e.prototype;return t.destroy=function(){this.tracks=this.queues=null},t.append=function(e,t,r){if(null!==this.queues&&null!==this.tracks){var i=this.queues[t];i.push(e),1!==i.length||r||this.executeNext(t)}},t.appendBlocker=function(e){var t=this;return new Promise((function(r){var i={label:"async-blocker",execute:r,onStart:function(){},onComplete:function(){},onError:function(){}};t.append(i,e)}))},t.prependBlocker=function(e){var t=this;return new Promise((function(r){if(t.queues){var i={label:"async-blocker-prepend",execute:r,onStart:function(){},onComplete:function(){},onError:function(){}};t.queues[e].unshift(i)}}))},t.removeBlockers=function(){null!==this.queues&&[this.queues.video,this.queues.audio,this.queues.audiovideo].forEach((function(e){var t,r=null==(t=e[0])?void 0:t.label;"async-blocker"!==r&&"async-blocker-prepend"!==r||(e[0].execute(),e.splice(0,1))}))},t.unblockAudio=function(e){null!==this.queues&&this.queues.audio[0]===e&&this.shiftAndExecuteNext("audio")},t.executeNext=function(e){if(null!==this.queues&&null!==this.tracks){var t=this.queues[e];if(t.length){var r=t[0];try{r.execute()}catch(t){var i;if(r.onError(t),null===this.queues||null===this.tracks)return;var a=null==(i=this.tracks[e])?void 0:i.buffer;null!=a&&a.updating||this.shiftAndExecuteNext(e)}}}},t.shiftAndExecuteNext=function(e){null!==this.queues&&(this.queues[e].shift(),this.executeNext(e))},t.current=function(e){var t;return(null==(t=this.queues)?void 0:t[e][0])||null},t.toString=function(){var e=this.queues,t=this.tracks;return null===e||null===t?"<destroyed>":"\n"+this.list("video")+"\n"+this.list("audio")+"\n"+this.list("audiovideo")+"}"},t.list=function(e){var t,r;return null!=(t=this.queues)&&t[e]||null!=(r=this.tracks)&&r[e]?e+": ("+this.listSbInfo(e)+") "+this.listOps(e):""},t.listSbInfo=function(e){var t,r=null==(t=this.tracks)?void 0:t[e],i=null==r?void 0:r.buffer;return i?"SourceBuffer"+(i.updating?" updating":"")+(r.ended?" ended":"")+(r.ending?" ending":""):"none"},t.listOps=function(e){var t;return(null==(t=this.queues)?void 0:t[e].map((function(e){return e.label})).join(", "))||""},e}(),ut=function(e,t){for(var r=0,i=e.length-1,a=null,n=null;r<=i;){var s=t(n=e[a=(r+i)/2|0]);if(s>0)r=a+1;else{if(!(s<0))return n;i=a-1}}return null};function dt(e,t,r,i,a){void 0===r&&(r=0),void 0===i&&(i=0),void 0===a&&(a=.005);var n=null;if(e){n=t[1+e.sn-t[0].sn]||null;var s=e.endDTS-r;s>0&&s<15e-7&&(r+=15e-7),n&&e.level!==n.level&&n.end<=e.end&&(n=t[2+e.sn-t[0].sn]||null)}else 0===r&&0===t[0].start&&(n=t[0]);if(n&&((!e||e.level===n.level)&&0===ht(r,i,n)||function(e,t,r){if(t&&0===t.start&&t.level<e.level&&(t.endPTS||0)>0){var i=t.tagList.reduce((function(e,t){return"INF"===t[0]&&(e+=parseFloat(t[1])),e}),r);return e.start<=i}return!1}(n,e,Math.min(a,i))))return n;var o=ut(t,ht.bind(null,r,i));return!o||o===e&&n?n:o}function ht(e,t,r){if(void 0===e&&(e=0),void 0===t&&(t=0),r.start<=e&&r.start+r.duration>e)return 0;var i=Math.min(t,r.duration+(r.deltaPTS?r.deltaPTS:0));return r.start+r.duration-i<=e?1:r.start-i>e&&r.start?-1:0}function ft(e,t,r){var i=1e3*Math.min(t,r.duration+(r.deltaPTS?r.deltaPTS:0));return(r.endProgramDateTime||0)-i>e}function ct(e){switch(e.details){case k.FRAG_LOAD_TIMEOUT:case k.KEY_LOAD_TIMEOUT:case k.LEVEL_LOAD_TIMEOUT:case k.MANIFEST_LOAD_TIMEOUT:return!0}return!1}function vt(e,t){var r=ct(t);return e.default[(r?"timeout":"error")+"Retry"]}function gt(e,t){var r="linear"===e.backoff?1:Math.pow(2,t);return Math.min(r*e.retryDelayMs,e.maxRetryDelayMs)}function mt(e){return d(d({},e),{errorRetry:null,timeoutRetry:null})}function pt(e,t,r,i){if(!e)return!1;var a=null==i?void 0:i.code,n=t<e.maxNumRetry&&(function(e){return 0===e&&!1===navigator.onLine||!!e&&(e<400||e>499)}(a)||!!r);return e.shouldRetry?e.shouldRetry(e,t,r,i,n):n}var yt=0,Et=2,Tt=3,St=5,Lt=0,Rt=1,At=2,bt=function(e){function t(t){var r;return(r=e.call(this,"error-controller",t.logger)||this).hls=void 0,r.playlistError=0,r.penalizedRenditions={},r.hls=t,r.registerListeners(),r}o(t,e);var r=t.prototype;return r.registerListeners=function(){var e=this.hls;e.on(D.ERROR,this.onError,this),e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.LEVEL_UPDATED,this.onLevelUpdated,this)},r.unregisterListeners=function(){var e=this.hls;e&&(e.off(D.ERROR,this.onError,this),e.off(D.ERROR,this.onErrorOut,this),e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.LEVEL_UPDATED,this.onLevelUpdated,this))},r.destroy=function(){this.unregisterListeners(),this.hls=null,this.penalizedRenditions={}},r.startLoad=function(e){},r.stopLoad=function(){this.playlistError=0},r.getVariantLevelIndex=function(e){return(null==e?void 0:e.type)===x?e.level:this.hls.loadLevel},r.onManifestLoading=function(){this.playlistError=0,this.penalizedRenditions={}},r.onLevelUpdated=function(){this.playlistError=0},r.onError=function(e,t){var r;if(!t.fatal){var i=this.hls,a=t.context;switch(t.details){case k.FRAG_LOAD_ERROR:case k.FRAG_LOAD_TIMEOUT:case k.KEY_LOAD_ERROR:case k.KEY_LOAD_TIMEOUT:return void(t.errorAction=this.getFragRetryOrSwitchAction(t));case k.FRAG_PARSING_ERROR:if(null!=(r=t.frag)&&r.gap)return void(t.errorAction=kt());case k.FRAG_GAP:case k.FRAG_DECRYPT_ERROR:return t.errorAction=this.getFragRetryOrSwitchAction(t),void(t.errorAction.action=Et);case k.LEVEL_EMPTY_ERROR:case k.LEVEL_PARSING_ERROR:var n,s=t.parent===x?t.level:i.loadLevel;return void(t.details===k.LEVEL_EMPTY_ERROR&&null!=(n=t.context)&&null!=(n=n.levelDetails)&&n.live?t.errorAction=this.getPlaylistRetryOrSwitchAction(t,s):(t.levelRetry=!1,t.errorAction=this.getLevelSwitchAction(t,s)));case k.LEVEL_LOAD_ERROR:case k.LEVEL_LOAD_TIMEOUT:return void("number"==typeof(null==a?void 0:a.level)&&(t.errorAction=this.getPlaylistRetryOrSwitchAction(t,a.level)));case k.AUDIO_TRACK_LOAD_ERROR:case k.AUDIO_TRACK_LOAD_TIMEOUT:case k.SUBTITLE_LOAD_ERROR:case k.SUBTITLE_TRACK_LOAD_TIMEOUT:if(a){var o=i.loadLevelObj;if(o&&(a.type===C&&o.hasAudioGroup(a.groupId)||a.type===P&&o.hasSubtitleGroup(a.groupId)))return t.errorAction=this.getPlaylistRetryOrSwitchAction(t,i.loadLevel),t.errorAction.action=Et,void(t.errorAction.flags=Rt)}return;case k.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED:var l=i.loadLevelObj,u=null==l?void 0:l.attrs["HDCP-LEVEL"];return void(u?t.errorAction={action:Et,flags:At,hdcpLevel:u}:this.keySystemError(t));case k.BUFFER_ADD_CODEC_ERROR:case k.REMUX_ALLOC_ERROR:case k.BUFFER_APPEND_ERROR:var d;return void(t.errorAction||(t.errorAction=this.getLevelSwitchAction(t,null!=(d=t.level)?d:i.loadLevel)));case k.INTERNAL_EXCEPTION:case k.BUFFER_APPENDING_ERROR:case k.BUFFER_FULL_ERROR:case k.LEVEL_SWITCH_ERROR:case k.BUFFER_STALLED_ERROR:case k.BUFFER_SEEK_OVER_HOLE:case k.BUFFER_NUDGE_ON_STALL:return void(t.errorAction=kt())}t.type===b.KEY_SYSTEM_ERROR&&this.keySystemError(t)}},r.keySystemError=function(e){var t=this.getVariantLevelIndex(e.frag);e.levelRetry=!1,e.errorAction=this.getLevelSwitchAction(e,t)},r.getPlaylistRetryOrSwitchAction=function(e,t){var r=vt(this.hls.config.playlistLoadPolicy,e),i=this.playlistError++;if(pt(r,i,ct(e),e.response))return{action:St,flags:Lt,retryConfig:r,retryCount:i};var a=this.getLevelSwitchAction(e,t);return r&&(a.retryConfig=r,a.retryCount=i),a},r.getFragRetryOrSwitchAction=function(e){var t=this.hls,r=this.getVariantLevelIndex(e.frag),i=t.levels[r],a=t.config,n=a.fragLoadPolicy,s=a.keyLoadPolicy,o=vt(e.details.startsWith("key")?s:n,e),l=t.levels.reduce((function(e,t){return e+t.fragmentError}),0);if(i&&(e.details!==k.FRAG_GAP&&i.fragmentError++,pt(o,l,ct(e),e.response)))return{action:St,flags:Lt,retryConfig:o,retryCount:l};var u=this.getLevelSwitchAction(e,r);return o&&(u.retryConfig=o,u.retryCount=l),u},r.getLevelSwitchAction=function(e,t){var r=this.hls;null==t&&(t=r.loadLevel);var i=this.hls.levels[t];if(i){var a,n,s=e.details;i.loadError++,s===k.BUFFER_APPEND_ERROR&&i.fragmentError++;var o=-1,l=r.levels,u=r.loadLevel,d=r.minAutoLevel,h=r.maxAutoLevel;r.autoLevelEnabled||r.config.preserveManualLevelOnError||(r.loadLevel=-1);for(var f,c=null==(a=e.frag)?void 0:a.type,v=(c===w&&s===k.FRAG_PARSING_ERROR||"audio"===e.sourceBufferName&&(s===k.BUFFER_ADD_CODEC_ERROR||s===k.BUFFER_APPEND_ERROR))&&l.some((function(e){var t=e.audioCodec;return i.audioCodec!==t})),g="video"===e.sourceBufferName&&(s===k.BUFFER_ADD_CODEC_ERROR||s===k.BUFFER_APPEND_ERROR)&&l.some((function(e){var t=e.codecSet,r=e.audioCodec;return i.codecSet!==t&&i.audioCodec===r})),m=null!=(n=e.context)?n:{},p=m.type,y=m.groupId,E=function(){var t=(T+u)%l.length;if(t!==u&&t>=d&&t<=h&&0===l[t].loadError){var r,a,n=l[t];if(s===k.FRAG_GAP&&c===x&&e.frag){var f=l[t].details;if(f){var m=dt(e.frag,f.fragments,e.frag.start);if(null!=m&&m.gap)return 0}}else{if(p===C&&n.hasAudioGroup(y)||p===P&&n.hasSubtitleGroup(y))return 0;if(c===w&&null!=(r=i.audioGroups)&&r.some((function(e){return n.hasAudioGroup(e)}))||c===O&&null!=(a=i.subtitleGroups)&&a.some((function(e){return n.hasSubtitleGroup(e)}))||v&&i.audioCodec===n.audioCodec||g&&i.codecSet===n.codecSet||!v&&i.codecSet!==n.codecSet)return 0}return o=t,1}},T=l.length;T--&&(0===(f=E())||1!==f););if(o>-1&&r.loadLevel!==o)return e.levelRetry=!0,this.playlistError=0,{action:Et,flags:Lt,nextAutoLevel:o}}return{action:Et,flags:Rt}},r.onErrorOut=function(e,t){var r;switch(null==(r=t.errorAction)?void 0:r.action){case yt:break;case Et:this.sendAlternateToPenaltyBox(t),t.errorAction.resolved||t.details===k.FRAG_GAP?/MediaSource readyState: ended/.test(t.error.message)&&(this.warn('MediaSource ended after "'+t.sourceBufferName+'" sourceBuffer append error. Attempting to recover from media error.'),this.hls.recoverMediaError()):t.fatal=!0}t.fatal&&this.hls.stopLoad()},r.sendAlternateToPenaltyBox=function(e){var t=this.hls,r=e.errorAction;if(r){var i=r.flags,a=r.hdcpLevel,n=r.nextAutoLevel;switch(i){case Lt:this.switchLevel(e,n);break;case At:a&&(t.maxHdcpLevel=qe[qe.indexOf(a)-1],r.resolved=!0),this.warn('Restricting playback to HDCP-LEVEL of "'+t.maxHdcpLevel+'" or lower')}r.resolved||this.switchLevel(e,n)}},r.switchLevel=function(e,t){if(void 0!==t&&e.errorAction&&(this.warn("switching to level "+t+" after "+e.details),this.hls.nextAutoLevel=t,e.errorAction.resolved=!0,this.hls.nextLoadLevel=this.hls.nextAutoLevel,e.details===k.BUFFER_ADD_CODEC_ERROR&&e.mimeType&&"audiovideo"!==e.sourceBufferName))for(var r=je(e.mimeType),i=this.hls.levels,a=i.length;a--;)i[a][e.sourceBufferName+"Codec"]===r&&this.hls.removeLevel(a)},t}(N);function kt(e){var t={action:yt,flags:Lt};return e&&(t.resolved=!0),t}var Dt={length:0,start:function(){return 0},end:function(){return 0}},_t=function(){function e(){}return e.isBuffered=function(t,r){if(t)for(var i=e.getBuffered(t),a=i.length;a--;)if(r>=i.start(a)&&r<=i.end(a))return!0;return!1},e.bufferedRanges=function(t){if(t){var r=e.getBuffered(t);return e.timeRangesToArray(r)}return[]},e.timeRangesToArray=function(e){for(var t=[],r=0;r<e.length;r++)t.push({start:e.start(r),end:e.end(r)});return t},e.bufferInfo=function(t,r,i){if(t){var a=e.bufferedRanges(t);if(a.length)return e.bufferedInfo(a,r,i)}return{len:0,start:r,end:r,bufferedIndex:-1}},e.bufferedInfo=function(e,t,r){t=Math.max(0,t),e.length>1&&e.sort((function(e,t){return e.start-t.start||t.end-e.end}));var i=-1,a=[];if(r)for(var n=0;n<e.length;n++){t>=e[n].start&&t<=e[n].end&&(i=n);var s=a.length;if(s){var o=a[s-1].end;e[n].start-o<r?e[n].end>o&&(a[s-1].end=e[n].end):a.push(e[n])}else a.push(e[n])}else a=e;for(var l,u=0,d=t,h=t,f=0;f<a.length;f++){var c=a[f].start,v=a[f].end;if(-1===i&&t>=c&&t<=v&&(i=f),t+r>=c&&t<v)d=c,u=(h=v)-t;else if(t+r<c){l=c;break}}return{len:u,start:d||0,end:h||0,nextStart:l,buffered:e,bufferedIndex:i}},e.getBuffered=function(e){try{return e.buffered||Dt}catch(e){return j.log("failed to get media.buffered",e),Dt}},e}(),It=/(avc[1234]|hvc1|hev1|dvh[1e]|vp09|av01)(?:\.[^.,]+)+/,Ct="HlsJsTrackRemovedError",Pt=function(e){function t(t){var r;return(r=e.call(this,t)||this).name=Ct,r}return o(t,e),t}(c(Error)),xt=function(e){function t(t,r){var i,a;return(i=e.call(this,"buffer-controller",t.logger)||this).hls=void 0,i.fragmentTracker=void 0,i.details=null,i._objectUrl=null,i.operationQueue=null,i.bufferCodecEventsTotal=0,i.media=null,i.mediaSource=null,i.lastMpegAudioChunk=null,i.blockedAudioAppend=null,i.lastVideoAppendEnd=0,i.appendSource=void 0,i.transferData=void 0,i.overrides=void 0,i.appendErrors={audio:0,video:0,audiovideo:0},i.tracks={},i.sourceBuffers=[[null,null],[null,null]],i._onEndStreaming=function(e){var t;i.hls&&"open"===(null==(t=i.mediaSource)?void 0:t.readyState)&&i.hls.pauseBuffering()},i._onStartStreaming=function(e){i.hls&&i.hls.resumeBuffering()},i._onMediaSourceOpen=function(e){var t=i,r=t.media,a=t.mediaSource;e&&i.log("Media source opened"),r&&a&&(a.removeEventListener("sourceopen",i._onMediaSourceOpen),r.removeEventListener("emptied",i._onMediaEmptied),i.updateDuration(),i.hls.trigger(D.MEDIA_ATTACHED,{media:r,mediaSource:a}),null!==i.mediaSource&&i.checkPendingTracks())},i._onMediaSourceClose=function(){i.log("Media source closed")},i._onMediaSourceEnded=function(){i.log("Media source ended")},i._onMediaEmptied=function(){var e=i,t=e.mediaSrc,r=e._objectUrl;t!==r&&i.error("Media element src was set while attaching MediaSource ("+r+" > "+t+")")},i.hls=t,i.fragmentTracker=r,i.appendSource=(a=z(t.config.preferManagedMediaSource),"undefined"!=typeof self&&a===self.ManagedMediaSource),i.initTracks(),i.registerListeners(),i}o(t,e);var r=t.prototype;return r.hasSourceTypes=function(){return Object.keys(this.tracks).length>0},r.destroy=function(){this.unregisterListeners(),this.details=null,this.lastMpegAudioChunk=this.blockedAudioAppend=null,this.transferData=this.overrides=void 0,this.operationQueue&&(this.operationQueue.destroy(),this.operationQueue=null),this.hls=this.fragmentTracker=null,this._onMediaSourceOpen=this._onMediaSourceClose=null,this._onMediaSourceEnded=null,this._onStartStreaming=this._onEndStreaming=null},r.registerListeners=function(){var e=this.hls;e.on(D.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.MANIFEST_PARSED,this.onManifestParsed,this),e.on(D.BUFFER_RESET,this.onBufferReset,this),e.on(D.BUFFER_APPENDING,this.onBufferAppending,this),e.on(D.BUFFER_CODECS,this.onBufferCodecs,this),e.on(D.BUFFER_EOS,this.onBufferEos,this),e.on(D.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(D.LEVEL_UPDATED,this.onLevelUpdated,this),e.on(D.FRAG_PARSED,this.onFragParsed,this),e.on(D.FRAG_CHANGED,this.onFragChanged,this),e.on(D.ERROR,this.onError,this)},r.unregisterListeners=function(){var e=this.hls;e.off(D.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.MANIFEST_PARSED,this.onManifestParsed,this),e.off(D.BUFFER_RESET,this.onBufferReset,this),e.off(D.BUFFER_APPENDING,this.onBufferAppending,this),e.off(D.BUFFER_CODECS,this.onBufferCodecs,this),e.off(D.BUFFER_EOS,this.onBufferEos,this),e.off(D.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(D.LEVEL_UPDATED,this.onLevelUpdated,this),e.off(D.FRAG_PARSED,this.onFragParsed,this),e.off(D.FRAG_CHANGED,this.onFragChanged,this),e.off(D.ERROR,this.onError,this)},r.transferMedia=function(){var e=this,t=this.media,r=this.mediaSource;if(!t)return null;var i={};if(this.operationQueue){var a=this.isUpdating();a||this.operationQueue.removeBlockers();var s=this.isQueued();(a||s)&&this.warn("Transfering MediaSource with"+(s?" operations in queue":"")+(a?" updating SourceBuffer(s)":"")+" "+this.operationQueue),this.operationQueue.destroy()}var o=this.transferData;return!this.sourceBufferCount&&o&&o.mediaSource===r?n(i,o.tracks):this.sourceBuffers.forEach((function(t){var r=t[0];r&&(i[r]=n({},e.tracks[r]),e.removeBuffer(r)),t[0]=t[1]=null})),{media:t,mediaSource:r,tracks:i}},r.initTracks=function(){this.sourceBuffers=[[null,null],[null,null]],this.tracks={},this.resetQueue(),this.resetAppendErrors(),this.lastMpegAudioChunk=this.blockedAudioAppend=null,this.lastVideoAppendEnd=0},r.onManifestLoading=function(){this.bufferCodecEventsTotal=0,this.details=null},r.onManifestParsed=function(e,t){var r,i=2;(t.audio&&!t.video||!t.altAudio)&&(i=1),this.bufferCodecEventsTotal=i,this.log(i+" bufferCodec event(s) expected."),null!=(r=this.transferData)&&r.mediaSource&&this.sourceBufferCount&&i&&this.bufferCreated()},r.onMediaAttaching=function(e,t){var r=this.media=t.media;this.transferData=this.overrides=void 0;var i=z(this.appendSource);if(i){var a=!!t.mediaSource;(a||t.overrides)&&(this.transferData=t,this.overrides=t.overrides);var n=this.mediaSource=t.mediaSource||new i;if(this.assignMediaSource(n),a)this._objectUrl=r.src,this.attachTransferred();else{var s=this._objectUrl=self.URL.createObjectURL(n);if(this.appendSource)try{r.removeAttribute("src");var o=self.ManagedMediaSource;r.disableRemotePlayback=r.disableRemotePlayback||o&&n instanceof o,wt(r),function(e,t){var r=self.document.createElement("source");r.type="video/mp4",r.src=t,e.appendChild(r)}(r,s),r.load()}catch(e){r.src=s}else r.src=s}r.addEventListener("emptied",this._onMediaEmptied)}},r.assignMediaSource=function(e){var t,r;this.log(((null==(t=this.transferData)?void 0:t.mediaSource)===e?"transferred":"created")+" media source: "+(null==(r=e.constructor)?void 0:r.name)),e.addEventListener("sourceopen",this._onMediaSourceOpen),e.addEventListener("sourceended",this._onMediaSourceEnded),e.addEventListener("sourceclose",this._onMediaSourceClose),this.appendSource&&(e.addEventListener("startstreaming",this._onStartStreaming),e.addEventListener("endstreaming",this._onEndStreaming))},r.attachTransferred=function(){var e=this,t=this.media,r=this.transferData;if(r&&t){var i=this.tracks,a=r.tracks,n=a?Object.keys(a):null,s=n?n.length:0,o=function(){Promise.resolve().then((function(){e.media&&e.mediaSourceOpenOrEnded&&e._onMediaSourceOpen()}))};if(a&&n&&s){if(!this.tracksReady)return this.hls.config.startFragPrefetch=!0,void this.log("attachTransferred: waiting for SourceBuffer track info");if(this.log("attachTransferred: (bufferCodecEventsTotal "+this.bufferCodecEventsTotal+")\nrequired tracks: "+it(i,(function(e,t){return"initSegment"===e?void 0:t}))+";\ntransfer tracks: "+it(a,(function(e,t){return"initSegment"===e?void 0:t}))+"}"),!function(e,t){var r=Object.keys(e),i=Object.keys(t),a=r.length,n=i.length;return!a||!n||a===n&&!r.some((function(e){return-1===i.indexOf(e)}))}(a,i)){r.mediaSource=null,r.tracks=void 0;var l=t.currentTime,u=this.details,d=Math.max(l,(null==u?void 0:u.fragments[0].start)||0);return d-l>1?void this.log("attachTransferred: waiting for playback to reach new tracks start time "+l+" -> "+d):(this.warn('attachTransferred: resetting MediaSource for incompatible tracks ("'+Object.keys(a)+'"->"'+Object.keys(i)+'") start time: '+d+" currentTime: "+l),this.onMediaDetaching(D.MEDIA_DETACHING,{}),this.onMediaAttaching(D.MEDIA_ATTACHING,r),void(t.currentTime=d))}this.transferData=void 0,n.forEach((function(t){var r=t,i=a[r];if(i){var n=i.buffer;if(n){var s=e.fragmentTracker,o=i.id;if(s.hasFragments(o)||s.hasParts(o)){var l=_t.getBuffered(n);s.detectEvictedFragments(r,l,o,null,!0)}var u=Ot(r),d=[r,n];e.sourceBuffers[u]=d,n.updating&&e.operationQueue&&e.operationQueue.prependBlocker(r),e.trackSourceBuffer(r,i)}}})),o(),this.bufferCreated()}else this.log("attachTransferred: MediaSource w/o SourceBuffers"),o()}},r.onMediaDetaching=function(e,t){var r=this,i=!!t.transferMedia;this.transferData=this.overrides=void 0;var a=this.media,n=this.mediaSource,s=this._objectUrl;if(n){if(this.log("media source "+(i?"transferring":"detaching")),i)this.sourceBuffers.forEach((function(e){var t=e[0];t&&r.removeBuffer(t)})),this.resetQueue();else{if(this.mediaSourceOpenOrEnded){var o="open"===n.readyState;try{for(var l=n.sourceBuffers,u=l.length;u--;)o&&l[u].abort(),n.removeSourceBuffer(l[u]);o&&n.endOfStream()}catch(e){this.warn("onMediaDetaching: "+e.message+" while calling endOfStream")}}this.sourceBufferCount&&this.onBufferReset()}n.removeEventListener("sourceopen",this._onMediaSourceOpen),n.removeEventListener("sourceended",this._onMediaSourceEnded),n.removeEventListener("sourceclose",this._onMediaSourceClose),this.appendSource&&(n.removeEventListener("startstreaming",this._onStartStreaming),n.removeEventListener("endstreaming",this._onEndStreaming)),this.mediaSource=null,this._objectUrl=null}a&&(a.removeEventListener("emptied",this._onMediaEmptied),i||(s&&self.URL.revokeObjectURL(s),this.mediaSrc===s?(a.removeAttribute("src"),this.appendSource&&wt(a),a.load()):this.warn("media|source.src was changed by a third party - skip cleanup")),this.media=null),this.hls.trigger(D.MEDIA_DETACHED,t)},r.onBufferReset=function(){var e=this;this.sourceBuffers.forEach((function(t){var r=t[0];r&&e.resetBuffer(r)})),this.initTracks()},r.resetBuffer=function(e){var t,r=null==(t=this.tracks[e])?void 0:t.buffer;if(this.removeBuffer(e),r)try{var i;null!=(i=this.mediaSource)&&i.sourceBuffers.length&&this.mediaSource.removeSourceBuffer(r)}catch(t){this.warn("onBufferReset "+e,t)}delete this.tracks[e]},r.removeBuffer=function(e){this.removeBufferListeners(e),this.sourceBuffers[Ot(e)]=[null,null];var t=this.tracks[e];t&&(t.buffer=void 0)},r.resetQueue=function(){this.operationQueue&&this.operationQueue.destroy(),this.operationQueue=new lt(this.tracks)},r.onBufferCodecs=function(e,t){var r=this,i=this.tracks,a=Object.keys(t);this.log('BUFFER_CODECS: "'+a+'" (current SB count '+this.sourceBufferCount+")");var n="audiovideo"in t&&(i.audio||i.video)||i.audiovideo&&("audio"in t||"video"in t),s=!n&&this.sourceBufferCount&&this.media&&a.some((function(e){return!i[e]}));n||s?this.warn('Unsupported transition between "'+Object.keys(i)+'" and "'+a+'" SourceBuffers'):(a.forEach((function(e){var a,n,s=t[e],o=s.id,l=s.codec,u=s.levelCodec,d=s.container,h=s.metadata,f=s.supplemental,c=i[e],v=null==(a=r.transferData)||null==(a=a.tracks)?void 0:a[e],g=null!=v&&v.buffer?v:c,m=(null==g?void 0:g.pendingCodec)||(null==g?void 0:g.codec),p=null==g?void 0:g.levelCodec;c||(c=i[e]={buffer:void 0,listeners:[],codec:l,supplemental:f,container:d,levelCodec:u,metadata:h,id:o});var y=He(m,p),E=null==y?void 0:y.replace(It,"$1"),T=He(l,u),S=null==(n=T)?void 0:n.replace(It,"$1");T&&y&&E!==S&&("audio"===e.slice(0,5)&&(T=Ve(T,r.appendSource)),r.log("switching codec "+m+" to "+T),T!==(c.pendingCodec||c.codec)&&(c.pendingCodec=T),c.container=d,r.appendChangeType(e,d,T))})),(this.tracksReady||this.sourceBufferCount)&&(t.tracks=this.sourceBufferTracks),this.sourceBufferCount||this.mediaSourceOpenOrEnded&&this.checkPendingTracks())},r.appendChangeType=function(e,t,r){var i=this,a=t+";codecs="+r,n={label:"change-type="+a,execute:function(){var n=i.tracks[e];if(n){var s=n.buffer;null!=s&&s.changeType&&(i.log("changing "+e+" sourceBuffer type to "+a),s.changeType(a),n.codec=r,n.container=t)}i.shiftAndExecuteNext(e)},onStart:function(){},onComplete:function(){},onError:function(t){i.warn("Failed to change "+e+" SourceBuffer type",t)}};this.append(n,e,this.isPending(this.tracks[e]))},r.blockAudio=function(e){var t,r=this,i=e.start,a=i+.05*e.duration;if(!0!==(null==(t=this.fragmentTracker.getAppendedFrag(i,x))?void 0:t.gap)){var n={label:"block-audio",execute:function(){var e,t=r.tracks.video;(r.lastVideoAppendEnd>a||null!=t&&t.buffer&&_t.isBuffered(t.buffer,a)||!0===(null==(e=r.fragmentTracker.getAppendedFrag(a,x))?void 0:e.gap))&&(r.blockedAudioAppend=null,r.shiftAndExecuteNext("audio"))},onStart:function(){},onComplete:function(){},onError:function(e){r.warn("Error executing block-audio operation",e)}};this.blockedAudioAppend={op:n,frag:e},this.append(n,"audio",!0)}},r.unblockAudio=function(){var e=this.blockedAudioAppend,t=this.operationQueue;e&&t&&(this.blockedAudioAppend=null,t.unblockAudio(e.op))},r.onBufferAppending=function(e,t){var r=this,i=this.tracks,a=t.data,n=t.type,s=t.parent,o=t.frag,l=t.part,u=t.chunkMeta,d=t.offset,h=u.buffering[n],f=o.sn,c=o.cc,v=self.performance.now();h.start=v;var g=o.stats.buffering,m=l?l.stats.buffering:null;0===g.start&&(g.start=v),m&&0===m.start&&(m.start=v);var p=i.audio,y=!1;"audio"===n&&"audio/mpeg"===(null==p?void 0:p.container)&&(y=!this.lastMpegAudioChunk||1===u.id||this.lastMpegAudioChunk.sn!==u.sn,this.lastMpegAudioChunk=u);var E=i.video,T=null==E?void 0:E.buffer;if(T&&"initSegment"!==f){var S=l||o,R=this.blockedAudioAppend;if("audio"!==n||"main"===s||this.blockedAudioAppend||E.ending||E.ended){if("video"===n){var A=S.end;if(R){var _=R.frag.start;(A>_||A<this.lastVideoAppendEnd||_t.isBuffered(T,_))&&this.unblockAudio()}this.lastVideoAppendEnd=A}}else{var I=S.start+.05*S.duration,C=T.buffered,P=this.currentOp("video");C.length||P?!P&&!_t.isBuffered(T,I)&&this.lastVideoAppendEnd<I&&this.blockAudio(S):this.blockAudio(S)}}var x=(l||o).start,w={label:"append-"+n,execute:function(){var e;h.executeStart=self.performance.now();var t=null==(e=r.tracks[n])?void 0:e.buffer;t&&(y?r.updateTimestampOffset(t,x,.1,n,f,c):void 0!==d&&L(d)&&r.updateTimestampOffset(t,d,1e-6,n,f,c)),r.appendExecutor(a,n)},onStart:function(){},onComplete:function(){var e=self.performance.now();h.executeEnd=h.end=e,0===g.first&&(g.first=e),m&&0===m.first&&(m.first=e);var t={};r.sourceBuffers.forEach((function(e){var r=e[0],i=e[1];r&&(t[r]=_t.getBuffered(i))})),r.appendErrors[n]=0,"audio"===n||"video"===n?r.appendErrors.audiovideo=0:(r.appendErrors.audio=0,r.appendErrors.video=0),r.hls.trigger(D.BUFFER_APPENDED,{type:n,frag:o,part:l,chunkMeta:u,parent:o.type,timeRanges:t})},onError:function(e){var t,i={type:b.MEDIA_ERROR,parent:o.type,details:k.BUFFER_APPEND_ERROR,sourceBufferName:n,frag:o,part:l,chunkMeta:u,error:e,err:e,fatal:!1},a=null==(t=r.media)?void 0:t.error;if(e.code===DOMException.QUOTA_EXCEEDED_ERR||"QuotaExceededError"==e.name||"quota"in e)i.details=k.BUFFER_FULL_ERROR;else if(e.code===DOMException.INVALID_STATE_ERR&&r.mediaSourceOpenOrEnded&&!a)i.errorAction=kt(!0);else if(e.name===Ct&&0===r.sourceBufferCount)i.errorAction=kt(!0);else{var s=++r.appendErrors[n];r.warn("Failed "+s+"/"+r.hls.config.appendErrorMaxRetry+' times to append segment in "'+n+'" sourceBuffer ('+(a||"no media error")+")"),(s>=r.hls.config.appendErrorMaxRetry||a)&&(i.fatal=!0)}r.hls.trigger(D.ERROR,i)}};this.append(w,n,this.isPending(this.tracks[n]))},r.getFlushOp=function(e,t,r){var i=this;return this.log('queuing "'+e+'" remove '+t+"-"+r),{label:"remove",execute:function(){i.removeExecutor(e,t,r)},onStart:function(){},onComplete:function(){i.hls.trigger(D.BUFFER_FLUSHED,{type:e})},onError:function(a){i.warn("Failed to remove "+t+"-"+r+' from "'+e+'" SourceBuffer',a)}}},r.onBufferFlushing=function(e,t){var r=this,i=t.type,a=t.startOffset,n=t.endOffset;i?this.append(this.getFlushOp(i,a,n),i):this.sourceBuffers.forEach((function(e){var t=e[0];t&&r.append(r.getFlushOp(t,a,n),t)}))},r.onFragParsed=function(e,t){var r=this,i=t.frag,a=t.part,n=[],s=a?a.elementaryStreams:i.elementaryStreams;s[re]?n.push("audiovideo"):(s[ee]&&n.push("audio"),s[te]&&n.push("video")),0===n.length&&this.warn("Fragments must have at least one ElementaryStreamType set. type: "+i.type+" level: "+i.level+" sn: "+i.sn),this.blockBuffers((function(){var e=self.performance.now();i.stats.buffering.end=e,a&&(a.stats.buffering.end=e);var t=a?a.stats:i.stats;r.hls.trigger(D.FRAG_BUFFERED,{frag:i,part:a,stats:t,id:i.type})}),n).catch((function(e){r.warn("Fragment buffered callback "+e),r.stepOperationQueue(r.sourceBufferTypes)}))},r.onFragChanged=function(e,t){this.trimBuffers()},r.onBufferEos=function(e,t){var r,i=this;this.sourceBuffers.forEach((function(e){var r=e[0];if(r){var a=i.tracks[r];t.type&&t.type!==r||(a.ending=!0,a.ended||(a.ended=!0,i.log(r+" buffer reached EOS")))}}));var a=!1!==(null==(r=this.overrides)?void 0:r.endOfStream);this.sourceBufferCount>0&&!this.sourceBuffers.some((function(e){var t,r=e[0];return r&&!(null!=(t=i.tracks[r])&&t.ended)}))?a?(this.log("Queueing EOS"),this.blockUntilOpen((function(){i.tracksEnded();var e=i.mediaSource;e&&"open"===e.readyState?(i.log("Calling mediaSource.endOfStream()"),e.endOfStream(),i.hls.trigger(D.BUFFERED_TO_END,void 0)):e&&i.log("Could not call mediaSource.endOfStream(). mediaSource.readyState: "+e.readyState)}))):(this.tracksEnded(),this.hls.trigger(D.BUFFERED_TO_END,void 0)):"video"===t.type&&this.unblockAudio()},r.tracksEnded=function(){var e=this;this.sourceBuffers.forEach((function(t){var r=t[0];if(null!==r){var i=e.tracks[r];i&&(i.ending=!1)}}))},r.onLevelUpdated=function(e,t){var r=t.details;r.fragments.length&&(this.details=r,this.updateDuration())},r.updateDuration=function(){var e=this;this.blockUntilOpen((function(){var t=e.getDurationAndRange();t&&e.updateMediaSource(t)}))},r.onError=function(e,t){if(t.details===k.BUFFER_APPEND_ERROR&&t.frag){var r,i=null==(r=t.errorAction)?void 0:r.nextAutoLevel;L(i)&&i!==t.frag.level&&this.resetAppendErrors()}},r.resetAppendErrors=function(){this.appendErrors={audio:0,video:0,audiovideo:0}},r.trimBuffers=function(){var e=this.hls,t=this.details,r=this.media;if(r&&null!==t&&this.sourceBufferCount){var i=e.config,a=r.currentTime,n=t.levelTargetDuration,s=t.live&&null!==i.liveBackBufferLength?i.liveBackBufferLength:i.backBufferLength;if(L(s)&&s>=0){var o=Math.max(s,n),l=Math.floor(a/n)*n-o;this.flushBackBuffer(a,n,l)}var u=i.frontBufferFlushThreshold;if(L(u)&&u>0){var d=Math.max(i.maxBufferLength,u),h=Math.max(d,n),f=Math.floor(a/n)*n+h;this.flushFrontBuffer(a,n,f)}}},r.flushBackBuffer=function(e,t,r){var i=this;this.sourceBuffers.forEach((function(e){var t=e[0],a=e[1];if(a){var n=_t.getBuffered(a);if(n.length>0&&r>n.start(0)){var s;i.hls.trigger(D.BACK_BUFFER_REACHED,{bufferEnd:r});var o=i.tracks[t];if(null!=(s=i.details)&&s.live)i.hls.trigger(D.LIVE_BACK_BUFFER_REACHED,{bufferEnd:r});else if(null!=o&&o.ended)return void i.log("Cannot flush "+t+" back buffer while SourceBuffer is in ended state");i.hls.trigger(D.BUFFER_FLUSHING,{startOffset:0,endOffset:r,type:t})}}}))},r.flushFrontBuffer=function(e,t,r){var i=this;this.sourceBuffers.forEach((function(t){var a=t[0],n=t[1];if(n){var s=_t.getBuffered(n),o=s.length;if(o<2)return;var l=s.start(o-1),u=s.end(o-1);if(r>l||e>=l&&e<=u)return;i.hls.trigger(D.BUFFER_FLUSHING,{startOffset:l,endOffset:1/0,type:a})}}))},r.getDurationAndRange=function(){var e,t=this.details,r=this.mediaSource;if(!t||!this.media||"open"!==(null==r?void 0:r.readyState))return null;var i=t.edge;if(t.live&&this.hls.config.liveDurationInfinity){if(t.fragments.length&&r.setLiveSeekableRange){var a=Math.max(0,t.fragmentStart);return{duration:1/0,start:a,end:Math.max(a,i)}}return{duration:1/0}}var n=null==(e=this.overrides)?void 0:e.duration;if(n)return L(n)?{duration:n}:null;var s=this.media.duration;return i>(L(r.duration)?r.duration:0)&&i>s||!L(s)?{duration:i}:null},r.updateMediaSource=function(e){var t=e.duration,r=e.start,i=e.end,a=this.mediaSource;this.media&&a&&"open"===a.readyState&&(a.duration!==t&&(L(t)&&this.log("Updating MediaSource duration to "+t.toFixed(3)),a.duration=t),void 0!==r&&void 0!==i&&(this.log("MediaSource duration is set to "+a.duration+". Setting seekable range to "+r+"-"+i+"."),a.setLiveSeekableRange(r,i)))},r.checkPendingTracks=function(){var e=this.bufferCodecEventsTotal,t=this.pendingTrackCount,r=this.tracks;if(this.log("checkPendingTracks (pending: "+t+" codec events expected: "+e+") "+it(r)),this.tracksReady){var i,a=null==(i=this.transferData)?void 0:i.tracks;a&&Object.keys(a).length?this.attachTransferred():this.createSourceBuffers()}},r.bufferCreated=function(){var e=this;if(this.sourceBufferCount){var t={};this.sourceBuffers.forEach((function(r){var i=r[0],a=r[1];if(i){var n=e.tracks[i];t[i]={buffer:a,container:n.container,codec:n.codec,supplemental:n.supplemental,levelCodec:n.levelCodec,id:n.id,metadata:n.metadata}}})),this.hls.trigger(D.BUFFER_CREATED,{tracks:t}),this.log("SourceBuffers created. Running queue: "+this.operationQueue),this.sourceBuffers.forEach((function(t){var r=t[0];e.executeNext(r)}))}else{var r=new Error("could not create source buffer for media codec(s)");this.hls.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.BUFFER_INCOMPATIBLE_CODECS_ERROR,fatal:!0,error:r,reason:r.message})}},r.createSourceBuffers=function(){var e=this.tracks,t=this.sourceBuffers,r=this.mediaSource;if(!r)throw new Error("createSourceBuffers called when mediaSource was null");for(var i in e){var a=i,n=e[a];if(this.isPending(n)){var s=this.getTrackCodec(n,a),o=n.container+";codecs="+s;n.codec=s,this.log("creating sourceBuffer("+o+")"+(this.currentOp(a)?" Queued":"")+" "+it(n));try{var l=r.addSourceBuffer(o),u=Ot(a),d=[a,l];t[u]=d,n.buffer=l}catch(e){var h;return this.error("error while trying to add sourceBuffer: "+e.message),this.shiftAndExecuteNext(a),null==(h=this.operationQueue)||h.removeBlockers(),delete this.tracks[a],void this.hls.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.BUFFER_ADD_CODEC_ERROR,fatal:!1,error:e,sourceBufferName:a,mimeType:o,parent:n.id})}this.trackSourceBuffer(a,n)}}this.bufferCreated()},r.getTrackCodec=function(e,t){var r=e.supplemental,i=e.codec;r&&("video"===t||"audiovideo"===t)&&Oe(r,"video")&&(i=function(e,t){var r=[];if(e)for(var i=e.split(","),a=0;a<i.length;a++)we(i[a],"video")||r.push(i[a]);return t&&r.push(t),r.join(",")}(i,r));var a=He(i,e.levelCodec);return a?"audio"===t.slice(0,5)?Ve(a,this.appendSource):a:""},r.trackSourceBuffer=function(e,t){var r=this,i=t.buffer;if(i){var a=this.getTrackCodec(t,e);this.tracks[e]={buffer:i,codec:a,container:t.container,levelCodec:t.levelCodec,supplemental:t.supplemental,metadata:t.metadata,id:t.id,listeners:[]},this.removeBufferListeners(e),this.addBufferListener(e,"updatestart",this.onSBUpdateStart),this.addBufferListener(e,"updateend",this.onSBUpdateEnd),this.addBufferListener(e,"error",this.onSBUpdateError),this.appendSource&&this.addBufferListener(e,"bufferedchange",(function(e,t){var i=t.removedRanges;null!=i&&i.length&&r.hls.trigger(D.BUFFER_FLUSHED,{type:e})}))}},r.onSBUpdateStart=function(e){var t=this.currentOp(e);t&&t.onStart()},r.onSBUpdateEnd=function(e){var t;if("closed"!==(null==(t=this.mediaSource)?void 0:t.readyState)){var r=this.currentOp(e);r&&(r.onComplete(),this.shiftAndExecuteNext(e))}else this.resetBuffer(e)},r.onSBUpdateError=function(e,t){var r,i=new Error(e+" SourceBuffer error. MediaSource readyState: "+(null==(r=this.mediaSource)?void 0:r.readyState));this.error(""+i,t),this.hls.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.BUFFER_APPENDING_ERROR,sourceBufferName:e,error:i,fatal:!1});var a=this.currentOp(e);a&&a.onError(i)},r.updateTimestampOffset=function(e,t,r,i,a,n){var s=t-e.timestampOffset;Math.abs(s)>=r&&(this.log("Updating "+i+" SourceBuffer timestampOffset to "+t+" (sn: "+a+" cc: "+n+")"),e.timestampOffset=t)},r.removeExecutor=function(e,t,r){var i=this.media,a=this.mediaSource,n=this.tracks[e],s=null==n?void 0:n.buffer;if(!i||!a||!s)return this.warn("Attempting to remove from the "+e+" SourceBuffer, but it does not exist"),void this.shiftAndExecuteNext(e);var o=L(i.duration)?i.duration:1/0,l=L(a.duration)?a.duration:1/0,u=Math.max(0,t),d=Math.min(r,o,l);d>u&&(!n.ending||n.ended)?(n.ended=!1,this.log("Removing ["+u+","+d+"] from the "+e+" SourceBuffer"),s.remove(u,d)):this.shiftAndExecuteNext(e)},r.appendExecutor=function(e,t){var r=this.tracks[t],i=null==r?void 0:r.buffer;if(!i)throw new Pt("Attempting to append to the "+t+" SourceBuffer, but it does not exist");r.ending=!1,r.ended=!1,i.appendBuffer(e)},r.blockUntilOpen=function(e){var t=this;if(this.isUpdating()||this.isQueued())this.blockBuffers(e).catch((function(e){t.warn("SourceBuffer blocked callback "+e),t.stepOperationQueue(t.sourceBufferTypes)}));else try{e()}catch(e){this.warn("Callback run without blocking "+this.operationQueue+" "+e)}},r.isUpdating=function(){return this.sourceBuffers.some((function(e){var t=e[0],r=e[1];return t&&r.updating}))},r.isQueued=function(){var e=this;return this.sourceBuffers.some((function(t){var r=t[0];return r&&!!e.currentOp(r)}))},r.isPending=function(e){return!!e&&!e.buffer},r.blockBuffers=function(e,t){var r=this;if(void 0===t&&(t=this.sourceBufferTypes),!t.length)return this.log("Blocking operation requested, but no SourceBuffers exist"),Promise.resolve().then(e);var i=this.operationQueue,a=t.map((function(e){return r.appendBlocker(e)}));return t.length>1&&!!this.blockedAudioAppend&&this.unblockAudio(),Promise.all(a).then((function(t){i===r.operationQueue&&(e(),r.stepOperationQueue(r.sourceBufferTypes))}))},r.stepOperationQueue=function(e){var t=this;e.forEach((function(e){var r,i=null==(r=t.tracks[e])?void 0:r.buffer;i&&!i.updating&&t.shiftAndExecuteNext(e)}))},r.append=function(e,t,r){this.operationQueue&&this.operationQueue.append(e,t,r)},r.appendBlocker=function(e){if(this.operationQueue)return this.operationQueue.appendBlocker(e)},r.currentOp=function(e){return this.operationQueue?this.operationQueue.current(e):null},r.executeNext=function(e){e&&this.operationQueue&&this.operationQueue.executeNext(e)},r.shiftAndExecuteNext=function(e){this.operationQueue&&this.operationQueue.shiftAndExecuteNext(e)},r.addBufferListener=function(e,t,r){var i=this.tracks[e];if(i){var a=i.buffer;if(a){var n=r.bind(this,e);i.listeners.push({event:t,listener:n}),a.addEventListener(t,n)}}},r.removeBufferListeners=function(e){var t=this.tracks[e];if(t){var r=t.buffer;r&&(t.listeners.forEach((function(e){r.removeEventListener(e.event,e.listener)})),t.listeners.length=0)}},i(t,[{key:"mediaSourceOpenOrEnded",get:function(){var e,t=null==(e=this.mediaSource)?void 0:e.readyState;return"open"===t||"ended"===t}},{key:"sourceBufferTracks",get:function(){var e=this;return Object.keys(this.tracks).reduce((function(t,r){var i=e.tracks[r];return t[r]={id:i.id,container:i.container,codec:i.codec,levelCodec:i.levelCodec},t}),{})}},{key:"bufferedToEnd",get:function(){var e=this;return this.sourceBufferCount>0&&!this.sourceBuffers.some((function(t){var r=t[0];if(r){var i=e.tracks[r];if(i)return!i.ended||i.ending}return!1}))}},{key:"tracksReady",get:function(){var e=this.pendingTrackCount;return e>0&&(e>=this.bufferCodecEventsTotal||this.isPending(this.tracks.audiovideo))}},{key:"mediaSrc",get:function(){var e,t,r=(null==(e=this.media)||null==(t=e.querySelector)?void 0:t.call(e,"source"))||this.media;return null==r?void 0:r.src}},{key:"pendingTrackCount",get:function(){var e=this;return Object.keys(this.tracks).reduce((function(t,r){return t+(e.isPending(e.tracks[r])?1:0)}),0)}},{key:"sourceBufferCount",get:function(){return this.sourceBuffers.reduce((function(e,t){return e+(t[0]?1:0)}),0)}},{key:"sourceBufferTypes",get:function(){return this.sourceBuffers.map((function(e){return e[0]})).filter((function(e){return!!e}))}}])}(N);function wt(e){var t=e.querySelectorAll("source");[].slice.call(t).forEach((function(t){e.removeChild(t)}))}function Ot(e){return"audio"===e?1:0}var Ft=function(){function e(e){this.hls=void 0,this.autoLevelCapping=void 0,this.firstLevel=void 0,this.media=void 0,this.restrictedLevels=void 0,this.timer=void 0,this.clientRect=void 0,this.streamController=void 0,this.hls=e,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.firstLevel=-1,this.media=null,this.restrictedLevels=[],this.timer=void 0,this.clientRect=null,this.registerListeners()}var t=e.prototype;return t.setStreamController=function(e){this.streamController=e},t.destroy=function(){this.hls&&this.unregisterListener(),this.timer&&this.stopCapping(),this.media=null,this.clientRect=null,this.hls=this.streamController=null},t.registerListeners=function(){var e=this.hls;e.on(D.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),e.on(D.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(D.MANIFEST_PARSED,this.onManifestParsed,this),e.on(D.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(D.BUFFER_CODECS,this.onBufferCodecs,this),e.on(D.MEDIA_DETACHING,this.onMediaDetaching,this)},t.unregisterListener=function(){var e=this.hls;e.off(D.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),e.off(D.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(D.MANIFEST_PARSED,this.onManifestParsed,this),e.off(D.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(D.BUFFER_CODECS,this.onBufferCodecs,this),e.off(D.MEDIA_DETACHING,this.onMediaDetaching,this)},t.onFpsDropLevelCapping=function(e,t){var r=this.hls.levels[t.droppedLevel];this.isLevelAllowed(r)&&this.restrictedLevels.push({bitrate:r.bitrate,height:r.height,width:r.width})},t.onMediaAttaching=function(e,t){this.media=t.media instanceof HTMLVideoElement?t.media:null,this.clientRect=null,this.timer&&this.hls.levels.length&&this.detectPlayerSize()},t.onManifestParsed=function(e,t){var r=this.hls;this.restrictedLevels=[],this.firstLevel=t.firstLevel,r.config.capLevelToPlayerSize&&t.video&&this.startCapping()},t.onLevelsUpdated=function(e,t){this.timer&&L(this.autoLevelCapping)&&this.detectPlayerSize()},t.onBufferCodecs=function(e,t){this.hls.config.capLevelToPlayerSize&&t.video&&this.startCapping()},t.onMediaDetaching=function(){this.stopCapping(),this.media=null},t.detectPlayerSize=function(){if(this.media){if(this.mediaHeight<=0||this.mediaWidth<=0)return void(this.clientRect=null);var e=this.hls.levels;if(e.length){var t=this.hls,r=this.getMaxLevel(e.length-1);r!==this.autoLevelCapping&&t.logger.log("Setting autoLevelCapping to "+r+": "+e[r].height+"p@"+e[r].bitrate+" for media "+this.mediaWidth+"x"+this.mediaHeight),t.autoLevelCapping=r,t.autoLevelEnabled&&t.autoLevelCapping>this.autoLevelCapping&&this.streamController&&this.streamController.nextLevelSwitch(),this.autoLevelCapping=t.autoLevelCapping}}},t.getMaxLevel=function(t){var r=this,i=this.hls.levels;if(!i.length)return-1;var a=i.filter((function(e,i){return r.isLevelAllowed(e)&&i<=t}));return this.clientRect=null,e.getMaxLevelByMediaSize(a,this.mediaWidth,this.mediaHeight)},t.startCapping=function(){this.timer||(this.autoLevelCapping=Number.POSITIVE_INFINITY,self.clearInterval(this.timer),this.timer=self.setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())},t.stopCapping=function(){this.restrictedLevels=[],this.firstLevel=-1,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(self.clearInterval(this.timer),this.timer=void 0)},t.getDimensions=function(){if(this.clientRect)return this.clientRect;var e=this.media,t={width:0,height:0};if(e){var r=e.getBoundingClientRect();t.width=r.width,t.height=r.height,t.width||t.height||(t.width=r.right-r.left||e.width||0,t.height=r.bottom-r.top||e.height||0)}return this.clientRect=t,t},t.isLevelAllowed=function(e){return!this.restrictedLevels.some((function(t){return e.bitrate===t.bitrate&&e.width===t.width&&e.height===t.height}))},e.getMaxLevelByMediaSize=function(e,t,r){if(null==e||!e.length)return-1;for(var i,a,n=e.length-1,s=Math.max(t,r),o=0;o<e.length;o+=1){var l=e[o];if((l.width>=s||l.height>=s)&&(i=l,!(a=e[o+1])||i.width!==a.width||i.height!==a.height)){n=o;break}}return n},i(e,[{key:"mediaWidth",get:function(){return this.getDimensions().width*this.contentScaleFactor}},{key:"mediaHeight",get:function(){return this.getDimensions().height*this.contentScaleFactor}},{key:"contentScaleFactor",get:function(){var e=1;if(!this.hls.config.ignoreDevicePixelRatio)try{e=self.devicePixelRatio}catch(e){}return Math.min(e,this.hls.config.maxDevicePixelRatio)}}])}(),Mt=/^(\d+)x(\d+)$/,Nt=/(.+?)=(".*?"|.*?)(?:,|$)/g,Bt=function(){function e(t,r){"string"==typeof t&&(t=e.parseAttrList(t,r)),n(this,t)}var t=e.prototype;return t.decimalInteger=function(e){var t=parseInt(this[e],10);return t>Number.MAX_SAFE_INTEGER?1/0:t},t.hexadecimalInteger=function(e){if(this[e]){var t=(this[e]||"0x").slice(2);t=(1&t.length?"0":"")+t;for(var r=new Uint8Array(t.length/2),i=0;i<t.length/2;i++)r[i]=parseInt(t.slice(2*i,2*i+2),16);return r}return null},t.hexadecimalIntegerAsNumber=function(e){var t=parseInt(this[e],16);return t>Number.MAX_SAFE_INTEGER?1/0:t},t.decimalFloatingPoint=function(e){return parseFloat(this[e])},t.optionalFloat=function(e,t){var r=this[e];return r?parseFloat(r):t},t.enumeratedString=function(e){return this[e]},t.enumeratedStringList=function(e,t){var r=this[e];return(r?r.split(/[ ,]+/):[]).reduce((function(e,t){return e[t.toLowerCase()]=!0,e}),t)},t.bool=function(e){return"YES"===this[e]},t.decimalResolution=function(e){var t=Mt.exec(this[e]);if(null!==t)return{width:parseInt(t[1],10),height:parseInt(t[2],10)}},e.parseAttrList=function(e,t){var r,i={};for(Nt.lastIndex=0;null!==(r=Nt.exec(e));){var a=r[1].trim(),n=r[2],s=0===n.indexOf('"')&&n.lastIndexOf('"')===n.length-1,o=!1;if(s)n=n.slice(1,-1);else switch(a){case"IV":case"SCTE35-CMD":case"SCTE35-IN":case"SCTE35-OUT":o=!0}if(t&&(s||o));else if(!o&&!s)switch(a){case"CLOSED-CAPTIONS":if("NONE"===n)break;case"ALLOWED-CPC":case"CLASS":case"ASSOC-LANGUAGE":case"AUDIO":case"BYTERANGE":case"CHANNELS":case"CHARACTERISTICS":case"CODECS":case"DATA-ID":case"END-DATE":case"GROUP-ID":case"ID":case"IMPORT":case"INSTREAM-ID":case"KEYFORMAT":case"KEYFORMATVERSIONS":case"LANGUAGE":case"NAME":case"PATHWAY-ID":case"QUERYPARAM":case"RECENTLY-REMOVED-DATERANGES":case"SERVER-URI":case"STABLE-RENDITION-ID":case"STABLE-VARIANT-ID":case"START-DATE":case"SUBTITLES":case"SUPPLEMENTAL-CODECS":case"URI":case"VALUE":case"VIDEO":case"X-ASSET-LIST":case"X-ASSET-URI":j.warn(e+": attribute "+a+" is missing quotes")}i[a]=n}return i},i(e,[{key:"clientAttrs",get:function(){return Object.keys(this).filter((function(e){return"X-"===e.substring(0,2)}))}}])}();function Ut(e){return"SCTE35-OUT"===e||"SCTE35-IN"===e||"SCTE35-CMD"===e}var Gt=function(){return i((function(e,t,r){var i;if(void 0===r&&(r=0),this.attr=void 0,this.tagAnchor=void 0,this.tagOrder=void 0,this._startDate=void 0,this._endDate=void 0,this._dateAtEnd=void 0,this._cue=void 0,this._badValueForSameId=void 0,this.tagAnchor=(null==t?void 0:t.tagAnchor)||null,this.tagOrder=null!=(i=null==t?void 0:t.tagOrder)?i:r,t){var a=t.attr;for(var s in a)if(Object.prototype.hasOwnProperty.call(e,s)&&e[s]!==a[s]){j.warn('DATERANGE tag attribute: "'+s+'" does not match for tags with ID: "'+e.ID+'"'),this._badValueForSameId=s;break}e=n(new Bt({}),a,e)}if(this.attr=e,t?(this._startDate=t._startDate,this._cue=t._cue,this._endDate=t._endDate,this._dateAtEnd=t._dateAtEnd):this._startDate=new Date(e["START-DATE"]),"END-DATE"in this.attr){var o=(null==t?void 0:t.endDate)||new Date(this.attr["END-DATE"]);L(o.getTime())&&(this._endDate=o)}}),[{key:"id",get:function(){return this.attr.ID}},{key:"class",get:function(){return this.attr.CLASS}},{key:"cue",get:function(){var e=this._cue;return void 0===e?this._cue=this.attr.enumeratedStringList(this.attr.CUE?"CUE":"X-CUE",{pre:!1,post:!1,once:!1}):e}},{key:"startTime",get:function(){var e=this.tagAnchor;return null===e||null===e.programDateTime?(j.warn('Expected tagAnchor Fragment with PDT set for DateRange "'+this.id+'": '+e),NaN):e.start+(this.startDate.getTime()-e.programDateTime)/1e3}},{key:"startDate",get:function(){return this._startDate}},{key:"endDate",get:function(){var e=this._endDate||this._dateAtEnd;if(e)return e;var t=this.duration;return null!==t?this._dateAtEnd=new Date(this._startDate.getTime()+1e3*t):null}},{key:"duration",get:function(){if("DURATION"in this.attr){var e=this.attr.decimalFloatingPoint("DURATION");if(L(e))return e}else if(this._endDate)return(this._endDate.getTime()-this._startDate.getTime())/1e3;return null}},{key:"plannedDuration",get:function(){return"PLANNED-DURATION"in this.attr?this.attr.decimalFloatingPoint("PLANNED-DURATION"):null}},{key:"endOnNext",get:function(){return this.attr.bool("END-ON-NEXT")}},{key:"isInterstitial",get:function(){return"com.apple.hls.interstitial"===this.class}},{key:"isValid",get:function(){return!!this.id&&!this._badValueForSameId&&L(this.startDate.getTime())&&(null===this.duration||this.duration>=0)&&(!this.endOnNext||!!this.class)&&(!this.attr.CUE||!this.cue.pre&&!this.cue.post||this.cue.pre!==this.cue.post)&&(!this.isInterstitial||"X-ASSET-URI"in this.attr||"X-ASSET-LIST"in this.attr)}}])}(),Vt=function(){function e(e){this.PTSKnown=!1,this.alignedSliding=!1,this.averagetargetduration=void 0,this.endCC=0,this.endSN=0,this.fragments=void 0,this.fragmentHint=void 0,this.partList=null,this.dateRanges=void 0,this.dateRangeTagCount=0,this.live=!0,this.requestScheduled=-1,this.ageHeader=0,this.advancedDateTime=void 0,this.updated=!0,this.advanced=!0,this.misses=0,this.startCC=0,this.startSN=0,this.startTimeOffset=null,this.targetduration=0,this.totalduration=0,this.type=null,this.url=void 0,this.m3u8="",this.version=null,this.canBlockReload=!1,this.canSkipUntil=0,this.canSkipDateRanges=!1,this.skippedSegments=0,this.recentlyRemovedDateranges=void 0,this.partHoldBack=0,this.holdBack=0,this.partTarget=0,this.preloadHint=void 0,this.renditionReports=void 0,this.tuneInGoal=0,this.deltaUpdateFailed=void 0,this.driftStartTime=0,this.driftEndTime=0,this.driftStart=0,this.driftEnd=0,this.encryptedFragments=void 0,this.playlistParsingError=null,this.variableList=null,this.hasVariableRefs=!1,this.appliedTimelineOffset=void 0,this.fragments=[],this.encryptedFragments=[],this.dateRanges={},this.url=e}return e.prototype.reloaded=function(e){if(!e)return this.advanced=!0,void(this.updated=!0);var t=this.lastPartSn-e.lastPartSn,r=this.lastPartIndex-e.lastPartIndex;this.updated=this.endSN!==e.endSN||!!r||!!t||!this.live,this.advanced=this.endSN>e.endSN||t>0||0===t&&r>0,this.updated||this.advanced?this.misses=Math.floor(.6*e.misses):this.misses=e.misses+1},i(e,[{key:"hasProgramDateTime",get:function(){return!!this.fragments.length&&L(this.fragments[this.fragments.length-1].programDateTime)}},{key:"levelTargetDuration",get:function(){return this.averagetargetduration||this.targetduration||10}},{key:"drift",get:function(){var e=this.driftEndTime-this.driftStartTime;return e>0?1e3*(this.driftEnd-this.driftStart)/e:1}},{key:"edge",get:function(){return this.partEnd||this.fragmentEnd}},{key:"partEnd",get:function(){var e;return null!=(e=this.partList)&&e.length?this.partList[this.partList.length-1].end:this.fragmentEnd}},{key:"fragmentEnd",get:function(){return this.fragments.length?this.fragments[this.fragments.length-1].end:0}},{key:"fragmentStart",get:function(){return this.fragments.length?this.fragments[0].start:0}},{key:"age",get:function(){return this.advancedDateTime?Math.max(Date.now()-this.advancedDateTime,0)/1e3:0}},{key:"lastPartIndex",get:function(){var e;return null!=(e=this.partList)&&e.length?this.partList[this.partList.length-1].index:-1}},{key:"maxPartIndex",get:function(){var e=this.partList;if(e){var t=this.lastPartIndex;if(-1!==t){for(var r=e.length;r--;)if(e[r].index>t)return e[r].index;return t}}return 0}},{key:"lastPartSn",get:function(){var e;return null!=(e=this.partList)&&e.length?this.partList[this.partList.length-1].fragment.sn:this.endSN}},{key:"expired",get:function(){if(this.live&&this.age&&this.misses<3){var e=this.partEnd-this.fragmentStart;return this.age>Math.max(e,this.totalduration)+this.levelTargetDuration}return!1}}])}(),Ht=0,Kt=1;function Wt(e){return"AES-128"===e||"AES-256"===e||"AES-256-CTR"===e}function Yt(e){switch(e){case"AES-128":case"AES-256":return Ht;case"AES-256-CTR":return Kt;default:throw new Error("invalid full segment method "+e)}}var jt=function(){function e(e,t,r,i,a,n){void 0===i&&(i=[1]),void 0===a&&(a=null),this.uri=void 0,this.method=void 0,this.keyFormat=void 0,this.keyFormatVersions=void 0,this.encrypted=void 0,this.isCommonEncryption=void 0,this.iv=null,this.key=null,this.keyId=null,this.pssh=null,this.method=e,this.uri=t,this.keyFormat=r,this.keyFormatVersions=i,this.iv=a,this.encrypted=!!e&&"NONE"!==e,this.isCommonEncryption=this.encrypted&&!Wt(e),null!=n&&n.startsWith("0x")&&(this.keyId=new Uint8Array(Z(n)))}e.clearKeyUriToKeyIdMap=function(){};var t=e.prototype;return t.matches=function(e){var t,r;return e.uri===this.uri&&e.method===this.method&&e.encrypted===this.encrypted&&e.keyFormat===this.keyFormat&&e.keyFormatVersions.join(",")===this.keyFormatVersions.join(",")&&(null==(t=e.iv)?void 0:t.join(","))===(null==(r=this.iv)?void 0:r.join(","))},t.isSupported=function(){if(this.method){if(Wt(this.method)||"NONE"===this.method)return!0;if("identity"===this.keyFormat)return"SAMPLE-AES"===this.method}return!1},t.getDecryptData=function(t){if(!this.encrypted||!this.uri)return null;if(Wt(this.method)&&this.uri&&!this.iv){"number"!=typeof t&&(j.warn('missing IV for initialization segment with method="'+this.method+'" - compliance issue'),t=0);var r=function(e){for(var t=new Uint8Array(16),r=12;r<16;r++)t[r]=e>>8*(15-r)&255;return t}(t);return new e(this.method,this.uri,"identity",this.keyFormatVersions,r)}return this},e}(),qt=/#EXT-X-STREAM-INF:([^\r\n]*)(?:[\r\n](?:#[^\r\n]*)?)*([^\r\n]+)|#EXT-X-(SESSION-DATA|SESSION-KEY|DEFINE|CONTENT-STEERING|START):([^\r\n]*)[\r\n]+/g,Xt=/#EXT-X-MEDIA:(.*)/g,zt=/^#EXT(?:INF|-X-TARGETDURATION):/m,Qt=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/(?!#) *(\S[^\r\n]*)/.source,/#.*/.source].join("|"),"g"),$t=new RegExp([/#EXT-X-(PROGRAM-DATE-TIME|BYTERANGE|DATERANGE|DEFINE|KEY|MAP|PART|PART-INF|PLAYLIST-TYPE|PRELOAD-HINT|RENDITION-REPORT|SERVER-CONTROL|SKIP|START):(.+)/.source,/#EXT-X-(BITRATE|DISCONTINUITY-SEQUENCE|MEDIA-SEQUENCE|TARGETDURATION|VERSION): *(\d+)/.source,/#EXT-X-(DISCONTINUITY|ENDLIST|GAP|INDEPENDENT-SEGMENTS)/.source,/(#)([^:]*):(.*)/.source,/(#)(.*)(?:.*)\r?\n?/.source].join("|")),Zt=function(){function e(){}return e.findGroup=function(e,t){for(var r=0;r<e.length;r++){var i=e[r];if(i.id===t)return i}},e.resolve=function(e,t){return S.buildAbsoluteURL(t,e,{alwaysNormalize:!0})},e.isMediaPlaylist=function(e){return zt.test(e)},e.parseMasterPlaylist=function(t,r){var i,a={contentSteering:null,levels:[],playlistParsingError:null,sessionData:null,sessionKeys:null,startTimeOffset:null,variableList:null,hasVariableRefs:!1},n=[];for(qt.lastIndex=0;null!=(i=qt.exec(t));)if(i[1]){var s,o=new Bt(i[1],a),l=i[2],u={attrs:o,bitrate:o.decimalInteger("BANDWIDTH")||o.decimalInteger("AVERAGE-BANDWIDTH"),name:o.NAME,url:e.resolve(l,r)},d=o.decimalResolution("RESOLUTION");d&&(u.width=d.width,u.height=d.height),ir(o.CODECS,u);var h=o["SUPPLEMENTAL-CODECS"];h&&(u.supplemental={},ir(h,u.supplemental)),null!=(s=u.unknownCodecs)&&s.length||n.push(u),a.levels.push(u)}else if(i[3]){var f=i[3],c=i[4];switch(f){case"SESSION-DATA":var v=new Bt(c,a),g=v["DATA-ID"];g&&(null===a.sessionData&&(a.sessionData={}),a.sessionData[g]=v);break;case"SESSION-KEY":var m=tr(c,r,a);m.encrypted&&m.isSupported()?(null===a.sessionKeys&&(a.sessionKeys=[]),a.sessionKeys.push(m)):j.warn('[Keys] Ignoring invalid EXT-X-SESSION-KEY tag: "'+c+'"');break;case"DEFINE":break;case"CONTENT-STEERING":var p=new Bt(c,a);a.contentSteering={uri:e.resolve(p["SERVER-URI"],r),pathwayId:p["PATHWAY-ID"]||"."};break;case"START":a.startTimeOffset=rr(c)}}var y=n.length>0&&n.length<a.levels.length;return a.levels=y?n:a.levels,0===a.levels.length&&(a.playlistParsingError=new Error("no levels found in manifest")),a},e.parseMasterPlaylistMedia=function(t,r,i){var a,n={},s=i.levels,o={AUDIO:s.map((function(e){return{id:e.attrs.AUDIO,audioCodec:e.audioCodec}})),SUBTITLES:s.map((function(e){return{id:e.attrs.SUBTITLES,textCodec:e.textCodec}})),"CLOSED-CAPTIONS":[]},l=0;for(Xt.lastIndex=0;null!==(a=Xt.exec(t));){var u=new Bt(a[1],i),d=u.TYPE;if(d){var h=o[d],f=n[d]||[];n[d]=f;var c=u.LANGUAGE,v=u["ASSOC-LANGUAGE"],g=u.CHANNELS,m=u.CHARACTERISTICS,p=u["INSTREAM-ID"],y={attrs:u,bitrate:0,id:l++,groupId:u["GROUP-ID"]||"",name:u.NAME||c||"",type:d,default:u.bool("DEFAULT"),autoselect:u.bool("AUTOSELECT"),forced:u.bool("FORCED"),lang:c,url:u.URI?e.resolve(u.URI,r):""};if(v&&(y.assocLang=v),g&&(y.channels=g),m&&(y.characteristics=m),p&&(y.instreamId=p),null!=h&&h.length){var E=e.findGroup(h,y.groupId)||h[0];ar(y,E,"audioCodec"),ar(y,E,"textCodec")}f.push(y)}}return n},e.parseLevelPlaylist=function(e,t,r,i,a,s){var o,l,u,d,h,f={url:t},c=new Vt(t),v=c.fragments,g=[],m=null,p=0,y=0,E=0,T=0,S=0,R=null,A=new ne(i,f),b=-1,k=!1,D=null;if(Qt.lastIndex=0,c.m3u8=e,c.hasVariableRefs=!1,"#EXTM3U"!==(null==(o=Qt.exec(e))?void 0:o[0]))return c.playlistParsingError=new Error("Missing format identifier #EXTM3U"),c;for(;null!==(l=Qt.exec(e));){k&&(k=!1,(A=new ne(i,f)).playlistOffset=E,A.setStart(E),A.sn=p,A.cc=T,S&&(A.bitrate=S),A.level=r,m&&(A.initSegment=m,m.rawProgramDateTime&&(A.rawProgramDateTime=m.rawProgramDateTime,m.rawProgramDateTime=null),D&&(A.setByteRange(D),D=null)));var _=l[1];if(_){A.duration=parseFloat(_);var I=(" "+l[2]).slice(1);A.title=I||null,A.tagList.push(I?["INF",_,I]:["INF",_])}else if(l[3]){if(L(A.duration)){A.playlistOffset=E,A.setStart(E),d&&or(A,d,c),A.sn=p,A.level=r,A.cc=T,v.push(A);var C=(" "+l[3]).slice(1);A.relurl=C,nr(A,R,g),R=A,E+=A.duration,p++,y=0,k=!0}}else{if(!(l=l[0].match($t))){j.warn("No matches on slow regex match for level playlist!");continue}for(u=1;u<l.length&&void 0===l[u];u++);var P=(" "+l[u]).slice(1),x=(" "+l[u+1]).slice(1),w=l[u+2]?(" "+l[u+2]).slice(1):null;switch(P){case"BYTERANGE":R?A.setByteRange(x,R):A.setByteRange(x);break;case"PROGRAM-DATE-TIME":A.rawProgramDateTime=x,A.tagList.push(["PROGRAM-DATE-TIME",x]),-1===b&&(b=v.length);break;case"PLAYLIST-TYPE":c.type&&lr(c,P,l),c.type=x.toUpperCase();break;case"MEDIA-SEQUENCE":0!==c.startSN?lr(c,P,l):v.length>0&&ur(c,P,l),p=c.startSN=parseInt(x);break;case"SKIP":c.skippedSegments&&lr(c,P,l);var O=new Bt(x,c),F=O.decimalInteger("SKIPPED-SEGMENTS");if(L(F)){c.skippedSegments+=F;for(var M=F;M--;)v.push(null);p+=F}var N=O.enumeratedString("RECENTLY-REMOVED-DATERANGES");N&&(c.recentlyRemovedDateranges=(c.recentlyRemovedDateranges||[]).concat(N.split("\t")));break;case"TARGETDURATION":0!==c.targetduration&&lr(c,P,l),c.targetduration=Math.max(parseInt(x),1);break;case"VERSION":null!==c.version&&lr(c,P,l),c.version=parseInt(x);break;case"INDEPENDENT-SEGMENTS":case"DEFINE":break;case"ENDLIST":c.live||lr(c,P,l),c.live=!1;break;case"#":(x||w)&&A.tagList.push(w?[x,w]:[x]);break;case"DISCONTINUITY":T++,A.tagList.push(["DIS"]);break;case"GAP":A.gap=!0,A.tagList.push([P]);break;case"BITRATE":A.tagList.push([P,x]),S=1e3*parseInt(x),L(S)?A.bitrate=S:S=0;break;case"DATERANGE":var B=new Bt(x,c),U=new Gt(B,c.dateRanges[B.ID],c.dateRangeTagCount);c.dateRangeTagCount++,U.isValid||c.skippedSegments?c.dateRanges[U.id]=U:j.warn('Ignoring invalid DATERANGE tag: "'+x+'"'),A.tagList.push(["EXT-X-DATERANGE",x]);break;case"DISCONTINUITY-SEQUENCE":0!==c.startCC?lr(c,P,l):v.length>0&&ur(c,P,l),c.startCC=T=parseInt(x);break;case"KEY":var G=tr(x,t,c);if(G.isSupported()){if("NONE"===G.method){d=void 0;break}d||(d={});var V=d[G.keyFormat];null!=V&&V.matches(G)||(V&&(d=n({},d)),d[G.keyFormat]=G)}else j.warn('[Keys] Ignoring invalid EXT-X-KEY tag: "'+x+'"');break;case"START":c.startTimeOffset=rr(x);break;case"MAP":var H=new Bt(x,c);if(A.duration){var K=new ne(i,f);sr(K,H,r,d),m=K,A.initSegment=m,m.rawProgramDateTime&&!A.rawProgramDateTime&&(A.rawProgramDateTime=m.rawProgramDateTime)}else{var W=A.byteRangeEndOffset;if(W){var Y=A.byteRangeStartOffset;D=W-Y+"@"+Y}else D=null;sr(A,H,r,d),m=A,k=!0}m.cc=T;break;case"SERVER-CONTROL":h&&lr(c,P,l),h=new Bt(x),c.canBlockReload=h.bool("CAN-BLOCK-RELOAD"),c.canSkipUntil=h.optionalFloat("CAN-SKIP-UNTIL",0),c.canSkipDateRanges=c.canSkipUntil>0&&h.bool("CAN-SKIP-DATERANGES"),c.partHoldBack=h.optionalFloat("PART-HOLD-BACK",0),c.holdBack=h.optionalFloat("HOLD-BACK",0);break;case"PART-INF":c.partTarget&&lr(c,P,l);var q=new Bt(x);c.partTarget=q.decimalFloatingPoint("PART-TARGET");break;case"PART":var X=c.partList;X||(X=c.partList=[]);var z=y>0?X[X.length-1]:void 0,Q=y++,$=new Bt(x,c),Z=new se($,A,f,Q,z);X.push(Z),A.duration+=Z.duration;break;case"PRELOAD-HINT":var J=new Bt(x,c);c.preloadHint=J;break;case"RENDITION-REPORT":var ee=new Bt(x,c);c.renditionReports=c.renditionReports||[],c.renditionReports.push(ee);break;default:j.warn("line parsed but not handled: "+l)}}}R&&!R.relurl?(v.pop(),E-=R.duration,c.partList&&(c.fragmentHint=R)):c.partList&&(nr(A,R,g),A.cc=T,c.fragmentHint=A,d&&or(A,d,c)),c.targetduration||(c.playlistParsingError=new Error("#EXT-X-TARGETDURATION is required"));var te=v.length,re=v[0],ie=v[te-1];if((E+=c.skippedSegments*c.targetduration)>0&&te&&ie){c.averagetargetduration=E/te;var ae=ie.sn;c.endSN="initSegment"!==ae?ae:0,c.live||(ie.endList=!0),b>0&&(function(e,t){for(var r=e[t],i=t;i--;){var a=e[i];if(!a)return;a.programDateTime=r.programDateTime-1e3*a.duration,r=a}}(v,b),re&&g.unshift(re))}return c.fragmentHint&&(E+=c.fragmentHint.duration),c.totalduration=E,g.length&&c.dateRangeTagCount&&re&&Jt(g,c),c.endCC=T,c},e}();function Jt(e,t){var r=e.length;if(r)for(var i=e[r-1],a=t.live?1/0:t.totalduration,n=Object.keys(t.dateRanges),s=n.length;s--;){var o=t.dateRanges[n[s]],l=o.startDate.getTime();o.tagAnchor=i.ref;for(var u=r;u--;){var d;if((null==(d=e[u])?void 0:d.sn)<t.startSN)break;var h=er(t,l,e,u,a);if(-1!==h){o.tagAnchor=t.fragments[h].ref;break}}}}function er(e,t,r,i,a){var n=r[i];if(n){var s,o=n.programDateTime;if((t>=o||0===i)&&t<=o+1e3*(((null==(s=r[i+1])?void 0:s.start)||a)-n.start)){var l=r[i].sn-e.startSN;if(l<0)return-1;var u=e.fragments;if(u.length>r.length)for(var d=(r[i+1]||u[u.length-1]).sn-e.startSN;d>l;d--){var h=u[d].programDateTime;if(t>=h&&t<h+1e3*u[d].duration)return d}return l}}return-1}function tr(e,t,r){var i,a,n=new Bt(e,r),s=null!=(i=n.METHOD)?i:"",o=n.URI,l=n.hexadecimalInteger("IV"),u=n.KEYFORMATVERSIONS,d=null!=(a=n.KEYFORMAT)?a:"identity";o&&n.IV&&!l&&j.error("Invalid IV: "+n.IV);var h=o?Zt.resolve(o,t):"",f=(u||"1").split("/").map(Number).filter(Number.isFinite);return new jt(s,h,d,f,l,n.KEYID)}function rr(e){var t=new Bt(e).decimalFloatingPoint("TIME-OFFSET");return L(t)?t:null}function ir(e,t){var r=(e||"").split(/[ ,]+/).filter((function(e){return e}));["video","audio","text"].forEach((function(e){var i=r.filter((function(t){return we(t,e)}));i.length&&(t[e+"Codec"]=i.map((function(e){return e.split("/")[0]})).join(","),r=r.filter((function(e){return-1===i.indexOf(e)})))})),t.unknownCodecs=r}function ar(e,t,r){var i=t[r];i&&(e[r]=i)}function nr(e,t,r){e.rawProgramDateTime?r.push(e):null!=t&&t.programDateTime&&(e.programDateTime=t.endProgramDateTime)}function sr(e,t,r,i){e.relurl=t.URI,t.BYTERANGE&&e.setByteRange(t.BYTERANGE),e.level=r,e.sn="initSegment",i&&(e.levelkeys=i),e.initSegment=null}function or(e,t,r){e.levelkeys=t;var i=r.encryptedFragments;i.length&&i[i.length-1].levelkeys===t||!Object.keys(t).some((function(e){return t[e].isCommonEncryption}))||i.push(e)}function lr(e,t,r){e.playlistParsingError=new Error("#EXT-X-"+t+" must not appear more than once ("+r[0]+")")}function ur(e,t,r){e.playlistParsingError=new Error("#EXT-X-"+t+" must appear before the first Media Segment ("+r[0]+")")}function dr(e,t){var r=t.startPTS;if(L(r)){var i,a=0;t.sn>e.sn?(a=r-e.start,i=e):(a=e.start-r,i=t),i.duration!==a&&i.setDuration(a)}else t.sn>e.sn?e.cc===t.cc&&e.minEndPTS?t.setStart(e.start+(e.minEndPTS-e.start)):t.setStart(e.start+e.duration):t.setStart(Math.max(e.start-t.duration,0))}function hr(e,t,r,i,a,n,s){i-r<=0&&(s.warn("Fragment should have a positive duration",t),i=r+t.duration,n=a+t.duration);var o=r,l=i,u=t.startPTS,d=t.endPTS;if(L(u)){var h=Math.abs(u-r);e&&h>e.totalduration?s.warn("media timestamps and playlist times differ by "+h+"s for level "+t.level+" "+e.url):L(t.deltaPTS)?t.deltaPTS=Math.max(h,t.deltaPTS):t.deltaPTS=h,o=Math.max(r,u),r=Math.min(r,u),a=void 0!==t.startDTS?Math.min(a,t.startDTS):a,l=Math.min(i,d),i=Math.max(i,d),n=void 0!==t.endDTS?Math.max(n,t.endDTS):n}var f=r-t.start;0!==t.start&&t.setStart(r),t.setDuration(i-t.start),t.startPTS=r,t.maxStartPTS=o,t.startDTS=a,t.endPTS=i,t.minEndPTS=l,t.endDTS=n;var c,v=t.sn;if(!e||v<e.startSN||v>e.endSN)return 0;var g=v-e.startSN,m=e.fragments;for(m[g]=t,c=g;c>0;c--)dr(m[c],m[c-1]);for(c=g;c<m.length-1;c++)dr(m[c],m[c+1]);return e.fragmentHint&&dr(m[m.length-1],e.fragmentHint),e.PTSKnown=e.alignedSliding=!0,f}function fr(e,t,r){if(e!==t){for(var i,a=null,s=e.fragments,o=s.length-1;o>=0;o--){var l=s[o].initSegment;if(l){a=l;break}}e.fragmentHint&&delete e.fragmentHint.endPTS,function(e,t,r){for(var i=t.skippedSegments,a=Math.max(e.startSN,t.startSN)-t.startSN,n=(e.fragmentHint?1:0)+(i?t.endSN:Math.min(e.endSN,t.endSN))-t.startSN,s=t.startSN-e.startSN,o=t.fragmentHint?t.fragments.concat(t.fragmentHint):t.fragments,l=e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments,u=a;u<=n;u++){var d=l[s+u],h=o[u];if(i&&!h&&d&&(h=t.fragments[u]=d),d&&h){if(r(d,h,u,o),d.url&&d.url!==h.url)return void(t.playlistParsingError=cr("media sequence mismatch "+h.sn+":",e,t,0,h));if(d.cc!==h.cc)return void(t.playlistParsingError=cr("discontinuity sequence mismatch ("+d.cc+"!="+h.cc+")",e,t,0,h))}}}(e,t,(function(e,r,n,s){if((!t.startCC||t.skippedSegments)&&r.cc!==e.cc){for(var o=e.cc-r.cc,l=n;l<s.length;l++)s[l].cc+=o;t.endCC=s[s.length-1].cc}L(e.startPTS)&&L(e.endPTS)&&(r.setStart(r.startPTS=e.startPTS),r.startDTS=e.startDTS,r.maxStartPTS=e.maxStartPTS,r.endPTS=e.endPTS,r.endDTS=e.endDTS,r.minEndPTS=e.minEndPTS,r.setDuration(e.endPTS-e.startPTS),r.duration&&(i=r),t.PTSKnown=t.alignedSliding=!0),e.hasStreams&&(r.elementaryStreams=e.elementaryStreams),r.loader=e.loader,e.hasStats&&(r.stats=e.stats),e.initSegment&&(r.initSegment=e.initSegment,a=e.initSegment)}));var u=t.fragments,d=t.fragmentHint?u.concat(t.fragmentHint):u;if(a&&d.forEach((function(e){var t;!e||e.initSegment&&e.initSegment.relurl!==(null==(t=a)?void 0:t.relurl)||(e.initSegment=a)})),t.skippedSegments){if(t.deltaUpdateFailed=u.some((function(e){return!e})),t.deltaUpdateFailed){r.warn("[level-helper] Previous playlist missing segments skipped in delta playlist");for(var h=t.skippedSegments;h--;)u.shift();t.startSN=u[0].sn}else{t.canSkipDateRanges&&(t.dateRanges=function(e,t,r){var i=t.dateRanges,a=t.recentlyRemovedDateranges,s=n({},e);a&&a.forEach((function(e){delete s[e]}));var o=Object.keys(s).length;return o&&Object.keys(i).forEach((function(e){var t=s[e],a=new Gt(i[e].attr,t);a.isValid?(s[e]=a,t||(a.tagOrder+=o)):r.warn('Ignoring invalid Playlist Delta Update DATERANGE tag: "'+it(i[e].attr)+'"')})),s}(e.dateRanges,t,r));var f=e.fragments.filter((function(e){return e.rawProgramDateTime}));if(e.hasProgramDateTime&&!t.hasProgramDateTime)for(var c=1;c<d.length;c++)null===d[c].programDateTime&&nr(d[c],d[c-1],f);Jt(f,t)}t.endCC=u[u.length-1].cc}if(!t.startCC){var v,g=mr(e,t.startSN-1);t.startCC=null!=(v=null==g?void 0:g.cc)?v:u[0].cc}!function(e,t,r){if(e&&t)for(var i=0,a=0,n=e.length;a<=n;a++){var s=e[a],o=t[a+i];s&&o&&s.index===o.index&&s.fragment.sn===o.fragment.sn?r(s,o):i--}}(e.partList,t.partList,(function(e,t){t.elementaryStreams=e.elementaryStreams,t.stats=e.stats})),i?hr(t,i,i.startPTS,i.endPTS,i.startDTS,i.endDTS,r):vr(e,t),u.length&&(t.totalduration=t.edge-u[0].start),t.driftStartTime=e.driftStartTime,t.driftStart=e.driftStart;var m=t.advancedDateTime;if(t.advanced&&m){var p=t.edge;t.driftStart||(t.driftStartTime=m,t.driftStart=p),t.driftEndTime=m,t.driftEnd=p}else t.driftEndTime=e.driftEndTime,t.driftEnd=e.driftEnd,t.advancedDateTime=e.advancedDateTime;-1===t.requestScheduled&&(t.requestScheduled=e.requestScheduled)}}function cr(e,t,r,i,a){return new Error(e+" "+a.url+"\nPlaylist starting @"+t.startSN+"\n"+t.m3u8+"\n\nPlaylist starting @"+r.startSN+"\n"+r.m3u8)}function vr(e,t,r){void 0===r&&(r=!0);var i=t.startSN+t.skippedSegments-e.startSN,a=e.fragments,n=i>=0,s=0;if(n&&i<a.length)s=a[i].start;else if(n&&t.startSN===e.endSN+1)s=e.fragmentEnd;else if(n&&r)s=e.fragmentStart+i*t.levelTargetDuration;else{if(t.skippedSegments||0!==t.fragmentStart)return;s=e.fragmentStart}!function(e,t){if(t){for(var r=e.fragments,i=e.skippedSegments;i<r.length;i++)r[i].addStart(t);e.fragmentHint&&e.fragmentHint.addStart(t)}}(t,s)}function gr(e,t){void 0===t&&(t=1/0);var r=1e3*e.targetduration;if(e.updated){var i=e.fragments;if(i.length&&4*r>t){var a=1e3*i[i.length-1].duration;a<r&&(r=a)}}else r/=2;return Math.round(r)}function mr(e,t,r){if(!e)return null;var i=e.fragments[t-e.startSN];return i||((i=e.fragmentHint)&&i.sn===t?i:t<e.startSN&&r&&r.sn===t?r:null)}function pr(e,t,r){return e?yr(e.partList,t,r):null}function yr(e,t,r){if(e)for(var i=e.length;i--;){var a=e[i];if(a.index===r&&a.fragment.sn===t)return a}return null}function Er(e){e.forEach((function(e,t){var r;null==(r=e.details)||r.fragments.forEach((function(e){e.level=t,e.initSegment&&(e.initSegment.level=t)}))}))}var Tr=function(e){function t(t){var r;return(r=e.call(this,"content-steering",t.logger)||this).hls=void 0,r.loader=null,r.uri=null,r.pathwayId=".",r._pathwayPriority=null,r.timeToLoad=300,r.reloadTimer=-1,r.updated=0,r.started=!1,r.enabled=!0,r.levels=null,r.audioTracks=null,r.subtitleTracks=null,r.penalizedPathways={},r.hls=t,r.registerListeners(),r}o(t,e);var r=t.prototype;return r.registerListeners=function(){var e=this.hls;e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(D.MANIFEST_PARSED,this.onManifestParsed,this),e.on(D.ERROR,this.onError,this)},r.unregisterListeners=function(){var e=this.hls;e&&(e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(D.MANIFEST_PARSED,this.onManifestParsed,this),e.off(D.ERROR,this.onError,this))},r.pathways=function(){return(this.levels||[]).reduce((function(e,t){return-1===e.indexOf(t.pathwayId)&&e.push(t.pathwayId),e}),[])},r.startLoad=function(){if(this.started=!0,this.clearTimeout(),this.enabled&&this.uri){if(this.updated){var e=1e3*this.timeToLoad-(performance.now()-this.updated);if(e>0)return void this.scheduleRefresh(this.uri,e)}this.loadSteeringManifest(this.uri)}},r.stopLoad=function(){this.started=!1,this.loader&&(this.loader.destroy(),this.loader=null),this.clearTimeout()},r.clearTimeout=function(){-1!==this.reloadTimer&&(self.clearTimeout(this.reloadTimer),this.reloadTimer=-1)},r.destroy=function(){this.unregisterListeners(),this.stopLoad(),this.hls=null,this.levels=this.audioTracks=this.subtitleTracks=null},r.removeLevel=function(e){var t=this.levels;t&&(this.levels=t.filter((function(t){return t!==e})))},r.onManifestLoading=function(){this.stopLoad(),this.enabled=!0,this.timeToLoad=300,this.updated=0,this.uri=null,this.pathwayId=".",this.levels=this.audioTracks=this.subtitleTracks=null},r.onManifestLoaded=function(e,t){var r=t.contentSteering;null!==r&&(this.pathwayId=r.pathwayId,this.uri=r.uri,this.started&&this.startLoad())},r.onManifestParsed=function(e,t){this.audioTracks=t.audioTracks,this.subtitleTracks=t.subtitleTracks},r.onError=function(e,t){var r=t.errorAction;if((null==r?void 0:r.action)===Et&&r.flags===Rt){var i=this.levels,a=this._pathwayPriority,n=this.pathwayId;if(t.context){var s=t.context,o=s.groupId,l=s.pathwayId,u=s.type;o&&i?n=this.getPathwayForGroupId(o,u,n):l&&(n=l)}n in this.penalizedPathways||(this.penalizedPathways[n]=performance.now()),!a&&i&&(a=this.pathways()),a&&a.length>1&&(this.updatePathwayPriority(a),r.resolved=this.pathwayId!==n),t.details!==k.BUFFER_APPEND_ERROR||t.fatal?r.resolved||this.warn("Could not resolve "+t.details+' ("'+t.error.message+'") with content-steering for Pathway: '+n+" levels: "+(i?i.length:i)+" priorities: "+it(a)+" penalized: "+it(this.penalizedPathways)):r.resolved=!0}},r.filterParsedLevels=function(e){this.levels=e;var t=this.getLevelsForPathway(this.pathwayId);if(0===t.length){var r=e[0].pathwayId;this.log("No levels found in Pathway "+this.pathwayId+'. Setting initial Pathway to "'+r+'"'),t=this.getLevelsForPathway(r),this.pathwayId=r}return t.length!==e.length&&this.log("Found "+t.length+"/"+e.length+' levels in Pathway "'+this.pathwayId+'"'),t},r.getLevelsForPathway=function(e){return null===this.levels?[]:this.levels.filter((function(t){return e===t.pathwayId}))},r.updatePathwayPriority=function(e){var t;this._pathwayPriority=e;var r=this.penalizedPathways,i=performance.now();Object.keys(r).forEach((function(e){i-r[e]>3e5&&delete r[e]}));for(var a=0;a<e.length;a++){var n=e[a];if(!(n in r)){if(n===this.pathwayId)return;var s=this.hls.nextLoadLevel,o=this.hls.levels[s];if((t=this.getLevelsForPathway(n)).length>0){this.log('Setting Pathway to "'+n+'"'),this.pathwayId=n,Er(t),this.hls.trigger(D.LEVELS_UPDATED,{levels:t});var l=this.hls.levels[s];o&&l&&this.levels&&(l.attrs["STABLE-VARIANT-ID"]!==o.attrs["STABLE-VARIANT-ID"]&&l.bitrate!==o.bitrate&&this.log("Unstable Pathways change from bitrate "+o.bitrate+" to "+l.bitrate),this.hls.nextLoadLevel=s);break}}}},r.getPathwayForGroupId=function(e,t,r){for(var i=this.getLevelsForPathway(r).concat(this.levels||[]),a=0;a<i.length;a++)if(t===C&&i[a].hasAudioGroup(e)||t===P&&i[a].hasSubtitleGroup(e))return i[a].pathwayId;return r},r.clonePathways=function(e){var t=this,r=this.levels;if(r){var i={},a={};e.forEach((function(e){var n=e.ID,s=e["BASE-ID"],o=e["URI-REPLACEMENT"];if(!r.some((function(e){return e.pathwayId===n}))){var l=t.getLevelsForPathway(s).map((function(e){var t=new Bt(e.attrs);t["PATHWAY-ID"]=n;var r=t.AUDIO&&t.AUDIO+"_clone_"+n,s=t.SUBTITLES&&t.SUBTITLES+"_clone_"+n;r&&(i[t.AUDIO]=r,t.AUDIO=r),s&&(a[t.SUBTITLES]=s,t.SUBTITLES=s);var l=Lr(e.uri,t["STABLE-VARIANT-ID"],"PER-VARIANT-URIS",o),u=new et({attrs:t,audioCodec:e.audioCodec,bitrate:e.bitrate,height:e.height,name:e.name,url:l,videoCodec:e.videoCodec,width:e.width});if(e.audioGroups)for(var d=1;d<e.audioGroups.length;d++)u.addGroupId("audio",e.audioGroups[d]+"_clone_"+n);if(e.subtitleGroups)for(var h=1;h<e.subtitleGroups.length;h++)u.addGroupId("text",e.subtitleGroups[h]+"_clone_"+n);return u}));r.push.apply(r,l),Sr(t.audioTracks,i,o,n),Sr(t.subtitleTracks,a,o,n)}}))}},r.loadSteeringManifest=function(e){var t,r=this,i=this.hls.config,a=i.loader;this.loader&&this.loader.destroy(),this.loader=new a(i);try{t=new self.URL(e)}catch(t){return this.enabled=!1,void this.log("Failed to parse Steering Manifest URI: "+e)}if("data:"!==t.protocol){var n=0|(this.hls.bandwidthEstimate||i.abrEwmaDefaultEstimate);t.searchParams.set("_HLS_pathway",this.pathwayId),t.searchParams.set("_HLS_throughput",""+n)}var s={responseType:"json",url:t.href},o=i.steeringManifestLoadPolicy.default,l=o.errorRetry||o.timeoutRetry||{},u={loadPolicy:o,timeout:o.maxLoadTimeMs,maxRetry:l.maxNumRetry||0,retryDelay:l.retryDelayMs||0,maxRetryDelay:l.maxRetryDelayMs||0},d={onSuccess:function(e,i,a,n){r.log('Loaded steering manifest: "'+t+'"');var s=e.data;if(1===(null==s?void 0:s.VERSION)){r.updated=performance.now(),r.timeToLoad=s.TTL;var o=s["RELOAD-URI"],l=s["PATHWAY-CLONES"],u=s["PATHWAY-PRIORITY"];if(o)try{r.uri=new self.URL(o,t).href}catch(e){return r.enabled=!1,void r.log("Failed to parse Steering Manifest RELOAD-URI: "+o)}r.scheduleRefresh(r.uri||a.url),l&&r.clonePathways(l);var d={steeringManifest:s,url:t.toString()};r.hls.trigger(D.STEERING_MANIFEST_LOADED,d),u&&r.updatePathwayPriority(u)}else r.log("Steering VERSION "+s.VERSION+" not supported!")},onError:function(e,t,i,a){if(r.log("Error loading steering manifest: "+e.code+" "+e.text+" ("+t.url+")"),r.stopLoad(),410===e.code)return r.enabled=!1,void r.log("Steering manifest "+t.url+" no longer available");var n=1e3*r.timeToLoad;if(429!==e.code)r.scheduleRefresh(r.uri||t.url,n);else{var s=r.loader;if("function"==typeof(null==s?void 0:s.getResponseHeader)){var o=s.getResponseHeader("Retry-After");o&&(n=1e3*parseFloat(o))}r.log("Steering manifest "+t.url+" rate limited")}},onTimeout:function(e,t,i){r.log("Timeout loading steering manifest ("+t.url+")"),r.scheduleRefresh(r.uri||t.url)}};this.log("Requesting steering manifest: "+t),this.loader.load(s,u,d)},r.scheduleRefresh=function(e,t){var r=this;void 0===t&&(t=1e3*this.timeToLoad),this.clearTimeout(),this.reloadTimer=self.setTimeout((function(){var t,i=null==(t=r.hls)?void 0:t.media;!i||i.ended?r.scheduleRefresh(e,1e3*r.timeToLoad):r.loadSteeringManifest(e)}),t)},i(t,[{key:"pathwayPriority",get:function(){return this._pathwayPriority},set:function(e){this.updatePathwayPriority(e)}}])}(N);function Sr(e,t,r,i){e&&Object.keys(t).forEach((function(a){var s=e.filter((function(e){return e.groupId===a})).map((function(e){var s=n({},e);return s.details=void 0,s.attrs=new Bt(s.attrs),s.url=s.attrs.URI=Lr(e.url,e.attrs["STABLE-RENDITION-ID"],"PER-RENDITION-URIS",r),s.groupId=s.attrs["GROUP-ID"]=t[a],s.attrs["PATHWAY-ID"]=i,s}));e.push.apply(e,s)}))}function Lr(e,t,r,i){var a,n=i.HOST,s=i.PARAMS,o=i[r];t&&(a=null==o?void 0:o[t])&&(e=a);var l=new self.URL(e);return n&&!a&&(l.host=n),s&&Object.keys(s).sort().forEach((function(e){e&&l.searchParams.set(e,s[e])})),l.href}var Rr=function(){function e(e){this.hls=void 0,this.isVideoPlaybackQualityAvailable=!1,this.timer=void 0,this.media=null,this.lastTime=void 0,this.lastDroppedFrames=0,this.lastDecodedFrames=0,this.streamController=void 0,this.hls=e,this.registerListeners()}var t=e.prototype;return t.setStreamController=function(e){this.streamController=e},t.registerListeners=function(){this.hls.on(D.MEDIA_ATTACHING,this.onMediaAttaching,this),this.hls.on(D.MEDIA_DETACHING,this.onMediaDetaching,this)},t.unregisterListeners=function(){this.hls.off(D.MEDIA_ATTACHING,this.onMediaAttaching,this),this.hls.off(D.MEDIA_DETACHING,this.onMediaDetaching,this)},t.destroy=function(){this.timer&&clearInterval(this.timer),this.unregisterListeners(),this.isVideoPlaybackQualityAvailable=!1,this.media=null},t.onMediaAttaching=function(e,t){var r=this.hls.config;if(r.capLevelOnFPSDrop){var i=t.media instanceof self.HTMLVideoElement?t.media:null;this.media=i,i&&"function"==typeof i.getVideoPlaybackQuality&&(this.isVideoPlaybackQualityAvailable=!0),self.clearInterval(this.timer),this.timer=self.setInterval(this.checkFPSInterval.bind(this),r.fpsDroppedMonitoringPeriod)}},t.onMediaDetaching=function(){this.media=null},t.checkFPS=function(e,t,r){var i=performance.now();if(t){if(this.lastTime){var a=i-this.lastTime,n=r-this.lastDroppedFrames,s=t-this.lastDecodedFrames,o=1e3*n/a,l=this.hls;if(l.trigger(D.FPS_DROP,{currentDropped:n,currentDecoded:s,totalDroppedFrames:r}),o>0&&n>l.config.fpsDroppedMonitoringThreshold*s){var u=l.currentLevel;l.logger.warn("drop FPS ratio greater than max allowed value for currentLevel: "+u),u>0&&(-1===l.autoLevelCapping||l.autoLevelCapping>=u)&&(u-=1,l.trigger(D.FPS_DROP_LEVEL_CAPPING,{level:u,droppedLevel:l.currentLevel}),l.autoLevelCapping=u,this.streamController.nextLevelSwitch())}}this.lastTime=i,this.lastDroppedFrames=r,this.lastDecodedFrames=t}},t.checkFPSInterval=function(){var e=this.media;if(e)if(this.isVideoPlaybackQualityAvailable){var t=e.getVideoPlaybackQuality();this.checkFPS(e,t.totalVideoFrames,t.droppedVideoFrames)}else this.checkFPS(e,e.webkitDecodedFrameCount,e.webkitDroppedFrameCount)},e}(),Ar=function(){function e(){this.chunks=[],this.dataLength=0}var t=e.prototype;return t.push=function(e){this.chunks.push(e),this.dataLength+=e.length},t.flush=function(){var e,t=this.chunks,r=this.dataLength;return t.length?(e=1===t.length?t[0]:function(e,t){for(var r=new Uint8Array(t),i=0,a=0;a<e.length;a++){var n=e[a];r.set(n,i),i+=n.length}return r}(t,r),this.reset(),e):new Uint8Array(0)},t.reset=function(){this.chunks.length=0,this.dataLength=0},e}();function br(e,t){return t+10<=e.length&&51===e[t]&&68===e[t+1]&&73===e[t+2]&&e[t+3]<255&&e[t+4]<255&&e[t+6]<128&&e[t+7]<128&&e[t+8]<128&&e[t+9]<128}function kr(e,t){return t+10<=e.length&&73===e[t]&&68===e[t+1]&&51===e[t+2]&&e[t+3]<255&&e[t+4]<255&&e[t+6]<128&&e[t+7]<128&&e[t+8]<128&&e[t+9]<128}function Dr(e,t){var r=0;return r=(127&e[t])<<21,r|=(127&e[t+1])<<14,r|=(127&e[t+2])<<7,r|=127&e[t+3]}function _r(e,t){for(var r=t,i=0;kr(e,t);)i+=10,i+=Dr(e,t+6),br(e,t+10)&&(i+=10),t+=i;if(i>0)return e.subarray(r,r+i)}function Ir(e,t){return 255===e[t]&&240==(246&e[t+1])}function Cr(e,t){return 1&e[t+1]?7:9}function Pr(e,t){return(3&e[t+3])<<11|e[t+4]<<3|(224&e[t+5])>>>5}function xr(e,t){return t+1<e.length&&Ir(e,t)}function wr(e,t){if(xr(e,t)){var r=Cr(e,t);if(t+r>=e.length)return!1;var i=Pr(e,t);if(i<=r)return!1;var a=t+i;return a===e.length||xr(e,a)}return!1}function Or(e,t,r,i,a){if(!e.samplerate){var s=function(e,t,r,i){var a=t[r+2],n=a>>2&15;if(!(n>12)){var s=1+(a>>6&3),o=t[r+3]>>6&3|(1&a)<<2,l="mp4a.40."+s,u=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350][n],d=n;5!==s&&29!==s||(d-=3);var h=[s<<3|(14&d)>>1,(1&d)<<7|o<<3];return j.log("manifest codec:"+i+", parsed codec:"+l+", channels:"+o+", rate:"+u+" (ADTS object type:"+s+" sampling index:"+n+")"),{config:h,samplerate:u,channelCount:o,codec:l,parsedCodec:l,manifestCodec:i}}var f=new Error("invalid ADTS sampling index:"+n);e.emit(D.ERROR,D.ERROR,{type:b.MEDIA_ERROR,details:k.FRAG_PARSING_ERROR,fatal:!0,error:f,reason:f.message})}(t,r,i,a);if(!s)return;n(e,s)}}function Fr(e){return 9216e4/e}function Mr(e,t,r,i,a){var n,s=i+a*Fr(e.samplerate),o=function(e,t){var r=Cr(e,t);if(t+r<=e.length){var i=Pr(e,t)-r;if(i>0)return{headerLength:r,frameLength:i}}}(t,r);if(o){var l=o.frameLength,u=o.headerLength,d=u+l,h=Math.max(0,r+d-t.length);h?(n=new Uint8Array(d-u)).set(t.subarray(r+u,t.length),0):n=t.subarray(r+u,r+d);var f={unit:n,pts:s};return h||e.samples.push(f),{sample:f,length:d,missing:h}}var c=t.length-r;return(n=new Uint8Array(c)).set(t.subarray(r,t.length),0),{sample:{unit:n,pts:s},length:c,missing:-1}}function Nr(e,t){return kr(e,t)&&Dr(e,t+6)+10<=e.length-t}function Br(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=1/0),function(e,t,r,i){var a=function(e){return e instanceof ArrayBuffer?e:e.buffer}(e),n=1;"BYTES_PER_ELEMENT"in i&&(n=i.BYTES_PER_ELEMENT);var s,o=(s=e)&&s.buffer instanceof ArrayBuffer&&void 0!==s.byteLength&&void 0!==s.byteOffset?e.byteOffset:0,l=(o+e.byteLength)/n,u=(o+t)/n,d=Math.floor(Math.max(0,Math.min(u,l))),h=Math.floor(Math.min(d+Math.max(r,0),l));return new i(a,d,h-d)}(e,t,r,Uint8Array)}function Ur(e){var t={key:e.type,description:"",data:"",mimeType:null,pictureType:null};if(!(e.size<2))if(3===e.data[0]){var r=e.data.subarray(1).indexOf(0);if(-1!==r){var i=Q(Br(e.data,1,r)),a=e.data[2+r],n=e.data.subarray(3+r).indexOf(0);if(-1!==n){var s,o=Q(Br(e.data,3+r,n));return s="--\x3e"===i?Q(Br(e.data,4+r+n)):function(e){return e instanceof ArrayBuffer?e:0==e.byteOffset&&e.byteLength==e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer}(e.data.subarray(4+r+n)),t.mimeType=i,t.pictureType=a,t.description=o,t.data=s,t}}}else console.log("Ignore frame with unrecognized character encoding")}function Gr(e){return"PRIV"===e.type?function(e){if(!(e.size<2)){var t=Q(e.data,!0),r=new Uint8Array(e.data.subarray(t.length+1));return{key:e.type,info:t,data:r.buffer}}}(e):"W"===e.type[0]?function(e){if("WXXX"===e.type){if(e.size<2)return;var t=1,r=Q(e.data.subarray(t),!0);t+=r.length+1;var i=Q(e.data.subarray(t));return{key:e.type,info:r,data:i}}var a=Q(e.data);return{key:e.type,info:"",data:a}}(e):"APIC"===e.type?Ur(e):function(e){if(!(e.size<2)){if("TXXX"===e.type){var t=1,r=Q(e.data.subarray(t),!0);t+=r.length+1;var i=Q(e.data.subarray(t));return{key:e.type,info:r,data:i}}var a=Q(e.data.subarray(1));return{key:e.type,info:"",data:a}}}(e)}function Vr(e){var t=String.fromCharCode(e[0],e[1],e[2],e[3]),r=Dr(e,4);return{type:t,size:r,data:e.subarray(10,10+r)}}var Hr=10,Kr=10;function Wr(e){for(var t=0,r=[];kr(e,t);){var i=Dr(e,t+6);e[t+5]>>6&1&&(t+=Hr);for(var a=(t+=Hr)+i;t+Kr<a;){var n=Vr(e.subarray(t)),s=Gr(n);s&&r.push(s),t+=n.size+Hr}br(e,t)&&(t+=Hr)}return r}function Yr(e){return e&&"PRIV"===e.key&&"com.apple.streaming.transportStreamTimestamp"===e.info}function jr(e){if(8===e.data.byteLength){var t=new Uint8Array(e.data),r=1&t[3],i=(t[4]<<23)+(t[5]<<15)+(t[6]<<7)+t[7];return i/=45,r&&(i+=47721858.84),Math.round(i)}}function qr(e){for(var t=Wr(e),r=0;r<t.length;r++){var i=t[r];if(Yr(i))return jr(i)}}var Xr=function(e){return e.audioId3="org.id3",e.dateRange="com.apple.quicktime.HLS",e.emsg="https://aomedia.org/emsg/ID3",e.misbklv="urn:misb:KLV:bin:1910.1",e}({});function zr(e,t){return void 0===e&&(e=""),void 0===t&&(t=9e4),{type:e,id:-1,pid:-1,inputTimeScale:t,sequenceNumber:-1,samples:[],dropped:0}}var Qr=function(){function e(){this._audioTrack=void 0,this._id3Track=void 0,this.frameIndex=0,this.cachedData=null,this.basePTS=null,this.initPTS=null,this.lastPTS=null}var t=e.prototype;return t.resetInitSegment=function(e,t,r,i){this._id3Track={type:"id3",id:3,pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0}},t.resetTimeStamp=function(e){this.initPTS=e,this.resetContiguity()},t.resetContiguity=function(){this.basePTS=null,this.lastPTS=null,this.frameIndex=0},t.canParse=function(e,t){return!1},t.appendFrame=function(e,t,r){},t.demux=function(e,t){this.cachedData&&(e=be(this.cachedData,e),this.cachedData=null);var r,i=_r(e,0),a=i?i.length:0,n=this._audioTrack,s=this._id3Track,o=i?qr(i):void 0,l=e.length;for((null===this.basePTS||0===this.frameIndex&&L(o))&&(this.basePTS=$r(o,t,this.initPTS),this.lastPTS=this.basePTS),null===this.lastPTS&&(this.lastPTS=this.basePTS),i&&i.length>0&&s.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:i,type:Xr.audioId3,duration:Number.POSITIVE_INFINITY});a<l;){if(this.canParse(e,a)){var u=this.appendFrame(n,e,a);u?(this.frameIndex++,this.lastPTS=u.sample.pts,r=a+=u.length):a=l}else Nr(e,a)?(i=_r(e,a),s.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:i,type:Xr.audioId3,duration:Number.POSITIVE_INFINITY}),r=a+=i.length):a++;if(a===l&&r!==l){var d=e.slice(r);this.cachedData?this.cachedData=be(this.cachedData,d):this.cachedData=d}}return{audioTrack:n,videoTrack:zr(),id3Track:s,textTrack:zr()}},t.demuxSampleAes=function(e,t,r){return Promise.reject(new Error("["+this+"] This demuxer does not support Sample-AES decryption"))},t.flush=function(e){var t=this.cachedData;return t&&(this.cachedData=null,this.demux(t,0)),{audioTrack:this._audioTrack,videoTrack:zr(),id3Track:this._id3Track,textTrack:zr()}},t.destroy=function(){this.cachedData=null,this._audioTrack=this._id3Track=void 0},e}(),$r=function(e,t,r){return L(e)?90*e:9e4*t+(r?9e4*r.baseTime/r.timescale:0)},Zr=null,Jr=[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],ei=[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],ti=[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],ri=[0,1,1,4];function ii(e,t,r,i,a){if(!(r+24>t.length)){var n=ai(t,r);if(n&&r+n.frameLength<=t.length){var s=i+a*(9e4*n.samplesPerFrame/n.sampleRate),o={unit:t.subarray(r,r+n.frameLength),pts:s,dts:s};return e.config=[],e.channelCount=n.channelCount,e.samplerate=n.sampleRate,e.samples.push(o),{sample:o,length:n.frameLength,missing:0}}}}function ai(e,t){var r=e[t+1]>>3&3,i=e[t+1]>>1&3,a=e[t+2]>>4&15,n=e[t+2]>>2&3;if(1!==r&&0!==a&&15!==a&&3!==n){var s=e[t+2]>>1&1,o=e[t+3]>>6,l=1e3*Jr[14*(3===r?3-i:3===i?3:4)+a-1],u=ei[3*(3===r?0:2===r?1:2)+n],d=3===o?1:2,h=ti[r][i],f=ri[i],c=8*h*f,v=Math.floor(h*l/u+s)*f;if(null===Zr){var g=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);Zr=g?parseInt(g[1]):0}return!!Zr&&Zr<=87&&2===i&&l>=224e3&&0===o&&(e[t+3]=128|e[t+3]),{sampleRate:u,channelCount:d,frameLength:v,samplesPerFrame:c}}}function ni(e,t){return 255===e[t]&&224==(224&e[t+1])&&0!=(6&e[t+1])}function si(e,t){return t+1<e.length&&ni(e,t)}function oi(e,t){if(t+1<e.length&&ni(e,t)){var r=ai(e,t),i=4;null!=r&&r.frameLength&&(i=r.frameLength);var a=t+i;return a===e.length||si(e,a)}return!1}var li=function(e){function t(t,r){var i;return(i=e.call(this)||this).observer=void 0,i.config=void 0,i.observer=t,i.config=r,i}o(t,e);var r=t.prototype;return r.resetInitSegment=function(t,r,i,a){e.prototype.resetInitSegment.call(this,t,r,i,a),this._audioTrack={container:"audio/adts",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"aac",samples:[],manifestCodec:r,duration:a,inputTimeScale:9e4,dropped:0}},t.probe=function(e,t){if(!e)return!1;var r=_r(e,0),i=(null==r?void 0:r.length)||0;if(oi(e,i))return!1;for(var a=e.length;i<a;i++)if(wr(e,i))return t.log("ADTS sync word found !"),!0;return!1},r.canParse=function(e,t){return function(e,t){return function(e,t){return t+5<e.length}(e,t)&&Ir(e,t)&&Pr(e,t)<=e.length-t}(e,t)},r.appendFrame=function(e,t,r){Or(e,this.observer,t,r,e.manifestCodec);var i=Mr(e,t,r,this.basePTS,this.frameIndex);if(i&&0===i.missing)return i},t}(Qr),ui=function(e){function t(){return e.apply(this,arguments)||this}o(t,e);var r=t.prototype;return r.resetInitSegment=function(t,r,i,a){e.prototype.resetInitSegment.call(this,t,r,i,a),this._audioTrack={container:"audio/mpeg",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"mp3",samples:[],manifestCodec:r,duration:a,inputTimeScale:9e4,dropped:0}},t.probe=function(e){if(!e)return!1;var t=_r(e,0),r=(null==t?void 0:t.length)||0;if(t&&11===e[r]&&119===e[r+1]&&void 0!==qr(t)&&function(e,t){var r=0,i=5;t+=i;for(var a=new Uint32Array(1),n=new Uint32Array(1),s=new Uint8Array(1);i>0;){s[0]=e[t];var o=Math.min(i,8),l=8-o;n[0]=4278190080>>>24+l<<l,a[0]=(s[0]&n[0])>>l,r=r?r<<o|a[0]:a[0],t+=1,i-=o}return r}(e,r)<=16)return!1;for(var i=e.length;r<i;r++)if(oi(e,r))return j.log("MPEG Audio sync word found !"),!0;return!1},r.canParse=function(e,t){return function(e,t){return ni(e,t)&&4<=e.length-t}(e,t)},r.appendFrame=function(e,t,r){if(null!==this.basePTS)return ii(e,t,r,this.basePTS,this.frameIndex)},t}(Qr),di=function(){function e(e,t,r){this.subtle=void 0,this.aesIV=void 0,this.aesMode=void 0,this.subtle=e,this.aesIV=t,this.aesMode=r}return e.prototype.decrypt=function(e,t){switch(this.aesMode){case Ht:return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},t,e);case Kt:return this.subtle.decrypt({name:"AES-CTR",counter:this.aesIV,length:64},t,e);default:throw new Error("[AESCrypto] invalid aes mode "+this.aesMode)}},e}(),hi=function(){function e(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.ksRows=0,this.keySize=0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.initTable()}var t=e.prototype;return t.uint8ArrayToUint32Array_=function(e){for(var t=new DataView(e),r=new Uint32Array(4),i=0;i<4;i++)r[i]=t.getUint32(4*i);return r},t.initTable=function(){var e=this.sBox,t=this.invSBox,r=this.subMix,i=r[0],a=r[1],n=r[2],s=r[3],o=this.invSubMix,l=o[0],u=o[1],d=o[2],h=o[3],f=new Uint32Array(256),c=0,v=0,g=0;for(g=0;g<256;g++)f[g]=g<128?g<<1:g<<1^283;for(g=0;g<256;g++){var m=v^v<<1^v<<2^v<<3^v<<4;m=m>>>8^255&m^99,e[c]=m,t[m]=c;var p=f[c],y=f[p],E=f[y],T=257*f[m]^16843008*m;i[c]=T<<24|T>>>8,a[c]=T<<16|T>>>16,n[c]=T<<8|T>>>24,s[c]=T,T=16843009*E^65537*y^257*p^16843008*c,l[m]=T<<24|T>>>8,u[m]=T<<16|T>>>16,d[m]=T<<8|T>>>24,h[m]=T,c?(c=p^f[f[f[E^p]]],v^=f[f[v]]):c=v=1}},t.expandKey=function(e){for(var t=this.uint8ArrayToUint32Array_(e),r=!0,i=0;i<t.length&&r;)r=t[i]===this.key[i],i++;if(!r){this.key=t;var a=this.keySize=t.length;if(4!==a&&6!==a&&8!==a)throw new Error("Invalid aes key size="+a);var n,s,o,l,u=this.ksRows=4*(a+6+1),d=this.keySchedule=new Uint32Array(u),h=this.invKeySchedule=new Uint32Array(u),f=this.sBox,c=this.rcon,v=this.invSubMix,g=v[0],m=v[1],p=v[2],y=v[3];for(n=0;n<u;n++)n<a?o=d[n]=t[n]:(l=o,n%a==0?(l=f[(l=l<<8|l>>>24)>>>24]<<24|f[l>>>16&255]<<16|f[l>>>8&255]<<8|f[255&l],l^=c[n/a|0]<<24):a>6&&n%a==4&&(l=f[l>>>24]<<24|f[l>>>16&255]<<16|f[l>>>8&255]<<8|f[255&l]),d[n]=o=(d[n-a]^l)>>>0);for(s=0;s<u;s++)n=u-s,l=3&s?d[n]:d[n-4],h[s]=s<4||n<=4?l:g[f[l>>>24]]^m[f[l>>>16&255]]^p[f[l>>>8&255]]^y[f[255&l]],h[s]=h[s]>>>0}},t.networkToHostOrderSwap=function(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24},t.decrypt=function(e,t,r){for(var i,a,n,s,o,l,u,d,h,f,c,v,g,m,p=this.keySize+6,y=this.invKeySchedule,E=this.invSBox,T=this.invSubMix,S=T[0],L=T[1],R=T[2],A=T[3],b=this.uint8ArrayToUint32Array_(r),k=b[0],D=b[1],_=b[2],I=b[3],C=new Int32Array(e),P=new Int32Array(C.length),x=this.networkToHostOrderSwap;t<C.length;){for(h=x(C[t]),f=x(C[t+1]),c=x(C[t+2]),v=x(C[t+3]),o=h^y[0],l=v^y[1],u=c^y[2],d=f^y[3],g=4,m=1;m<p;m++)i=S[o>>>24]^L[l>>16&255]^R[u>>8&255]^A[255&d]^y[g],a=S[l>>>24]^L[u>>16&255]^R[d>>8&255]^A[255&o]^y[g+1],n=S[u>>>24]^L[d>>16&255]^R[o>>8&255]^A[255&l]^y[g+2],s=S[d>>>24]^L[o>>16&255]^R[l>>8&255]^A[255&u]^y[g+3],o=i,l=a,u=n,d=s,g+=4;i=E[o>>>24]<<24^E[l>>16&255]<<16^E[u>>8&255]<<8^E[255&d]^y[g],a=E[l>>>24]<<24^E[u>>16&255]<<16^E[d>>8&255]<<8^E[255&o]^y[g+1],n=E[u>>>24]<<24^E[d>>16&255]<<16^E[o>>8&255]<<8^E[255&l]^y[g+2],s=E[d>>>24]<<24^E[o>>16&255]<<16^E[l>>8&255]<<8^E[255&u]^y[g+3],P[t]=x(i^k),P[t+1]=x(s^D),P[t+2]=x(n^_),P[t+3]=x(a^I),k=h,D=f,_=c,I=v,t+=4}return P.buffer},e}(),fi=function(){function e(e,t,r){this.subtle=void 0,this.key=void 0,this.aesMode=void 0,this.subtle=e,this.key=t,this.aesMode=r}return e.prototype.expandKey=function(){var e=function(e){switch(e){case Ht:return"AES-CBC";case Kt:return"AES-CTR";default:throw new Error("[FastAESKey] invalid aes mode "+e)}}(this.aesMode);return this.subtle.importKey("raw",this.key,{name:e},!1,["encrypt","decrypt"])},e}(),ci=function(){function e(e,t){var r=(void 0===t?{}:t).removePKCS7Padding,i=void 0===r||r;if(this.logEnabled=!0,this.removePKCS7Padding=void 0,this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null,this.useSoftware=void 0,this.enableSoftwareAES=void 0,this.enableSoftwareAES=e.enableSoftwareAES,this.removePKCS7Padding=i,i)try{var a=self.crypto;a&&(this.subtle=a.subtle||a.webkitSubtle)}catch(e){}this.useSoftware=!this.subtle}var t=e.prototype;return t.destroy=function(){this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null},t.isSync=function(){return this.useSoftware},t.flush=function(){var e=this.currentResult,t=this.remainderData;if(!e||t)return this.reset(),null;var r,i,a,n=new Uint8Array(e);return this.reset(),this.removePKCS7Padding?(i=(r=n).byteLength,(a=i&&new DataView(r.buffer).getUint8(i-1))?r.slice(0,i-a):r):n},t.reset=function(){this.currentResult=null,this.currentIV=null,this.remainderData=null,this.softwareDecrypter&&(this.softwareDecrypter=null)},t.decrypt=function(e,t,r,i){var a=this;return this.useSoftware?new Promise((function(n,s){var o=ArrayBuffer.isView(e)?e:new Uint8Array(e);a.softwareDecrypt(o,t,r,i);var l=a.flush();l?n(l.buffer):s(new Error("[softwareDecrypt] Failed to decrypt data"))})):this.webCryptoDecrypt(new Uint8Array(e),t,r,i)},t.softwareDecrypt=function(e,t,r,i){var a=this.currentIV,n=this.currentResult,s=this.remainderData;if(i!==Ht||16!==t.byteLength)return j.warn("SoftwareDecrypt: can only handle AES-128-CBC"),null;this.logOnce("JS AES decrypt"),s&&(e=be(s,e),this.remainderData=null);var o=this.getValidChunk(e);if(!o.length)return null;a&&(r=a);var l=this.softwareDecrypter;l||(l=this.softwareDecrypter=new hi),l.expandKey(t);var u=n;return this.currentResult=l.decrypt(o.buffer,0,r),this.currentIV=o.slice(-16).buffer,u||null},t.webCryptoDecrypt=function(e,t,r,i){var a=this;if(this.key!==t||!this.fastAesKey){if(!this.subtle)return Promise.resolve(this.onWebCryptoError(e,t,r,i));this.key=t,this.fastAesKey=new fi(this.subtle,t,i)}return this.fastAesKey.expandKey().then((function(t){return a.subtle?(a.logOnce("WebCrypto AES decrypt"),new di(a.subtle,new Uint8Array(r),i).decrypt(e.buffer,t)):Promise.reject(new Error("web crypto not initialized"))})).catch((function(n){return j.warn("[decrypter]: WebCrypto Error, disable WebCrypto API, "+n.name+": "+n.message),a.onWebCryptoError(e,t,r,i)}))},t.onWebCryptoError=function(e,t,r,i){var a=this.enableSoftwareAES;if(a){this.useSoftware=!0,this.logEnabled=!0,this.softwareDecrypt(e,t,r,i);var n=this.flush();if(n)return n.buffer}throw new Error("WebCrypto"+(a?" and softwareDecrypt":"")+": failed to decrypt data")},t.getValidChunk=function(e){var t=e,r=e.length-e.length%16;return r!==e.length&&(t=e.slice(0,r),this.remainderData=e.slice(r)),t},t.logOnce=function(e){this.logEnabled&&(j.log("[decrypter]: "+e),this.logEnabled=!1)},e}(),vi=/\/emsg[-/]ID3/i,gi=function(){function e(e,t){this.remainderData=null,this.timeOffset=0,this.config=void 0,this.videoTrack=void 0,this.audioTrack=void 0,this.id3Track=void 0,this.txtTrack=void 0,this.config=t}var t=e.prototype;return t.resetTimeStamp=function(){},t.resetInitSegment=function(e,t,r,i){var a=this.videoTrack=zr("video",1),n=this.audioTrack=zr("audio",1),s=this.txtTrack=zr("text",1);if(this.id3Track=zr("id3",1),this.timeOffset=0,null!=e&&e.byteLength){var o=ye(e);if(o.video){var l=o.video,u=l.id,d=l.timescale,h=l.codec,f=l.supplemental;a.id=u,a.timescale=s.timescale=d,a.codec=h,a.supplemental=f}if(o.audio){var c=o.audio,v=c.id,g=c.timescale,m=c.codec;n.id=v,n.timescale=g,n.codec=m}s.id=de.text,a.sampleDuration=0,a.duration=n.duration=i}},t.resetContiguity=function(){this.remainderData=null},e.probe=function(e){return function(e){for(var t=e.byteLength,r=0;r<t;){var i=ce(e,r);if(i>8&&109===e[r+4]&&111===e[r+5]&&111===e[r+6]&&102===e[r+7])return!0;r=i>1?r+i:t}return!1}(e)},t.demux=function(e,t){this.timeOffset=t;var r=e,i=this.videoTrack,a=this.txtTrack;if(this.config.progressive){this.remainderData&&(r=be(this.remainderData,e));var n=function(e){var t={valid:null,remainder:null},r=me(e,["moof"]);if(r.length<2)return t.remainder=e,t;var i=r[r.length-1];return t.valid=e.slice(0,i.byteOffset-8),t.remainder=e.slice(i.byteOffset-8),t}(r);this.remainderData=n.remainder,i.samples=n.valid||new Uint8Array}else i.samples=r;var s=this.extractID3Track(i,t);return a.samples=ke(t,i),{videoTrack:i,audioTrack:this.audioTrack,id3Track:s,textTrack:this.txtTrack}},t.flush=function(){var e=this.timeOffset,t=this.videoTrack,r=this.txtTrack;t.samples=this.remainderData||new Uint8Array,this.remainderData=null;var i=this.extractID3Track(t,this.timeOffset);return r.samples=ke(e,t),{videoTrack:t,audioTrack:zr(),id3Track:i,textTrack:zr()}},t.extractID3Track=function(e,t){var r=this,i=this.id3Track;if(e.samples.length){var a=me(e.samples,["emsg"]);a&&a.forEach((function(e){var a=function(e){var t=e[0],r="",i="",a=0,n=0,s=0,o=0,l=0,u=0;if(0===t){for(;"\0"!==he(e.subarray(u,u+1));)r+=he(e.subarray(u,u+1)),u+=1;for(r+=he(e.subarray(u,u+1)),u+=1;"\0"!==he(e.subarray(u,u+1));)i+=he(e.subarray(u,u+1)),u+=1;i+=he(e.subarray(u,u+1)),u+=1,a=ce(e,12),n=ce(e,16),o=ce(e,20),l=ce(e,24),u=28}else if(1===t){a=ce(e,u+=4);var d=ce(e,u+=4),h=ce(e,u+=4);for(u+=4,s=Math.pow(2,32)*d+h,R(s)||(s=Number.MAX_SAFE_INTEGER,j.warn("Presentation time exceeds safe integer limit and wrapped to max safe integer in parsing emsg box")),o=ce(e,u),l=ce(e,u+=4),u+=4;"\0"!==he(e.subarray(u,u+1));)r+=he(e.subarray(u,u+1)),u+=1;for(r+=he(e.subarray(u,u+1)),u+=1;"\0"!==he(e.subarray(u,u+1));)i+=he(e.subarray(u,u+1)),u+=1;i+=he(e.subarray(u,u+1)),u+=1}return{schemeIdUri:r,value:i,timeScale:a,presentationTime:s,presentationTimeDelta:n,eventDuration:o,id:l,payload:e.subarray(u,e.byteLength)}}(e);if(vi.test(a.schemeIdUri)){var n=mi(a,t),s=4294967295===a.eventDuration?Number.POSITIVE_INFINITY:a.eventDuration/a.timeScale;s<=.001&&(s=Number.POSITIVE_INFINITY);var o=a.payload;i.samples.push({data:o,len:o.byteLength,dts:n,pts:n,type:Xr.emsg,duration:s})}else if(r.config.enableEmsgKLVMetadata&&a.schemeIdUri.startsWith("urn:misb:KLV:bin:1910.1")){var l=mi(a,t);i.samples.push({data:a.payload,len:a.payload.byteLength,dts:l,pts:l,type:Xr.misbklv,duration:Number.POSITIVE_INFINITY})}}))}return i},t.demuxSampleAes=function(e,t,r){return Promise.reject(new Error("The MP4 demuxer does not support SAMPLE-AES decryption"))},t.destroy=function(){this.config=null,this.remainderData=null,this.videoTrack=this.audioTrack=this.id3Track=this.txtTrack=void 0},e}();function mi(e,t){return L(e.presentationTime)?e.presentationTime/e.timeScale:t+e.presentationTimeDelta/e.timeScale}var pi=function(){function e(e,t,r){this.keyData=void 0,this.decrypter=void 0,this.keyData=r,this.decrypter=new ci(t,{removePKCS7Padding:!1})}var t=e.prototype;return t.decryptBuffer=function(e){return this.decrypter.decrypt(e,this.keyData.key.buffer,this.keyData.iv.buffer,Ht)},t.decryptAacSample=function(e,t,r){var i=this,a=e[t].unit;if(!(a.length<=16)){var n=a.subarray(16,a.length-a.length%16),s=n.buffer.slice(n.byteOffset,n.byteOffset+n.length);this.decryptBuffer(s).then((function(n){var s=new Uint8Array(n);a.set(s,16),i.decrypter.isSync()||i.decryptAacSamples(e,t+1,r)}))}},t.decryptAacSamples=function(e,t,r){for(;;t++){if(t>=e.length)return void r();if(!(e[t].unit.length<32||(this.decryptAacSample(e,t,r),this.decrypter.isSync())))return}},t.getAvcEncryptedData=function(e){for(var t=16*Math.floor((e.length-48)/160)+16,r=new Int8Array(t),i=0,a=32;a<e.length-16;a+=160,i+=16)r.set(e.subarray(a,a+16),i);return r},t.getAvcDecryptedUnit=function(e,t){for(var r=new Uint8Array(t),i=0,a=32;a<e.length-16;a+=160,i+=16)e.set(r.subarray(i,i+16),a);return e},t.decryptAvcSample=function(e,t,r,i,a){var n=this,s=Ce(a.data),o=this.getAvcEncryptedData(s);this.decryptBuffer(o.buffer).then((function(o){a.data=n.getAvcDecryptedUnit(s,o),n.decrypter.isSync()||n.decryptAvcSamples(e,t,r+1,i)}))},t.decryptAvcSamples=function(e,t,r,i){if(e instanceof Uint8Array)throw new Error("Cannot decrypt samples of type Uint8Array");for(;;t++,r=0){if(t>=e.length)return void i();for(var a=e[t].units;!(r>=a.length);r++){var n=a[r];if(!(n.data.length<=48||1!==n.type&&5!==n.type||(this.decryptAvcSample(e,t,r,i,n),this.decrypter.isSync())))return}}},e}(),yi=function(){function e(){this.VideoSample=null}var t=e.prototype;return t.createVideoSample=function(e,t,r){return{key:e,frame:!1,pts:t,dts:r,units:[],length:0}},t.getLastNalUnit=function(e){var t,r,i=this.VideoSample;if(i&&0!==i.units.length||(i=e[e.length-1]),null!=(t=i)&&t.units){var a=i.units;r=a[a.length-1]}return r},t.pushAccessUnit=function(e,t){if(e.units.length&&e.frame){if(void 0===e.pts){var r=t.samples,i=r.length;if(!i)return void t.dropped++;var a=r[i-1];e.pts=a.pts,e.dts=a.dts}t.samples.push(e)}},t.parseNALu=function(e,t,r){var i,a,n=t.byteLength,s=e.naluState||0,o=s,l=[],u=0,d=-1,h=0;for(-1===s&&(d=0,h=this.getNALuType(t,0),s=0,u=1);u<n;)if(i=t[u++],s)if(1!==s)if(i)if(1===i){if(a=u-s-1,d>=0){var f={data:t.subarray(d,a),type:h};l.push(f)}else{var c=this.getLastNalUnit(e.samples);c&&(o&&u<=4-o&&c.state&&(c.data=c.data.subarray(0,c.data.byteLength-o)),a>0&&(c.data=be(c.data,t.subarray(0,a)),c.state=0))}u<n?(d=u,h=this.getNALuType(t,u),s=0):s=-1}else s=0;else s=3;else s=i?0:2;else s=i?0:1;if(d>=0&&s>=0){var v={data:t.subarray(d,n),type:h,state:s};l.push(v)}if(0===l.length){var g=this.getLastNalUnit(e.samples);g&&(g.data=be(g.data,t))}return e.naluState=s,l},e}(),Ei=function(){function e(e){this.data=void 0,this.bytesAvailable=void 0,this.word=void 0,this.bitsAvailable=void 0,this.data=e,this.bytesAvailable=e.byteLength,this.word=0,this.bitsAvailable=0}var t=e.prototype;return t.loadWord=function(){var e=this.data,t=this.bytesAvailable,r=e.byteLength-t,i=new Uint8Array(4),a=Math.min(4,t);if(0===a)throw new Error("no bytes available");i.set(e.subarray(r,r+a)),this.word=new DataView(i.buffer).getUint32(0),this.bitsAvailable=8*a,this.bytesAvailable-=a},t.skipBits=function(e){var t;e=Math.min(e,8*this.bytesAvailable+this.bitsAvailable),this.bitsAvailable>e?(this.word<<=e,this.bitsAvailable-=e):(e-=this.bitsAvailable,e-=(t=e>>3)<<3,this.bytesAvailable-=t,this.loadWord(),this.word<<=e,this.bitsAvailable-=e)},t.readBits=function(e){var t=Math.min(this.bitsAvailable,e),r=this.word>>>32-t;if(e>32&&j.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=t,this.bitsAvailable>0)this.word<<=t;else{if(!(this.bytesAvailable>0))throw new Error("no bits available");this.loadWord()}return(t=e-t)>0&&this.bitsAvailable?r<<t|this.readBits(t):r},t.skipLZ=function(){var e;for(e=0;e<this.bitsAvailable;++e)if(0!=(this.word&2147483648>>>e))return this.word<<=e,this.bitsAvailable-=e,e;return this.loadWord(),e+this.skipLZ()},t.skipUEG=function(){this.skipBits(1+this.skipLZ())},t.skipEG=function(){this.skipBits(1+this.skipLZ())},t.readUEG=function(){var e=this.skipLZ();return this.readBits(e+1)-1},t.readEG=function(){var e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)},t.readBoolean=function(){return 1===this.readBits(1)},t.readUByte=function(){return this.readBits(8)},t.readUShort=function(){return this.readBits(16)},t.readUInt=function(){return this.readBits(32)},e}(),Ti=function(e){function t(){return e.apply(this,arguments)||this}o(t,e);var r=t.prototype;return r.parsePES=function(e,t,r,i){var a,n=this,s=this.parseNALu(e,r.data,i),o=this.VideoSample,l=!1;r.data=null,o&&s.length&&!e.audFound&&(this.pushAccessUnit(o,e),o=this.VideoSample=this.createVideoSample(!1,r.pts,r.dts)),s.forEach((function(i){var s,u;switch(i.type){case 1:var d=!1;a=!0;var h,f=i.data;if(l&&f.length>4){var c=n.readSliceType(f);2!==c&&4!==c&&7!==c&&9!==c||(d=!0)}d&&null!=(h=o)&&h.frame&&!o.key&&(n.pushAccessUnit(o,e),o=n.VideoSample=null),o||(o=n.VideoSample=n.createVideoSample(!0,r.pts,r.dts)),o.frame=!0,o.key=d;break;case 5:a=!0,null!=(s=o)&&s.frame&&!o.key&&(n.pushAccessUnit(o,e),o=n.VideoSample=null),o||(o=n.VideoSample=n.createVideoSample(!0,r.pts,r.dts)),o.key=!0,o.frame=!0;break;case 6:a=!0,Ie(i.data,1,r.pts,t.samples);break;case 7:var v,g;a=!0,l=!0;var m=i.data,p=n.readSPS(m);if(!e.sps||e.width!==p.width||e.height!==p.height||(null==(v=e.pixelRatio)?void 0:v[0])!==p.pixelRatio[0]||(null==(g=e.pixelRatio)?void 0:g[1])!==p.pixelRatio[1]){e.width=p.width,e.height=p.height,e.pixelRatio=p.pixelRatio,e.sps=[m];for(var y=m.subarray(1,4),E="avc1.",T=0;T<3;T++){var S=y[T].toString(16);S.length<2&&(S="0"+S),E+=S}e.codec=E}break;case 8:a=!0,e.pps=[i.data];break;case 9:a=!0,e.audFound=!0,null!=(u=o)&&u.frame&&(n.pushAccessUnit(o,e),o=null),o||(o=n.VideoSample=n.createVideoSample(!1,r.pts,r.dts));break;case 12:a=!0;break;default:a=!1}o&&a&&o.units.push(i)})),i&&o&&(this.pushAccessUnit(o,e),this.VideoSample=null)},r.getNALuType=function(e,t){return 31&e[t]},r.readSliceType=function(e){var t=new Ei(e);return t.readUByte(),t.readUEG(),t.readUEG()},r.skipScalingList=function(e,t){for(var r=8,i=8,a=0;a<e;a++)0!==i&&(i=(r+t.readEG()+256)%256),r=0===i?r:i},r.readSPS=function(e){var t,r,i,a=new Ei(e),n=0,s=0,o=0,l=0,u=a.readUByte.bind(a),d=a.readBits.bind(a),h=a.readUEG.bind(a),f=a.readBoolean.bind(a),c=a.skipBits.bind(a),v=a.skipEG.bind(a),g=a.skipUEG.bind(a),m=this.skipScalingList.bind(this);u();var p=u();if(d(5),c(3),u(),g(),100===p||110===p||122===p||244===p||44===p||83===p||86===p||118===p||128===p){var y=h();if(3===y&&c(1),g(),g(),c(1),f())for(r=3!==y?8:12,i=0;i<r;i++)f()&&m(i<6?16:64,a)}g();var E=h();if(0===E)h();else if(1===E)for(c(1),v(),v(),t=h(),i=0;i<t;i++)v();g(),c(1);var T=h(),S=h(),L=d(1);0===L&&c(1),c(1),f()&&(n=h(),s=h(),o=h(),l=h());var R=[1,1];if(f()&&f())switch(u()){case 1:R=[1,1];break;case 2:R=[12,11];break;case 3:R=[10,11];break;case 4:R=[16,11];break;case 5:R=[40,33];break;case 6:R=[24,11];break;case 7:R=[20,11];break;case 8:R=[32,11];break;case 9:R=[80,33];break;case 10:R=[18,11];break;case 11:R=[15,11];break;case 12:R=[64,33];break;case 13:R=[160,99];break;case 14:R=[4,3];break;case 15:R=[3,2];break;case 16:R=[2,1];break;case 255:R=[u()<<8|u(),u()<<8|u()]}return{width:Math.ceil(16*(T+1)-2*n-2*s),height:(2-L)*(S+1)*16-(L?2:4)*(o+l),pixelRatio:R}},t}(yi),Si=188,Li=function(){function e(e,t,r,i){this.logger=void 0,this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.sampleAes=null,this.pmtParsed=!1,this.audioCodec=void 0,this.videoCodec=void 0,this._pmtId=-1,this._videoTrack=void 0,this._audioTrack=void 0,this._id3Track=void 0,this._txtTrack=void 0,this.aacOverFlow=null,this.remainderData=null,this.videoParser=void 0,this.observer=e,this.config=t,this.typeSupported=r,this.logger=i,this.videoParser=null}e.probe=function(t,r){var i=e.syncOffset(t);return i>0&&r.warn("MPEG2-TS detected but first sync word found @ offset "+i),-1!==i},e.syncOffset=function(e){for(var t=e.length,r=Math.min(940,t-Si)+1,i=0;i<r;){for(var a=!1,n=-1,s=0,o=i;o<t;o+=Si){if(71!==e[o]||t-o!==Si&&71!==e[o+Si]){if(s)return-1;break}if(s++,-1===n&&0!==(n=o)&&(r=Math.min(n+18612,e.length-Si)+1),a||(a=0===Ri(e,o)),a&&s>1&&(0===n&&s>2||o+Si>r))return n}i++}return-1},e.createTrack=function(e,t){return{container:"video"===e||"audio"===e?"video/mp2t":void 0,type:e,id:de[e],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0,duration:"audio"===e?t:void 0}};var t=e.prototype;return t.resetInitSegment=function(t,r,i,a){this.pmtParsed=!1,this._pmtId=-1,this._videoTrack=e.createTrack("video"),this._videoTrack.duration=a,this._audioTrack=e.createTrack("audio",a),this._id3Track=e.createTrack("id3"),this._txtTrack=e.createTrack("text"),this._audioTrack.segmentCodec="aac",this.videoParser=null,this.aacOverFlow=null,this.remainderData=null,this.audioCodec=r,this.videoCodec=i},t.resetTimeStamp=function(){},t.resetContiguity=function(){var e=this._audioTrack,t=this._videoTrack,r=this._id3Track;e&&(e.pesData=null),t&&(t.pesData=null),r&&(r.pesData=null),this.aacOverFlow=null,this.remainderData=null},t.demux=function(t,r,i,a){var n;void 0===i&&(i=!1),void 0===a&&(a=!1),i||(this.sampleAes=null);var s=this._videoTrack,o=this._audioTrack,l=this._id3Track,u=this._txtTrack,d=s.pid,h=s.pesData,f=o.pid,c=l.pid,v=o.pesData,g=l.pesData,m=null,p=this.pmtParsed,y=this._pmtId,E=t.length;if(this.remainderData&&(E=(t=be(this.remainderData,t)).length,this.remainderData=null),E<Si&&!a)return this.remainderData=t,{audioTrack:o,videoTrack:s,id3Track:l,textTrack:u};var T=Math.max(0,e.syncOffset(t));(E-=(E-T)%Si)<t.byteLength&&!a&&(this.remainderData=new Uint8Array(t.buffer,E,t.buffer.byteLength-E));for(var S=0,L=T;L<E;L+=Si)if(71===t[L]){var R=!!(64&t[L+1]),A=Ri(t,L),b=void 0;if((48&t[L+3])>>4>1){if((b=L+5+t[L+4])===L+Si)continue}else b=L+4;switch(A){case d:R&&(h&&(n=_i(h,this.logger))&&(this.readyVideoParser(s.segmentCodec),null!==this.videoParser&&this.videoParser.parsePES(s,u,n,!1)),h={data:[],size:0}),h&&(h.data.push(t.subarray(b,L+Si)),h.size+=L+Si-b);break;case f:if(R){if(v&&(n=_i(v,this.logger)))switch(o.segmentCodec){case"aac":this.parseAACPES(o,n);break;case"mp3":this.parseMPEGPES(o,n)}v={data:[],size:0}}v&&(v.data.push(t.subarray(b,L+Si)),v.size+=L+Si-b);break;case c:R&&(g&&(n=_i(g,this.logger))&&this.parseID3PES(l,n),g={data:[],size:0}),g&&(g.data.push(t.subarray(b,L+Si)),g.size+=L+Si-b);break;case 0:R&&(b+=t[b]+1),y=this._pmtId=Ai(t,b);break;case y:R&&(b+=t[b]+1);var k=bi(t,b,this.typeSupported,i,this.observer,this.logger);(d=k.videoPid)>0&&(s.pid=d,s.segmentCodec=k.segmentVideoCodec),(f=k.audioPid)>0&&(o.pid=f,o.segmentCodec=k.segmentAudioCodec),(c=k.id3Pid)>0&&(l.pid=c),null===m||p||(this.logger.warn("MPEG-TS PMT found at "+L+" after unknown PID '"+m+"'. Backtracking to sync byte @"+T+" to parse all TS packets."),m=null,L=T-188),p=this.pmtParsed=!0;break;case 17:case 8191:break;default:m=A}}else S++;S>0&&ki(this.observer,new Error("Found "+S+" TS packet/s that do not start with 0x47"),void 0,this.logger),s.pesData=h,o.pesData=v,l.pesData=g;var D={audioTrack:o,videoTrack:s,id3Track:l,textTrack:u};return a&&this.extractRemainingSamples(D),D},t.flush=function(){var e,t=this.remainderData;return this.remainderData=null,e=t?this.demux(t,-1,!1,!0):{videoTrack:this._videoTrack,audioTrack:this._audioTrack,id3Track:this._id3Track,textTrack:this._txtTrack},this.extractRemainingSamples(e),this.sampleAes?this.decrypt(e,this.sampleAes):e},t.extractRemainingSamples=function(e){var t,r=e.audioTrack,i=e.videoTrack,a=e.id3Track,n=e.textTrack,s=i.pesData,o=r.pesData,l=a.pesData;if(s&&(t=_i(s,this.logger))?(this.readyVideoParser(i.segmentCodec),null!==this.videoParser&&(this.videoParser.parsePES(i,n,t,!0),i.pesData=null)):i.pesData=s,o&&(t=_i(o,this.logger))){switch(r.segmentCodec){case"aac":this.parseAACPES(r,t);break;case"mp3":this.parseMPEGPES(r,t)}r.pesData=null}else null!=o&&o.size&&this.logger.log("last AAC PES packet truncated,might overlap between fragments"),r.pesData=o;l&&(t=_i(l,this.logger))?(this.parseID3PES(a,t),a.pesData=null):a.pesData=l},t.demuxSampleAes=function(e,t,r){var i=this.demux(e,r,!0,!this.config.progressive),a=this.sampleAes=new pi(this.observer,this.config,t);return this.decrypt(i,a)},t.readyVideoParser=function(e){null===this.videoParser&&"avc"===e&&(this.videoParser=new Ti)},t.decrypt=function(e,t){return new Promise((function(r){var i=e.audioTrack,a=e.videoTrack;i.samples&&"aac"===i.segmentCodec?t.decryptAacSamples(i.samples,0,(function(){a.samples?t.decryptAvcSamples(a.samples,0,0,(function(){r(e)})):r(e)})):a.samples&&t.decryptAvcSamples(a.samples,0,0,(function(){r(e)}))}))},t.destroy=function(){this.observer&&this.observer.removeAllListeners(),this.config=this.logger=this.observer=null,this.aacOverFlow=this.videoParser=this.remainderData=this.sampleAes=null,this._videoTrack=this._audioTrack=this._id3Track=this._txtTrack=void 0},t.parseAACPES=function(e,t){var r,i,a,n=0,s=this.aacOverFlow,o=t.data;if(s){this.aacOverFlow=null;var l=s.missing,u=s.sample.unit.byteLength;if(-1===l)o=be(s.sample.unit,o);else{var d=u-l;s.sample.unit.set(o.subarray(0,l),d),e.samples.push(s.sample),n=s.missing}}for(r=n,i=o.length;r<i-1&&!xr(o,r);r++);if(r!==n){var h,f=r<i-1;if(h=f?"AAC PES did not start with ADTS header,offset:"+r:"No ADTS header found in AAC PES",ki(this.observer,new Error(h),f,this.logger),!f)return}if(Or(e,this.observer,o,r,this.audioCodec),void 0!==t.pts)a=t.pts;else{if(!s)return void this.logger.warn("[tsdemuxer]: AAC PES unknown PTS");var c=Fr(e.samplerate);a=s.sample.pts+c}for(var v,g=0;r<i;){if(r+=(v=Mr(e,o,r,a,g)).length,v.missing){this.aacOverFlow=v;break}for(g++;r<i-1&&!xr(o,r);r++);}},t.parseMPEGPES=function(e,t){var r=t.data,i=r.length,a=0,n=0,s=t.pts;if(void 0!==s)for(;n<i;)if(si(r,n)){var o=ii(e,r,n,s,a);if(!o)break;n+=o.length,a++}else n++;else this.logger.warn("[tsdemuxer]: MPEG PES unknown PTS")},t.parseAC3PES=function(e,t){},t.parseID3PES=function(e,t){if(void 0!==t.pts){var r=n({},t,{type:this._videoTrack?Xr.emsg:Xr.audioId3,duration:Number.POSITIVE_INFINITY});e.samples.push(r)}else this.logger.warn("[tsdemuxer]: ID3 PES unknown PTS")},e}();function Ri(e,t){return((31&e[t+1])<<8)+e[t+2]}function Ai(e,t){return(31&e[t+10])<<8|e[t+11]}function bi(e,t,r,i,a,n){var s={audioPid:-1,videoPid:-1,id3Pid:-1,segmentVideoCodec:"avc",segmentAudioCodec:"aac"},o=t+3+((15&e[t+1])<<8|e[t+2])-4;for(t+=12+((15&e[t+10])<<8|e[t+11]);t<o;){var l=Ri(e,t),u=(15&e[t+3])<<8|e[t+4];switch(e[t]){case 207:if(!i){Di("ADTS AAC",n);break}case 15:-1===s.audioPid&&(s.audioPid=l);break;case 21:-1===s.id3Pid&&(s.id3Pid=l);break;case 219:if(!i){Di("H.264",n);break}case 27:-1===s.videoPid&&(s.videoPid=l);break;case 3:case 4:r.mpeg||r.mp3?-1===s.audioPid&&(s.audioPid=l,s.segmentAudioCodec="mp3"):n.log("MPEG audio found, not supported in this browser");break;case 193:if(!i){Di("AC-3",n);break}case 129:n.warn("AC-3 in M2TS support not included in build");break;case 6:if(-1===s.audioPid&&u>0)for(var d=t+5,h=u;h>2;){106===e[d]&&n.warn("AC-3 in M2TS support not included in build");var f=e[d+1]+2;d+=f,h-=f}break;case 194:case 135:return ki(a,new Error("Unsupported EC-3 in M2TS found"),void 0,n),s;case 36:return ki(a,new Error("Unsupported HEVC in M2TS found"),void 0,n),s}t+=u+5}return s}function ki(e,t,r,i){i.warn("parsing error: "+t.message),e.emit(D.ERROR,D.ERROR,{type:b.MEDIA_ERROR,details:k.FRAG_PARSING_ERROR,fatal:!1,levelRetry:r,error:t,reason:t.message})}function Di(e,t){t.log(e+" with AES-128-CBC encryption found in unencrypted stream")}function _i(e,t){var r,i,a,n,s,o=0,l=e.data;if(!e||0===e.size)return null;for(;l[0].length<19&&l.length>1;)l[0]=be(l[0],l[1]),l.splice(1,1);if(1===((r=l[0])[0]<<16)+(r[1]<<8)+r[2]){if((i=(r[4]<<8)+r[5])&&i>e.size-6)return null;var u=r[7];192&u&&(n=536870912*(14&r[9])+4194304*(255&r[10])+16384*(254&r[11])+128*(255&r[12])+(254&r[13])/2,64&u?n-(s=536870912*(14&r[14])+4194304*(255&r[15])+16384*(254&r[16])+128*(255&r[17])+(254&r[18])/2)>54e5&&(t.warn(Math.round((n-s)/9e4)+"s delta between PTS and DTS, align them"),n=s):s=n);var d=(a=r[8])+9;if(e.size<=d)return null;e.size-=d;for(var h=new Uint8Array(e.size),f=0,c=l.length;f<c;f++){var v=(r=l[f]).byteLength;if(d){if(d>v){d-=v;continue}r=r.subarray(d),v-=d,d=0}h.set(r,o),o+=v}return i&&(i-=a+3),{data:h,pts:n,dts:s,len:i}}return null}var Ii=function(){function e(){}return e.getSilentFrame=function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}},e}(),Ci=Math.pow(2,32)-1,Pi=function(){function e(){}return e.init=function(){var t;for(t in e.types={avc1:[],avcC:[],hvc1:[],hvcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],dac3:[],"ac-3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]},e.types)e.types.hasOwnProperty(t)&&(e.types[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)]);var r=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);e.HDLR_TYPES={video:r,audio:i};var a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),n=new Uint8Array([0,0,0,0,0,0,0,0]);e.STTS=e.STSC=e.STCO=n,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);var s=new Uint8Array([105,115,111,109]),o=new Uint8Array([97,118,99,49]),l=new Uint8Array([0,0,0,1]);e.FTYP=e.box(e.types.ftyp,s,l,s,o),e.DINF=e.box(e.types.dinf,e.box(e.types.dref,a))},e.box=function(e){for(var t=8,r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];for(var n=i.length,s=n;n--;)t+=i[n].byteLength;var o=new Uint8Array(t);for(o[0]=t>>24&255,o[1]=t>>16&255,o[2]=t>>8&255,o[3]=255&t,o.set(e,4),n=0,t=8;n<s;n++)o.set(i[n],t),t+=i[n].byteLength;return o},e.hdlr=function(t){return e.box(e.types.hdlr,e.HDLR_TYPES[t])},e.mdat=function(t){return e.box(e.types.mdat,t)},e.mdhd=function(t,r){r*=t;var i=Math.floor(r/(Ci+1)),a=Math.floor(r%(Ci+1));return e.box(e.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,255&t,i>>24,i>>16&255,i>>8&255,255&i,a>>24,a>>16&255,a>>8&255,255&a,85,196,0,0]))},e.mdia=function(t){return e.box(e.types.mdia,e.mdhd(t.timescale||0,t.duration||0),e.hdlr(t.type),e.minf(t))},e.mfhd=function(t){return e.box(e.types.mfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))},e.minf=function(t){return"audio"===t.type?e.box(e.types.minf,e.box(e.types.smhd,e.SMHD),e.DINF,e.stbl(t)):e.box(e.types.minf,e.box(e.types.vmhd,e.VMHD),e.DINF,e.stbl(t))},e.moof=function(t,r,i){return e.box(e.types.moof,e.mfhd(t),e.traf(i,r))},e.moov=function(t){for(var r=t.length,i=[];r--;)i[r]=e.trak(t[r]);return e.box.apply(null,[e.types.moov,e.mvhd(t[0].timescale||0,t[0].duration||0)].concat(i).concat(e.mvex(t)))},e.mvex=function(t){for(var r=t.length,i=[];r--;)i[r]=e.trex(t[r]);return e.box.apply(null,[e.types.mvex].concat(i))},e.mvhd=function(t,r){r*=t;var i=Math.floor(r/(Ci+1)),a=Math.floor(r%(Ci+1)),n=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,255&t,i>>24,i>>16&255,i>>8&255,255&i,a>>24,a>>16&255,a>>8&255,255&a,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return e.box(e.types.mvhd,n)},e.sdtp=function(t){var r,i,a=t.samples||[],n=new Uint8Array(4+a.length);for(r=0;r<a.length;r++)i=a[r].flags,n[r+4]=i.dependsOn<<4|i.isDependedOn<<2|i.hasRedundancy;return e.box(e.types.sdtp,n)},e.stbl=function(t){return e.box(e.types.stbl,e.stsd(t),e.box(e.types.stts,e.STTS),e.box(e.types.stsc,e.STSC),e.box(e.types.stsz,e.STSZ),e.box(e.types.stco,e.STCO))},e.avc1=function(t){var r,i,a,n=[],s=[];for(r=0;r<t.sps.length;r++)a=(i=t.sps[r]).byteLength,n.push(a>>>8&255),n.push(255&a),n=n.concat(Array.prototype.slice.call(i));for(r=0;r<t.pps.length;r++)a=(i=t.pps[r]).byteLength,s.push(a>>>8&255),s.push(255&a),s=s.concat(Array.prototype.slice.call(i));var o=e.box(e.types.avcC,new Uint8Array([1,n[3],n[4],n[5],255,224|t.sps.length].concat(n).concat([t.pps.length]).concat(s))),l=t.width,u=t.height,d=t.pixelRatio[0],h=t.pixelRatio[1];return e.box(e.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,l>>8&255,255&l,u>>8&255,255&u,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),o,e.box(e.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),e.box(e.types.pasp,new Uint8Array([d>>24,d>>16&255,d>>8&255,255&d,h>>24,h>>16&255,h>>8&255,255&h])))},e.esds=function(e){var t=e.config;return new Uint8Array([0,0,0,0,3,25,0,1,0,4,17,64,21,0,0,0,0,0,0,0,0,0,0,0,5,2].concat(t,[6,1,2]))},e.audioStsd=function(e){var t=e.samplerate||0;return new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount||0,0,16,0,0,0,0,t>>8&255,255&t,0,0])},e.mp4a=function(t){return e.box(e.types.mp4a,e.audioStsd(t),e.box(e.types.esds,e.esds(t)))},e.mp3=function(t){return e.box(e.types[".mp3"],e.audioStsd(t))},e.ac3=function(t){return e.box(e.types["ac-3"],e.audioStsd(t),e.box(e.types.dac3,t.config))},e.stsd=function(t){var r=t.segmentCodec;if("audio"===t.type){if("aac"===r)return e.box(e.types.stsd,e.STSD,e.mp4a(t));if("mp3"===r&&"mp3"===t.codec)return e.box(e.types.stsd,e.STSD,e.mp3(t))}else{if(!t.pps||!t.sps)throw new Error("video track missing pps or sps");if("avc"===r)return e.box(e.types.stsd,e.STSD,e.avc1(t))}throw new Error("unsupported "+t.type+" segment codec ("+r+"/"+t.codec+")")},e.tkhd=function(t){var r=t.id,i=(t.duration||0)*(t.timescale||0),a=t.width||0,n=t.height||0,s=Math.floor(i/(Ci+1)),o=Math.floor(i%(Ci+1));return e.box(e.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,s>>24,s>>16&255,s>>8&255,255&s,o>>24,o>>16&255,o>>8&255,255&o,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,a>>8&255,255&a,0,0,n>>8&255,255&n,0,0]))},e.traf=function(t,r){var i=e.sdtp(t),a=t.id,n=Math.floor(r/(Ci+1)),s=Math.floor(r%(Ci+1));return e.box(e.types.traf,e.box(e.types.tfhd,new Uint8Array([0,0,0,0,a>>24,a>>16&255,a>>8&255,255&a])),e.box(e.types.tfdt,new Uint8Array([1,0,0,0,n>>24,n>>16&255,n>>8&255,255&n,s>>24,s>>16&255,s>>8&255,255&s])),e.trun(t,i.length+16+20+8+16+8+8),i)},e.trak=function(t){return t.duration=t.duration||4294967295,e.box(e.types.trak,e.tkhd(t),e.mdia(t))},e.trex=function(t){var r=t.id;return e.box(e.types.trex,new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))},e.trun=function(t,r){var i,a,n,s,o,l,u=t.samples||[],d=u.length,h=12+16*d,f=new Uint8Array(h);for(r+=8+h,f.set(["video"===t.type?1:0,0,15,1,d>>>24&255,d>>>16&255,d>>>8&255,255&d,r>>>24&255,r>>>16&255,r>>>8&255,255&r],0),i=0;i<d;i++)n=(a=u[i]).duration,s=a.size,o=a.flags,l=a.cts,f.set([n>>>24&255,n>>>16&255,n>>>8&255,255&n,s>>>24&255,s>>>16&255,s>>>8&255,255&s,o.isLeading<<2|o.dependsOn,o.isDependedOn<<6|o.hasRedundancy<<4|o.paddingValue<<1|o.isNonSync,61440&o.degradPrio,15&o.degradPrio,l>>>24&255,l>>>16&255,l>>>8&255,255&l],12+16*i);return e.box(e.types.trun,f)},e.initSegment=function(t){e.types||e.init();var r=e.moov(t);return be(e.FTYP,r)},e.hvc1=function(e){return new Uint8Array},e}();function xi(e,t){return function(e,t,r,i){var a=e*t*r;return Math.round(a)}(e,1e3,1/9e4)}Pi.types=void 0,Pi.HDLR_TYPES=void 0,Pi.STTS=void 0,Pi.STSC=void 0,Pi.STCO=void 0,Pi.STSZ=void 0,Pi.VMHD=void 0,Pi.SMHD=void 0,Pi.STSD=void 0,Pi.FTYP=void 0,Pi.DINF=void 0;var wi=null,Oi=null;function Fi(e,t,r,i){return{duration:t,size:r,cts:i,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:e?2:1,isNonSync:e?0:1}}}var Mi=function(e){function t(t,r,i,a){var n;if((n=e.call(this,"mp4-remuxer",a)||this).observer=void 0,n.config=void 0,n.typeSupported=void 0,n.ISGenerated=!1,n._initPTS=null,n._initDTS=null,n.nextVideoTs=null,n.nextAudioTs=null,n.videoSampleDuration=null,n.isAudioContiguous=!1,n.isVideoContiguous=!1,n.videoTrackConfig=void 0,n.observer=t,n.config=r,n.typeSupported=i,n.ISGenerated=!1,null===wi){var s=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);wi=s?parseInt(s[1]):0}if(null===Oi){var o=navigator.userAgent.match(/Safari\/(\d+)/i);Oi=o?parseInt(o[1]):0}return n}o(t,e);var r=t.prototype;return r.destroy=function(){this.config=this.videoTrackConfig=this._initPTS=this._initDTS=null},r.resetTimeStamp=function(e){this.log("initPTS & initDTS reset"),this._initPTS=this._initDTS=e},r.resetNextTimestamp=function(){this.log("reset next timestamp"),this.isVideoContiguous=!1,this.isAudioContiguous=!1},r.resetInitSegment=function(){this.log("ISGenerated flag reset"),this.ISGenerated=!1,this.videoTrackConfig=void 0},r.getVideoStartPts=function(e){var t=!1,r=e[0].pts,i=e.reduce((function(e,i){var a=i.pts,n=a-e;return n<-4294967296&&(t=!0,n=(a=Ni(a,r))-e),n>0?e:a}),r);return t&&this.debug("PTS rollover detected"),i},r.remux=function(e,t,r,i,a,n,s,o){var l,u,d,h,f,c,v=a,g=a,m=e.pid>-1,p=t.pid>-1,y=t.samples.length,E=e.samples.length>0,T=s&&y>0||y>1;if((!m||E)&&(!p||T)||this.ISGenerated||s){if(this.ISGenerated){var S,L,R,A,b=this.videoTrackConfig;(b&&(t.width!==b.width||t.height!==b.height||(null==(S=t.pixelRatio)?void 0:S[0])!==(null==(L=b.pixelRatio)?void 0:L[0])||(null==(R=t.pixelRatio)?void 0:R[1])!==(null==(A=b.pixelRatio)?void 0:A[1]))||!b&&T||null===this.nextAudioTs&&E)&&this.resetInitSegment()}this.ISGenerated||(d=this.generateIS(e,t,a,n));var k,D=this.isVideoContiguous,_=-1;if(T&&(_=function(e){for(var t=0;t<e.length;t++)if(e[t].key)return t;return-1}(t.samples),!D&&this.config.forceKeyFrameOnDiscontinuity))if(c=!0,_>0){this.warn("Dropped "+_+" out of "+y+" video samples due to a missing keyframe");var I=this.getVideoStartPts(t.samples);t.samples=t.samples.slice(_),t.dropped+=_,k=g+=(t.samples[0].pts-I)/t.inputTimeScale}else-1===_&&(this.warn("No keyframe found out of "+y+" video samples"),c=!1);if(this.ISGenerated){if(E&&T){var C=this.getVideoStartPts(t.samples),P=(Ni(e.samples[0].pts,C)-C)/t.inputTimeScale;v+=Math.max(0,P),g+=Math.max(0,-P)}if(E){if(e.samplerate||(this.warn("regenerate InitSegment as audio detected"),d=this.generateIS(e,t,a,n)),u=this.remuxAudio(e,v,this.isAudioContiguous,n,p||T||o===w?g:void 0),T){var x=u?u.endPTS-u.startPTS:0;t.inputTimeScale||(this.warn("regenerate InitSegment as video detected"),d=this.generateIS(e,t,a,n)),l=this.remuxVideo(t,g,D,x)}}else T&&(l=this.remuxVideo(t,g,D,0));l&&(l.firstKeyFrame=_,l.independent=-1!==_,l.firstKeyFramePTS=k)}}return this.ISGenerated&&this._initPTS&&this._initDTS&&(r.samples.length&&(f=Bi(r,a,this._initPTS,this._initDTS)),i.samples.length&&(h=Ui(i,a,this._initPTS))),{audio:u,video:l,initSegment:d,independent:c,text:h,id3:f}},r.generateIS=function(e,t,r,i){var a,n,s,o=e.samples,l=t.samples,u=this.typeSupported,d={},h=this._initPTS,f=!h||i,c="audio/mp4",v=-1;if(f&&(a=n=1/0),e.config&&o.length){switch(e.timescale=e.samplerate,e.segmentCodec){case"mp3":u.mpeg?(c="audio/mpeg",e.codec=""):u.mp3&&(e.codec="mp3");break;case"ac3":e.codec="ac-3"}d.audio={id:"audio",container:c,codec:e.codec,initSegment:"mp3"===e.segmentCodec&&u.mpeg?new Uint8Array(0):Pi.initSegment([e]),metadata:{channelCount:e.channelCount}},f&&(v=e.id,s=e.inputTimeScale,h&&s===h.timescale?f=!1:a=n=o[0].pts-Math.round(s*r))}if(t.sps&&t.pps&&l.length){if(t.timescale=t.inputTimeScale,d.video={id:"main",container:"video/mp4",codec:t.codec,initSegment:Pi.initSegment([t]),metadata:{width:t.width,height:t.height}},f)if(v=t.id,s=t.inputTimeScale,h&&s===h.timescale)f=!1;else{var g=this.getVideoStartPts(l),m=Math.round(s*r);n=Math.min(n,Ni(l[0].dts,g)-m),a=Math.min(a,g-m)}this.videoTrackConfig={width:t.width,height:t.height,pixelRatio:t.pixelRatio}}if(Object.keys(d).length)return this.ISGenerated=!0,f?(h&&this.warn("Timestamps at playlist time: "+(i?"":"~")+r+" "+a/s+" != initPTS: "+h.baseTime/h.timescale+" ("+h.baseTime+"/"+h.timescale+") trackId: "+h.trackId),this.log("Found initPTS at playlist time: "+r+" offset: "+a/s+" ("+a+"/"+s+") trackId: "+v),this._initPTS={baseTime:a,timescale:s,trackId:v},this._initDTS={baseTime:n,timescale:s,trackId:v}):a=s=void 0,{tracks:d,initPTS:a,timescale:s,trackId:v}},r.remuxVideo=function(e,t,r,i){var a,s,o=e.inputTimeScale,l=e.samples,u=[],d=l.length,h=this._initPTS,f=h.baseTime*o/h.timescale,c=this.nextVideoTs,v=8,g=this.videoSampleDuration,m=Number.POSITIVE_INFINITY,p=Number.NEGATIVE_INFINITY,y=!1;if(!r||null===c){var E=f+t*o,T=l[0].pts-Ni(l[0].dts,l[0].pts);wi&&null!==c&&Math.abs(E-T-(c+f))<15e3?r=!0:c=E-T-f}for(var S=c+f,L=0;L<d;L++){var R=l[L];R.pts=Ni(R.pts,S),R.dts=Ni(R.dts,S),R.dts<l[L>0?L-1:L].dts&&(y=!0)}y&&l.sort((function(e,t){var r=e.dts-t.dts,i=e.pts-t.pts;return r||i})),a=l[0].dts;var A=(s=l[l.length-1].dts)-a,_=A?Math.round(A/(d-1)):g||e.inputTimeScale/30;if(r){var I=a-S,C=I>_,P=I<-1;if((C||P)&&(C?this.warn((e.segmentCodec||"").toUpperCase()+": "+xi(I)+" ms ("+I+"dts) hole between fragments detected at "+t.toFixed(3)):this.warn((e.segmentCodec||"").toUpperCase()+": "+xi(-I)+" ms ("+I+"dts) overlapping between fragments detected at "+t.toFixed(3)),!P||S>=l[0].pts||wi)){a=S;var x=l[0].pts-I;if(C)l[0].dts=a,l[0].pts=x;else for(var w=!0,O=0;O<l.length&&!(l[O].dts>x&&w);O++){var F=l[O].pts;if(l[O].dts-=I,l[O].pts-=I,O<l.length-1){var M=l[O+1].pts;w=M<=l[O].pts==M<=F}}this.log("Video: Initial PTS/DTS adjusted: "+xi(x)+"/"+xi(a)+", delta: "+xi(I)+" ms")}}for(var N=0,B=0,U=a=Math.max(0,a),G=0;G<d;G++){for(var V=l[G],H=V.units,K=H.length,W=0,Y=0;Y<K;Y++)W+=H[Y].data.length;B+=W,N+=K,V.length=W,V.dts<U?(V.dts=U,U+=_/4|0||1):U=V.dts,m=Math.min(V.pts,m),p=Math.max(V.pts,p)}s=l[d-1].dts;var j,q=B+4*N+8;try{j=new Uint8Array(q)}catch(e){return void this.observer.emit(D.ERROR,D.ERROR,{type:b.MUX_ERROR,details:k.REMUX_ALLOC_ERROR,fatal:!1,error:e,bytes:q,reason:"fail allocating video mdat "+q})}var X=new DataView(j.buffer);X.setUint32(0,q),j.set(Pi.types.mdat,4);for(var z=!1,Q=Number.POSITIVE_INFINITY,$=Number.POSITIVE_INFINITY,Z=Number.NEGATIVE_INFINITY,J=Number.NEGATIVE_INFINITY,ee=0;ee<d;ee++){for(var te=l[ee],re=te.units,ie=0,ae=0,ne=re.length;ae<ne;ae++){var se=re[ae],oe=se.data,le=se.data.byteLength;X.setUint32(v,le),v+=4,j.set(oe,v),v+=le,ie+=4+le}var ue=void 0;if(ee<d-1)g=l[ee+1].dts-te.dts,ue=l[ee+1].pts-te.pts;else{var de=this.config,he=ee>0?te.dts-l[ee-1].dts:_;if(ue=ee>0?te.pts-l[ee-1].pts:_,de.stretchShortVideoTrack&&null!==this.nextAudioTs){var fe=Math.floor(de.maxBufferHole*o),ce=(i?m+i*o:this.nextAudioTs+f)-te.pts;ce>fe?((g=ce-he)<0?g=he:z=!0,this.log("It is approximately "+ce/90+" ms to the next segment; using duration "+g/90+" ms for the last video frame.")):g=he}else g=he}var ve=Math.round(te.pts-te.dts);Q=Math.min(Q,g),Z=Math.max(Z,g),$=Math.min($,ue),J=Math.max(J,ue),u.push(Fi(te.key,g,ie,ve))}if(u.length)if(wi){if(wi<70){var ge=u[0].flags;ge.dependsOn=2,ge.isNonSync=0}}else if(Oi&&J-$<Z-Q&&_/Z<.025&&0===u[0].cts){this.warn("Found irregular gaps in sample duration. Using PTS instead of DTS to determine MP4 sample duration.");for(var me=a,pe=0,ye=u.length;pe<ye;pe++){var Ee=me+u[pe].duration,Te=me+u[pe].cts;if(pe<ye-1){var Se=Ee+u[pe+1].cts;u[pe].duration=Se-Te}else u[pe].duration=pe?u[pe-1].duration:_;u[pe].cts=0,me=Ee}}var Le=s+(g=z||!g?_:g);this.nextVideoTs=c=Le-f,this.videoSampleDuration=g,this.isVideoContiguous=!0;var Re={data1:Pi.moof(e.sequenceNumber++,a,n(e,{samples:u})),data2:j,startPTS:(m-f)/o,endPTS:(p+g-f)/o,startDTS:(a-f)/o,endDTS:c/o,type:"video",hasAudio:!1,hasVideo:!0,nb:u.length,dropped:e.dropped};return e.samples=[],e.dropped=0,Re},r.getSamplesPerFrame=function(e){switch(e.segmentCodec){case"mp3":return 1152;case"ac3":return 1536;default:return 1024}},r.remuxAudio=function(e,t,r,i,a){var s=e.inputTimeScale,o=s/(e.samplerate?e.samplerate:s),l=this.getSamplesPerFrame(e),u=l*o,d=this._initPTS,h="mp3"===e.segmentCodec&&this.typeSupported.mpeg,f=[],c=void 0!==a,v=e.samples,g=h?0:8,m=this.nextAudioTs||-1,p=d.baseTime*s/d.timescale,y=p+t*s;if(this.isAudioContiguous=r=r||v.length&&m>0&&(i&&Math.abs(y-(m+p))<9e3||Math.abs(Ni(v[0].pts,y)-(m+p))<20*u),v.forEach((function(e){e.pts=Ni(e.pts,y)})),!r||m<0){if(v=v.filter((function(e){return e.pts>=0})),!v.length)return;m=0===a?0:i&&!c?Math.max(0,y-p):v[0].pts-p}if("aac"===e.segmentCodec)for(var E=this.config.maxAudioFramesDrift,T=0,S=m+p;T<v.length;T++){var L=v[T],R=L.pts,A=R-S,_=Math.abs(1e3*A/s);if(A<=-E*u&&c)0===T&&(this.warn("Audio frame @ "+(R/s).toFixed(3)+"s overlaps marker by "+Math.round(1e3*A/s)+" ms."),this.nextAudioTs=m=R-p,S=R);else if(A>=E*u&&_<1e4&&c){var I=Math.round(A/u);for(S=R-I*u;S<0&&I&&u;)I--,S+=u;0===T&&(this.nextAudioTs=m=S-p),this.warn("Injecting "+I+" audio frames @ "+((S-p)/s).toFixed(3)+"s due to "+Math.round(1e3*A/s)+" ms gap.");for(var C=0;C<I;C++){var P=Ii.getSilentFrame(e.parsedCodec||e.manifestCodec||e.codec,e.channelCount);P||(this.log("Unable to get silent frame for given audio codec; duplicating last frame instead."),P=L.unit.subarray()),v.splice(T,0,{unit:P,pts:S}),S+=u,T++}}L.pts=S,S+=u}for(var x,w=null,O=null,F=0,M=v.length;M--;)F+=v[M].unit.byteLength;for(var N=0,B=v.length;N<B;N++){var U=v[N],G=U.unit,V=U.pts;if(null!==O)f[N-1].duration=Math.round((V-O)/o);else{if(r&&"aac"===e.segmentCodec&&(V=m+p),w=V,!(F>0))return;F+=g;try{x=new Uint8Array(F)}catch(e){return void this.observer.emit(D.ERROR,D.ERROR,{type:b.MUX_ERROR,details:k.REMUX_ALLOC_ERROR,fatal:!1,error:e,bytes:F,reason:"fail allocating audio mdat "+F})}h||(new DataView(x.buffer).setUint32(0,F),x.set(Pi.types.mdat,4))}x.set(G,g);var H=G.byteLength;g+=H,f.push(Fi(!0,l,H,0)),O=V}var K=f.length;if(K){var W=f[f.length-1];m=O-p,this.nextAudioTs=m+o*W.duration;var Y=h?new Uint8Array(0):Pi.moof(e.sequenceNumber++,w/o,n({},e,{samples:f}));e.samples=[];var j=(w-p)/s,q=m/s,X={data1:Y,data2:x,startPTS:j,endPTS:q,startDTS:j,endDTS:q,type:"audio",hasAudio:!0,hasVideo:!1,nb:K};return this.isAudioContiguous=!0,X}},t}(N);function Ni(e,t){var r;if(null===t)return e;for(r=t<e?-8589934592:8589934592;Math.abs(e-t)>4294967296;)e+=r;return e}function Bi(e,t,r,i){var a=e.samples.length;if(a){for(var n=e.inputTimeScale,s=0;s<a;s++){var o=e.samples[s];o.pts=Ni(o.pts-r.baseTime*n/r.timescale,t*n)/n,o.dts=Ni(o.dts-i.baseTime*n/i.timescale,t*n)/n}var l=e.samples;return e.samples=[],{samples:l}}}function Ui(e,t,r){var i=e.samples.length;if(i){for(var a=e.inputTimeScale,n=0;n<i;n++){var s=e.samples[n];s.pts=Ni(s.pts-r.baseTime*a/r.timescale,t*a)/a}e.samples.sort((function(e,t){return e.pts-t.pts}));var o=e.samples;return e.samples=[],{samples:o}}}var Gi,Vi=function(e){function t(t,r,i,a){var n;return(n=e.call(this,"passthrough-remuxer",a)||this).emitInitSegment=!1,n.audioCodec=void 0,n.videoCodec=void 0,n.initData=void 0,n.initPTS=null,n.initTracks=void 0,n.lastEndTime=null,n.isVideoContiguous=!1,n}o(t,e);var r=t.prototype;return r.destroy=function(){},r.resetTimeStamp=function(e){this.lastEndTime=null;var t=this.initPTS;t&&e&&t.baseTime===e.baseTime&&t.timescale===e.timescale||(this.initPTS=e)},r.resetNextTimestamp=function(){this.isVideoContiguous=!1,this.lastEndTime=null},r.resetInitSegment=function(e,t,r,i){this.audioCodec=t,this.videoCodec=r,this.generateInitSegment(e,i),this.emitInitSegment=!0},r.generateInitSegment=function(e,t){var r=this.audioCodec,i=this.videoCodec;if(null==e||!e.byteLength)return this.initTracks=void 0,void(this.initData=void 0);var a=this.initData=ye(e),n=a.audio,s=a.video;if(t)Ae(e,t);else{var o=n||s;null!=o&&o.encrypted&&this.warn('Init segment with encrypted track with has no key ("'+o.codec+'")!')}n&&(r=Ki(n,ee,this)),s&&(i=Ki(s,te,this));var l={};n&&s?l.audiovideo={container:"video/mp4",codec:r+","+i,supplemental:s.supplemental,encrypted:s.encrypted,initSegment:e,id:"main"}:n?l.audio={container:"audio/mp4",codec:r,encrypted:n.encrypted,initSegment:e,id:"audio"}:s?l.video={container:"video/mp4",codec:i,supplemental:s.supplemental,encrypted:s.encrypted,initSegment:e,id:"main"}:this.warn("initSegment does not contain moov or trak boxes."),this.initTracks=l},r.remux=function(e,t,r,i,a,n){var s,o,l=this.initPTS,u=this.lastEndTime,d={audio:void 0,video:void 0,text:i,id3:r,initSegment:void 0};L(u)||(u=this.lastEndTime=a||0);var h=t.samples;if(!h.length)return d;var f={initPTS:void 0,timescale:void 0,trackId:void 0},c=this.initData;if(null!=(s=c)&&s.length||(this.generateInitSegment(h),c=this.initData),null==(o=c)||!o.length)return this.warn("Failed to generate initSegment."),d;this.emitInitSegment&&(f.tracks=this.initTracks,this.emitInitSegment=!1);var v=function(e,t,r){for(var i={},a=me(e,["moof","traf"]),n=0;n<a.length;n++){var s=a[n],o=me(s,["tfhd"])[0],l=ce(o,4),u=t[l];if(u){i[l]||(i[l]={start:NaN,duration:0,sampleCount:0,timescale:u.timescale,type:u.type});var d=i[l],h=me(s,["tfdt"])[0];if(h){var f=h[0],c=ce(h,4);1===f&&(c===le?r.warn("[mp4-demuxer]: Ignoring assumed invalid signed 64-bit track fragment decode time"):(c*=le+1,c+=ce(h,8))),L(c)&&(!L(d.start)||c<d.start)&&(d.start=c)}var v=u.default,g=ce(o,0)|(null==v?void 0:v.flags),m=(null==v?void 0:v.duration)||0;8&g&&(m=ce(o,2&g?12:8));for(var p=me(s,["trun"]),y=d.start||0,E=0,T=m,S=0;S<p.length;S++){var R=p[S],A=ce(R,4),b=d.sampleCount;d.sampleCount+=A;var k=1&R[3],D=4&R[3],_=1&R[2],I=2&R[2],C=4&R[2],P=8&R[2],x=8,w=A;for(k&&(x+=4),D&&A&&(1&R[x+1]||void 0!==d.keyFrameIndex||(d.keyFrameIndex=b),x+=4,_?(T=ce(R,x),x+=4):T=m,I&&(x+=4),P&&(x+=4),y+=T,E+=T,w--);w--;)_?(T=ce(R,x),x+=4):T=m,I&&(x+=4),C&&(1&R[x+1]||void 0===d.keyFrameIndex&&(d.keyFrameIndex=d.sampleCount-(w+1),d.keyFrameStart=y),x+=4),P&&(x+=4),y+=T,E+=T;!E&&m&&(E+=m*A)}d.duration+=E}}if(!Object.keys(i).some((function(e){return i[e].duration}))){for(var O=1/0,F=0,M=me(e,["sidx"]),N=0;N<M.length;N++){var B=pe(M[N]);if(null!=B&&B.references){O=Math.min(O,B.earliestPresentationTime/B.timescale);var U=B.references.reduce((function(e,t){return e+t.info.duration||0}),0);F=Math.max(F,U+B.earliestPresentationTime/B.timescale)}}F&&L(F)&&Object.keys(i).forEach((function(e){i[e].duration||(i[e].duration=F*i[e].timescale-i[e].start)}))}return i}(h,c,this),g=c.audio?v[c.audio.id]:null,m=c.video?v[c.video.id]:null,p=Hi(m,1/0),y=Hi(g,1/0),E=Hi(m,0,!0),T=Hi(g,0,!0),S=a,R=0,A=g&&(!m||!l&&y<p||l&&l.trackId===c.audio.id),b=A?g:m;if(b){var k=b.timescale,D=b.start-a*k,_=A?c.audio.id:c.video.id;S=b.start/k,R=A?T-y:E-p,!n&&l||!function(e,t,r,i){if(null===e)return!0;var a=Math.max(i,1),n=t-e.baseTime/e.timescale;return Math.abs(n-r)>a}(l,S,a,R)&&k===l.timescale||(l&&this.warn("Timestamps at playlist time: "+(n?"":"~")+a+" "+D/k+" != initPTS: "+l.baseTime/l.timescale+" ("+l.baseTime+"/"+l.timescale+") trackId: "+l.trackId),this.log("Found initPTS at playlist time: "+a+" offset: "+(S-a)+" ("+D+"/"+k+") trackId: "+_),l=null,f.initPTS=D,f.timescale=k,f.trackId=_)}else this.warn("No audio or video samples found for initPTS at playlist time: "+a);l?(f.initPTS=l.baseTime,f.timescale=l.timescale,f.trackId=l.trackId):(f.timescale&&void 0!==f.trackId&&void 0!==f.initPTS||(this.warn("Could not set initPTS"),f.initPTS=S,f.timescale=1,f.trackId=-1),this.initPTS=l={baseTime:f.initPTS,timescale:f.timescale,trackId:f.trackId});var I=S-l.baseTime/l.timescale,C=I+R;R>0?this.lastEndTime=C:(this.warn("Duration parsed from mp4 should be greater than zero"),this.resetNextTimestamp());var P=!!c.audio,x=!!c.video,w="";P&&(w+="audio"),x&&(w+="video");var O={data1:h,startPTS:I,startDTS:I,endPTS:C,endDTS:C,type:w,hasAudio:P,hasVideo:x,nb:1,dropped:0,encrypted:!!c.audio&&c.audio.encrypted||!!c.video&&c.video.encrypted};d.audio=P&&!x?O:void 0,d.video=x?O:void 0;var F=null==m?void 0:m.sampleCount;if(F){var M=m.keyFrameIndex,N=-1!==M;O.nb=F,O.dropped=0===M||this.isVideoContiguous?0:N?M:F,O.independent=N,O.firstKeyFrame=M,N&&m.keyFrameStart&&(O.firstKeyFramePTS=(m.keyFrameStart-l.baseTime)/l.timescale),this.isVideoContiguous||(d.independent=N),this.isVideoContiguous||(this.isVideoContiguous=N),O.dropped&&this.warn("fmp4 does not start with IDR: firstIDR "+M+"/"+F+" dropped: "+O.dropped+" start: "+(O.firstKeyFramePTS||"NA"))}return d.initSegment=f,d.id3=Bi(r,a,l,l),i.samples.length&&(d.text=Ui(i,a,l)),d},t}(N);function Hi(e,t,r){return void 0===r&&(r=!1),void 0!==(null==e?void 0:e.start)?(e.start+(r?e.duration:0))/e.timescale:t}function Ki(e,t,r){var i=e.codec;return i&&i.length>4?i:t===ee?"ec-3"===i||"ac-3"===i||"alac"===i?i:"fLaC"===i||"Opus"===i?Ve(i,!1):(r.warn('Unhandled audio codec "'+i+'" in mp4 MAP'),i||"mp4a"):(r.warn('Unhandled video codec "'+i+'" in mp4 MAP'),i||"avc1")}try{Gi=self.performance.now.bind(self.performance)}catch(e){Gi=Date.now}var Wi=[{demux:gi,remux:Vi},{demux:Li,remux:Mi},{demux:li,remux:Mi},{demux:ui,remux:Mi}],Yi=function(){function e(e,t,r,i,a,n){this.asyncResult=!1,this.logger=void 0,this.observer=void 0,this.typeSupported=void 0,this.config=void 0,this.id=void 0,this.demuxer=void 0,this.remuxer=void 0,this.decrypter=void 0,this.probe=void 0,this.decryptionPromise=null,this.transmuxConfig=void 0,this.currentTransmuxState=void 0,this.observer=e,this.typeSupported=t,this.config=r,this.id=a,this.logger=n}var t=e.prototype;return t.configure=function(e){this.transmuxConfig=e,this.decrypter&&this.decrypter.reset()},t.push=function(e,t,r,i){var a=this,n=r.transmuxing;n.executeStart=Gi();var s=new Uint8Array(e),o=this.currentTransmuxState,l=this.transmuxConfig;i&&(this.currentTransmuxState=i);var u=i||o,d=u.contiguous,h=u.discontinuity,f=u.trackSwitch,c=u.accurateTimeOffset,v=u.timeOffset,g=u.initSegmentChange,m=l.audioCodec,p=l.videoCodec,y=l.defaultInitPts,E=l.duration,T=l.initSegmentData,S=function(e,t){var r=null;return e.byteLength>0&&null!=(null==t?void 0:t.key)&&null!==t.iv&&null!=t.method&&(r=t),r}(s,t);if(S&&Wt(S.method)){var L=this.getDecrypter(),R=Yt(S.method);if(!L.isSync())return this.asyncResult=!0,this.decryptionPromise=L.webCryptoDecrypt(s,S.key.buffer,S.iv.buffer,R).then((function(e){var t=a.push(e,null,r);return a.decryptionPromise=null,t})),this.decryptionPromise;var A=L.softwareDecrypt(s,S.key.buffer,S.iv.buffer,R);if(r.part>-1){var _=L.flush();A=_?_.buffer:_}if(!A)return n.executeEnd=Gi(),ji(r);s=new Uint8Array(A)}var I=this.needsProbing(h,f);if(I){var C=this.configureTransmuxer(s);if(C)return this.logger.warn("[transmuxer] "+C.message),this.observer.emit(D.ERROR,D.ERROR,{type:b.MEDIA_ERROR,details:k.FRAG_PARSING_ERROR,fatal:!1,error:C,reason:C.message}),n.executeEnd=Gi(),ji(r)}(h||f||g||I)&&this.resetInitSegment(T,m,p,E,t),(h||g||I)&&this.resetInitialTimestamp(y),d||this.resetContiguity();var P=this.transmux(s,S,v,c,r);this.asyncResult=qi(P);var x=this.currentTransmuxState;return x.contiguous=!0,x.discontinuity=!1,x.trackSwitch=!1,n.executeEnd=Gi(),P},t.flush=function(e){var t=this,r=e.transmuxing;r.executeStart=Gi();var i=this.decrypter,a=this.currentTransmuxState,n=this.decryptionPromise;if(n)return this.asyncResult=!0,n.then((function(){return t.flush(e)}));var s=[],o=a.timeOffset;if(i){var l=i.flush();l&&s.push(this.push(l.buffer,null,e))}var u=this.demuxer,d=this.remuxer;if(!u||!d){r.executeEnd=Gi();var h=[ji(e)];return this.asyncResult?Promise.resolve(h):h}var f=u.flush(o);return qi(f)?(this.asyncResult=!0,f.then((function(r){return t.flushRemux(s,r,e),s}))):(this.flushRemux(s,f,e),this.asyncResult?Promise.resolve(s):s)},t.flushRemux=function(e,t,r){var i=t.audioTrack,a=t.videoTrack,n=t.id3Track,s=t.textTrack,o=this.currentTransmuxState,l=o.accurateTimeOffset,u=o.timeOffset;this.logger.log("[transmuxer.ts]: Flushed "+this.id+" sn: "+r.sn+(r.part>-1?" part: "+r.part:"")+" of "+(this.id===x?"level":"track")+" "+r.level);var d=this.remuxer.remux(i,a,n,s,u,l,!0,this.id);e.push({remuxResult:d,chunkMeta:r}),r.transmuxing.executeEnd=Gi()},t.resetInitialTimestamp=function(e){var t=this.demuxer,r=this.remuxer;t&&r&&(t.resetTimeStamp(e),r.resetTimeStamp(e))},t.resetContiguity=function(){var e=this.demuxer,t=this.remuxer;e&&t&&(e.resetContiguity(),t.resetNextTimestamp())},t.resetInitSegment=function(e,t,r,i,a){var n=this.demuxer,s=this.remuxer;n&&s&&(n.resetInitSegment(e,t,r,i),s.resetInitSegment(e,t,r,a))},t.destroy=function(){this.demuxer&&(this.demuxer.destroy(),this.demuxer=void 0),this.remuxer&&(this.remuxer.destroy(),this.remuxer=void 0)},t.transmux=function(e,t,r,i,a){return t&&"SAMPLE-AES"===t.method?this.transmuxSampleAes(e,t,r,i,a):this.transmuxUnencrypted(e,r,i,a)},t.transmuxUnencrypted=function(e,t,r,i){var a=this.demuxer.demux(e,t,!1,!this.config.progressive),n=a.audioTrack,s=a.videoTrack,o=a.id3Track,l=a.textTrack;return{remuxResult:this.remuxer.remux(n,s,o,l,t,r,!1,this.id),chunkMeta:i}},t.transmuxSampleAes=function(e,t,r,i,a){var n=this;return this.demuxer.demuxSampleAes(e,t,r).then((function(e){return{remuxResult:n.remuxer.remux(e.audioTrack,e.videoTrack,e.id3Track,e.textTrack,r,i,!1,n.id),chunkMeta:a}}))},t.configureTransmuxer=function(e){for(var t,r=this.config,i=this.observer,a=this.typeSupported,n=0,s=Wi.length;n<s;n++){var o;if(null!=(o=Wi[n].demux)&&o.probe(e,this.logger)){t=Wi[n];break}}if(!t)return new Error("Failed to find demuxer by probing fragment data");var l=this.demuxer,u=this.remuxer,d=t.remux,h=t.demux;u&&u instanceof d||(this.remuxer=new d(i,r,a,this.logger)),l&&l instanceof h||(this.demuxer=new h(i,r,a,this.logger),this.probe=h.probe)},t.needsProbing=function(e,t){return!this.demuxer||!this.remuxer||e||t},t.getDecrypter=function(){var e=this.decrypter;return e||(e=this.decrypter=new ci(this.config)),e},e}(),ji=function(e){return{remuxResult:{},chunkMeta:e}};function qi(e){return"then"in e&&e.then instanceof Function}var Xi=function(e,t,r,i,a){this.audioCodec=void 0,this.videoCodec=void 0,this.initSegmentData=void 0,this.duration=void 0,this.defaultInitPts=void 0,this.audioCodec=e,this.videoCodec=t,this.initSegmentData=r,this.duration=i,this.defaultInitPts=a||null},zi=function(e,t,r,i,a,n){this.discontinuity=void 0,this.contiguous=void 0,this.accurateTimeOffset=void 0,this.trackSwitch=void 0,this.timeOffset=void 0,this.initSegmentChange=void 0,this.discontinuity=e,this.contiguous=t,this.accurateTimeOffset=r,this.trackSwitch=i,this.timeOffset=a,this.initSegmentChange=n},Qi=/(\d+)-(\d+)\/(\d+)/,$i=function(){function e(e){this.fetchSetup=void 0,this.requestTimeout=void 0,this.request=null,this.response=null,this.controller=void 0,this.context=null,this.config=null,this.callbacks=null,this.stats=void 0,this.loader=null,this.fetchSetup=e.fetchSetup||Zi,this.controller=new self.AbortController,this.stats=new J}var t=e.prototype;return t.destroy=function(){this.loader=this.callbacks=this.context=this.config=this.request=null,this.abortInternal(),this.response=null,this.fetchSetup=this.controller=this.stats=null},t.abortInternal=function(){this.controller&&!this.stats.loading.end&&(this.stats.aborted=!0,this.controller.abort())},t.abort=function(){var e;this.abortInternal(),null!=(e=this.callbacks)&&e.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.response)},t.load=function(e,t,r){var i=this,a=this.stats;if(a.loading.start)throw new Error("Loader can only be used once.");a.loading.start=self.performance.now();var s=function(e,t){var r={method:"GET",mode:"cors",credentials:"same-origin",signal:t,headers:new self.Headers(n({},e.headers))};return e.rangeEnd&&r.headers.set("Range","bytes="+e.rangeStart+"-"+String(e.rangeEnd-1)),r}(e,this.controller.signal),o="arraybuffer"===e.responseType,l=o?"byteLength":"length",u=t.loadPolicy,d=u.maxTimeToFirstByteMs,h=u.maxLoadTimeMs;this.context=e,this.config=t,this.callbacks=r,this.request=this.fetchSetup(e,s),self.clearTimeout(this.requestTimeout),t.timeout=d&&L(d)?d:h,this.requestTimeout=self.setTimeout((function(){i.callbacks&&(i.abortInternal(),i.callbacks.onTimeout(a,e,i.response))}),t.timeout),(qi(this.request)?this.request.then(self.fetch):self.fetch(this.request)).then((function(r){var n;i.response=i.loader=r;var s=Math.max(self.performance.now(),a.loading.start);if(self.clearTimeout(i.requestTimeout),t.timeout=h,i.requestTimeout=self.setTimeout((function(){i.callbacks&&(i.abortInternal(),i.callbacks.onTimeout(a,e,i.response))}),h-(s-a.loading.start)),!r.ok){var l=r.status,u=r.statusText;throw new Ji(u||"fetch, bad network response",l,r)}a.loading.first=s,a.total=function(e){var t=e.get("Content-Range");if(t){var r=function(e){var t=Qi.exec(e);if(t)return parseInt(t[2])-parseInt(t[1])+1}(t);if(L(r))return r}var i=e.get("Content-Length");if(i)return parseInt(i)}(r.headers)||a.total;var d=null==(n=i.callbacks)?void 0:n.onProgress;return d&&L(t.highWaterMark)?i.loadProgressively(r,a,e,t.highWaterMark,d):o?r.arrayBuffer():"json"===e.responseType?r.json():r.text()})).then((function(r){var n,s,o=i.response;if(!o)throw new Error("loader destroyed");self.clearTimeout(i.requestTimeout),a.loading.end=Math.max(self.performance.now(),a.loading.first);var u=r[l];u&&(a.loaded=a.total=u);var d={url:o.url,data:r,code:o.status},h=null==(n=i.callbacks)?void 0:n.onProgress;h&&!L(t.highWaterMark)&&h(a,e,r,o),null==(s=i.callbacks)||s.onSuccess(d,a,e,o)})).catch((function(t){var r;if(self.clearTimeout(i.requestTimeout),!a.aborted){var n=t&&t.code||0,s=t?t.message:null;null==(r=i.callbacks)||r.onError({code:n,text:s},e,t?t.details:null,a)}}))},t.getCacheAge=function(){var e=null;if(this.response){var t=this.response.headers.get("age");e=t?parseFloat(t):null}return e},t.getResponseHeader=function(e){return this.response?this.response.headers.get(e):null},t.loadProgressively=function(e,t,r,i,a){void 0===i&&(i=0);var n=new Ar,s=e.body.getReader(),o=function(){return s.read().then((function(s){if(s.done)return n.dataLength&&a(t,r,n.flush().buffer,e),Promise.resolve(new ArrayBuffer(0));var l=s.value,u=l.length;return t.loaded+=u,u<i||n.dataLength?(n.push(l),n.dataLength>=i&&a(t,r,n.flush().buffer,e)):a(t,r,l.buffer,e),o()})).catch((function(){return Promise.reject()}))};return o()},e}();function Zi(e,t){return new self.Request(e.url,t)}var Ji=function(e){function t(t,r,i){var a;return(a=e.call(this,t)||this).code=void 0,a.details=void 0,a.code=r,a.details=i,a}return o(t,e),t}(c(Error)),ea=/^age:\s*[\d.]+\s*$/im,ta=function(){function e(e){this.xhrSetup=void 0,this.requestTimeout=void 0,this.retryTimeout=void 0,this.retryDelay=void 0,this.config=null,this.callbacks=null,this.context=null,this.loader=null,this.stats=void 0,this.xhrSetup=e&&e.xhrSetup||null,this.stats=new J,this.retryDelay=0}var t=e.prototype;return t.destroy=function(){this.callbacks=null,this.abortInternal(),this.loader=null,this.config=null,this.context=null,this.xhrSetup=null},t.abortInternal=function(){var e=this.loader;self.clearTimeout(this.requestTimeout),self.clearTimeout(this.retryTimeout),e&&(e.onreadystatechange=null,e.onprogress=null,4!==e.readyState&&(this.stats.aborted=!0,e.abort()))},t.abort=function(){var e;this.abortInternal(),null!=(e=this.callbacks)&&e.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.loader)},t.load=function(e,t,r){if(this.stats.loading.start)throw new Error("Loader can only be used once.");this.stats.loading.start=self.performance.now(),this.context=e,this.config=t,this.callbacks=r,this.loadInternal()},t.loadInternal=function(){var e=this,t=this.config,r=this.context;if(t&&r){var i=this.loader=new self.XMLHttpRequest,a=this.stats;a.loading.first=0,a.loaded=0,a.aborted=!1;var n=this.xhrSetup;n?Promise.resolve().then((function(){if(e.loader===i&&!e.stats.aborted)return n(i,r.url)})).catch((function(t){if(e.loader===i&&!e.stats.aborted)return i.open("GET",r.url,!0),n(i,r.url)})).then((function(){e.loader!==i||e.stats.aborted||e.openAndSendXhr(i,r,t)})).catch((function(t){var n;null==(n=e.callbacks)||n.onError({code:i.status,text:t.message},r,i,a)})):this.openAndSendXhr(i,r,t)}},t.openAndSendXhr=function(e,t,r){e.readyState||e.open("GET",t.url,!0);var i=t.headers,a=r.loadPolicy,n=a.maxTimeToFirstByteMs,s=a.maxLoadTimeMs;if(i)for(var o in i)e.setRequestHeader(o,i[o]);t.rangeEnd&&e.setRequestHeader("Range","bytes="+t.rangeStart+"-"+(t.rangeEnd-1)),e.onreadystatechange=this.readystatechange.bind(this),e.onprogress=this.loadprogress.bind(this),e.responseType=t.responseType,self.clearTimeout(this.requestTimeout),r.timeout=n&&L(n)?n:s,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),r.timeout),e.send()},t.readystatechange=function(){var e=this.context,t=this.loader,r=this.stats;if(e&&t){var i=t.readyState,a=this.config;if(!r.aborted&&i>=2&&(0===r.loading.first&&(r.loading.first=Math.max(self.performance.now(),r.loading.start),a.timeout!==a.loadPolicy.maxLoadTimeMs&&(self.clearTimeout(this.requestTimeout),a.timeout=a.loadPolicy.maxLoadTimeMs,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),a.loadPolicy.maxLoadTimeMs-(r.loading.first-r.loading.start)))),4===i)){self.clearTimeout(this.requestTimeout),t.onreadystatechange=null,t.onprogress=null;var n=t.status,s="text"===t.responseType?t.responseText:null;if(n>=200&&n<300){var o=null!=s?s:t.response;if(null!=o){var l,u;r.loading.end=Math.max(self.performance.now(),r.loading.first);var d="arraybuffer"===t.responseType?o.byteLength:o.length;r.loaded=r.total=d,r.bwEstimate=8e3*r.total/(r.loading.end-r.loading.first);var h=null==(l=this.callbacks)?void 0:l.onProgress;h&&h(r,e,o,t);var f={url:t.responseURL,data:o,code:n};return void(null==(u=this.callbacks)||u.onSuccess(f,r,e,t))}}var c,v=a.loadPolicy.errorRetry;pt(v,r.retry,!1,{url:e.url,data:void 0,code:n})?this.retry(v):(j.error(n+" while loading "+e.url),null==(c=this.callbacks)||c.onError({code:n,text:t.statusText},e,t,r))}}},t.loadtimeout=function(){if(this.config){var e=this.config.loadPolicy.timeoutRetry;if(pt(e,this.stats.retry,!0))this.retry(e);else{var t;j.warn("timeout while loading "+(null==(t=this.context)?void 0:t.url));var r=this.callbacks;r&&(this.abortInternal(),r.onTimeout(this.stats,this.context,this.loader))}}},t.retry=function(e){var t=this.context,r=this.stats;this.retryDelay=gt(e,r.retry),r.retry++,j.warn((status?"HTTP Status "+status:"Timeout")+" while loading "+(null==t?void 0:t.url)+", retrying "+r.retry+"/"+e.maxNumRetry+" in "+this.retryDelay+"ms"),this.abortInternal(),this.loader=null,self.clearTimeout(this.retryTimeout),this.retryTimeout=self.setTimeout(this.loadInternal.bind(this),this.retryDelay)},t.loadprogress=function(e){var t=this.stats;t.loaded=e.loaded,e.lengthComputable&&(t.total=e.total)},t.getCacheAge=function(){var e=null;if(this.loader&&ea.test(this.loader.getAllResponseHeaders())){var t=this.loader.getResponseHeader("age");e=t?parseFloat(t):null}return e},t.getResponseHeader=function(e){return this.loader&&new RegExp("^"+e+":\\s*[\\d.]+\\s*$","im").test(this.loader.getAllResponseHeaders())?this.loader.getResponseHeader(e):null},e}(),ra={maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:null,errorRetry:null},ia=d(d({autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,ignoreDevicePixelRatio:!1,maxDevicePixelRatio:Number.POSITIVE_INFINITY,preferManagedMediaSource:!0,initialLiveManifestSize:1,maxBufferLength:30,backBufferLength:1/0,frontBufferFlushThreshold:1/0,startOnSegmentBoundary:!1,maxBufferSize:6e7,maxFragLookUpTolerance:.25,maxBufferHole:.1,detectStallWithCurrentTimeMs:1250,highBufferWatchdogPeriod:2,nudgeOffset:.1,nudgeMaxRetry:3,nudgeOnVideoHole:!0,liveSyncMode:"edge",liveSyncDurationCount:3,liveSyncOnStallIncrease:1,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,maxLiveSyncPlaybackRate:1,liveDurationInfinity:!1,liveBackBufferLength:null,maxMaxBufferLength:600,enableWorker:!0,workerPath:null,enableSoftwareAES:!0,startLevel:void 0,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,ignorePlaylistParsingErrors:!1,loader:ta,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,licenseXhrSetup:void 0,licenseResponseCallback:void 0,abrController:ot,bufferController:xt,capLevelController:Ft,errorController:bt,fpsController:Rr,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrEwmaDefaultEstimateMax:5e6,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0,emeEnabled:!1,widevineLicenseUrl:void 0,drmSystems:{},drmSystemOptions:{},requestMediaKeySystemAccessFunc:null,requireKeySystemAccessOnStart:!1,testBandwidth:!0,progressive:!1,lowLatencyMode:!0,cmcd:void 0,enableDateRangeMetadataCues:!0,enableEmsgMetadataCues:!0,enableEmsgKLVMetadata:!1,enableID3MetadataCues:!0,enableInterstitialPlayback:!1,interstitialAppendInPlace:!0,interstitialLiveLookAhead:10,useMediaCapabilities:!1,preserveManualLevelOnError:!1,certLoadPolicy:{default:ra},keyLoadPolicy:{default:{maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"},errorRetry:{maxNumRetry:8,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"}}},manifestLoadPolicy:{default:{maxTimeToFirstByteMs:1/0,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},playlistLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:2,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},fragLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:12e4,timeoutRetry:{maxNumRetry:4,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:6,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},steeringManifestLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},interstitialAssetListLoadPolicy:{default:ra},manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3},{cueHandler:X,enableWebVTT:!1,enableIMSC1:!1,enableCEA708Captions:!1,captionsTextTrack1Label:"English",captionsTextTrack1LanguageCode:"en",captionsTextTrack2Label:"Spanish",captionsTextTrack2LanguageCode:"es",captionsTextTrack3Label:"Unknown CC",captionsTextTrack3LanguageCode:"",captionsTextTrack4Label:"Unknown CC",captionsTextTrack4LanguageCode:"",renderTextTracksNatively:!0}),{},{subtitleStreamController:void 0,subtitleTrackController:void 0,timelineController:void 0,audioStreamController:void 0,audioTrackController:void 0,emeController:void 0,cmcdController:void 0,contentSteeringController:Tr,interstitialsController:void 0});function aa(e){return e&&"object"==typeof e?Array.isArray(e)?e.map(aa):Object.keys(e).reduce((function(t,r){return t[r]=aa(e[r]),t}),{}):e}function na(e,t){var r=e.loader;r!==$i&&r!==ta?(t.log("[config]: Custom loader detected, cannot enable progressive streaming"),e.progressive=!1):function(){if(self.fetch&&self.AbortController&&self.ReadableStream&&self.Request)try{return new self.ReadableStream({}),!0}catch(e){}return!1}()&&(e.loader=$i,e.progressive=!0,e.enableSoftwareAES=!0,t.log("[config]: Progressive streaming enabled, using FetchLoader"))}var sa="NOT_LOADED",oa="APPENDING",la="PARTIAL",ua="OK",da=function(){function e(e){this.activePartLists=Object.create(null),this.endListFragments=Object.create(null),this.fragments=Object.create(null),this.timeRanges=Object.create(null),this.bufferPadding=.2,this.hls=void 0,this.hasGaps=!1,this.hls=e,this._registerListeners()}var t=e.prototype;return t._registerListeners=function(){var e=this.hls;e&&(e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.BUFFER_APPENDED,this.onBufferAppended,this),e.on(D.FRAG_BUFFERED,this.onFragBuffered,this),e.on(D.FRAG_LOADED,this.onFragLoaded,this))},t._unregisterListeners=function(){var e=this.hls;e&&(e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.BUFFER_APPENDED,this.onBufferAppended,this),e.off(D.FRAG_BUFFERED,this.onFragBuffered,this),e.off(D.FRAG_LOADED,this.onFragLoaded,this))},t.destroy=function(){this._unregisterListeners(),this.hls=this.fragments=this.activePartLists=this.endListFragments=this.timeRanges=null},t.getAppendedFrag=function(e,t){var r=this.activePartLists[t];if(r)for(var i=r.length;i--;){var a=r[i];if(!a)break;if(a.start<=e&&e<=a.end&&a.loaded)return a}return this.getBufferedFrag(e,t)},t.getBufferedFrag=function(e,t){return this.getFragAtPos(e,t,!0)},t.getFragAtPos=function(e,t,r){for(var i=this.fragments,a=Object.keys(i),n=a.length;n--;){var s=i[a[n]];if((null==s?void 0:s.body.type)===t&&(!r||s.buffered)){var o=s.body;if(o.start<=e&&e<=o.end)return o}}return null},t.detectEvictedFragments=function(e,t,r,i,a){var n=this;this.timeRanges&&(this.timeRanges[e]=t);var s=(null==i?void 0:i.fragment.sn)||-1;Object.keys(this.fragments).forEach((function(i){var o=n.fragments[i];if(o&&!(s>=o.body.sn))if(o.buffered||o.loaded&&!a){var l=o.range[e];l&&(0!==l.time.length?l.time.some((function(e){var r=!n.isTimeBuffered(e.startPTS,e.endPTS,t);return r&&n.removeFragment(o.body),r})):n.removeFragment(o.body))}else o.body.type===r&&n.removeFragment(o.body)}))},t.detectPartialFragments=function(e){var t=this,r=this.timeRanges;if(r&&"initSegment"!==e.frag.sn){var i=e.frag,a=fa(i),n=this.fragments[a];if(!(!n||n.buffered&&i.gap)){var s=!i.relurl;Object.keys(r).forEach((function(a){var o=i.elementaryStreams[a];if(o){var l=r[a],u=s||!0===o.partial;n.range[a]=t.getBufferedTimes(i,e.part,u,l)}})),n.loaded=null,Object.keys(n.range).length?(n.buffered=!0,(n.body.endList=i.endList||n.body.endList)&&(this.endListFragments[n.body.type]=n),ha(n)||this.removeParts(i.sn-1,i.type)):this.removeFragment(n.body)}}},t.removeParts=function(e,t){var r=this.activePartLists[t];r&&(this.activePartLists[t]=ca(r,(function(t){return t.fragment.sn>=e})))},t.fragBuffered=function(e,t){var r=fa(e),i=this.fragments[r];!i&&t&&(i=this.fragments[r]={body:e,appendedPTS:null,loaded:null,buffered:!1,range:Object.create(null)},e.gap&&(this.hasGaps=!0)),i&&(i.loaded=null,i.buffered=!0)},t.getBufferedTimes=function(e,t,r,i){for(var a={time:[],partial:r},n=e.start,s=e.end,o=e.minEndPTS||s,l=e.maxStartPTS||n,u=0;u<i.length;u++){var d=i.start(u)-this.bufferPadding,h=i.end(u)+this.bufferPadding;if(l>=d&&o<=h){a.time.push({startPTS:Math.max(n,i.start(u)),endPTS:Math.min(s,i.end(u))});break}if(n<h&&s>d){var f=Math.max(n,i.start(u)),c=Math.min(s,i.end(u));c>f&&(a.partial=!0,a.time.push({startPTS:f,endPTS:c}))}else if(s<=d)break}return a},t.getPartialFragment=function(e){var t,r,i,a=null,n=0,s=this.bufferPadding,o=this.fragments;return Object.keys(o).forEach((function(l){var u=o[l];u&&ha(u)&&(r=u.body.start-s,i=u.body.end+s,e>=r&&e<=i&&(t=Math.min(e-r,i-e),n<=t&&(a=u.body,n=t)))})),a},t.isEndListAppended=function(e){var t=this.endListFragments[e];return void 0!==t&&(t.buffered||ha(t))},t.getState=function(e){var t=fa(e),r=this.fragments[t];return r?r.buffered?ha(r)?la:ua:oa:sa},t.isTimeBuffered=function(e,t,r){for(var i,a,n=0;n<r.length;n++){if(i=r.start(n)-this.bufferPadding,a=r.end(n)+this.bufferPadding,e>=i&&t<=a)return!0;if(t<=i)return!1}return!1},t.onManifestLoading=function(){this.removeAllFragments()},t.onFragLoaded=function(e,t){if("initSegment"!==t.frag.sn&&!t.frag.bitrateTest){var r=t.frag,i=t.part?null:t,a=fa(r);this.fragments[a]={body:r,appendedPTS:null,loaded:i,buffered:!1,range:Object.create(null)}}},t.onBufferAppended=function(e,t){var r=t.frag,i=t.part,a=t.timeRanges,n=t.type;if("initSegment"!==r.sn){var s=r.type;if(i){var o=this.activePartLists[s];o||(this.activePartLists[s]=o=[]),o.push(i)}this.timeRanges=a;var l=a[n];this.detectEvictedFragments(n,l,s,i)}},t.onFragBuffered=function(e,t){this.detectPartialFragments(t)},t.hasFragment=function(e){var t=fa(e);return!!this.fragments[t]},t.hasFragments=function(e){var t=this.fragments,r=Object.keys(t);if(!e)return r.length>0;for(var i=r.length;i--;){var a=t[r[i]];if((null==a?void 0:a.body.type)===e)return!0}return!1},t.hasParts=function(e){var t;return!(null==(t=this.activePartLists[e])||!t.length)},t.removeFragmentsInRange=function(e,t,r,i,a){var n=this;i&&!this.hasGaps||Object.keys(this.fragments).forEach((function(s){var o=n.fragments[s];if(o){var l=o.body;l.type!==r||i&&!l.gap||l.start<t&&l.end>e&&(o.buffered||a)&&n.removeFragment(l)}}))},t.removeFragment=function(e){var t=fa(e);e.clearElementaryStreamInfo();var r=this.activePartLists[e.type];if(r){var i=e.sn;this.activePartLists[e.type]=ca(r,(function(e){return e.fragment.sn!==i}))}delete this.fragments[t],e.endList&&delete this.endListFragments[e.type]},t.removeAllFragments=function(){var e;this.fragments=Object.create(null),this.endListFragments=Object.create(null),this.activePartLists=Object.create(null),this.hasGaps=!1;var t=null==(e=this.hls)||null==(e=e.latestLevelDetails)?void 0:e.partList;t&&t.forEach((function(e){return e.clearElementaryStreamInfo()}))},e}();function ha(e){var t,r,i;return e.buffered&&!!(e.body.gap||null!=(t=e.range.video)&&t.partial||null!=(r=e.range.audio)&&r.partial||null!=(i=e.range.audiovideo)&&i.partial)}function fa(e){return e.type+"_"+e.level+"_"+e.sn}function ca(e,t){return e.filter((function(e){var r=t(e);return r||e.clearElementaryStreamInfo(),r}))}var va=Math.pow(2,17),ga=function(){function e(e){this.config=void 0,this.loader=null,this.partLoadTimeout=-1,this.config=e}var t=e.prototype;return t.destroy=function(){this.loader&&(this.loader.destroy(),this.loader=null)},t.abort=function(){this.loader&&this.loader.abort()},t.load=function(e,t){var r=this,i=e.url;if(!i)return Promise.reject(new ya({type:b.NETWORK_ERROR,details:k.FRAG_LOAD_ERROR,fatal:!1,frag:e,error:new Error("Fragment does not have a "+(i?"part list":"url")),networkDetails:null}));this.abort();var a=this.config,n=a.fLoader,s=a.loader;return new Promise((function(o,l){if(r.loader&&r.loader.destroy(),e.gap){if(e.tagList.some((function(e){return"GAP"===e[0]})))return void l(pa(e));e.gap=!1}var u=r.loader=n?new n(a):new s(a),h=ma(e);e.loader=u;var f=mt(a.fragLoadPolicy.default),c={loadPolicy:f,timeout:f.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:"initSegment"===e.sn?1/0:va};e.stats=u.stats;var v={onSuccess:function(t,i,a,n){r.resetLoader(e,u);var s=t.data;a.resetIV&&e.decryptdata&&(e.decryptdata.iv=new Uint8Array(s.slice(0,16)),s=s.slice(16)),o({frag:e,part:null,payload:s,networkDetails:n})},onError:function(t,a,n,s){r.resetLoader(e,u),l(new ya({type:b.NETWORK_ERROR,details:k.FRAG_LOAD_ERROR,fatal:!1,frag:e,response:d({url:i,data:void 0},t),error:new Error("HTTP Error "+t.code+" "+t.text),networkDetails:n,stats:s}))},onAbort:function(t,i,a){r.resetLoader(e,u),l(new ya({type:b.NETWORK_ERROR,details:k.INTERNAL_ABORTED,fatal:!1,frag:e,error:new Error("Aborted"),networkDetails:a,stats:t}))},onTimeout:function(t,i,a){r.resetLoader(e,u),l(new ya({type:b.NETWORK_ERROR,details:k.FRAG_LOAD_TIMEOUT,fatal:!1,frag:e,error:new Error("Timeout after "+c.timeout+"ms"),networkDetails:a,stats:t}))}};t&&(v.onProgress=function(r,i,a,n){return t({frag:e,part:null,payload:a,networkDetails:n})}),u.load(h,c,v)}))},t.loadPart=function(e,t,r){var i=this;this.abort();var a=this.config,n=a.fLoader,s=a.loader;return new Promise((function(o,l){if(i.loader&&i.loader.destroy(),e.gap||t.gap)l(pa(e,t));else{var u=i.loader=n?new n(a):new s(a),h=ma(e,t);e.loader=u;var f=mt(a.fragLoadPolicy.default),c={loadPolicy:f,timeout:f.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:va};t.stats=u.stats,u.load(h,c,{onSuccess:function(a,n,s,l){i.resetLoader(e,u),i.updateStatsFromPart(e,t);var d={frag:e,part:t,payload:a.data,networkDetails:l};r(d),o(d)},onError:function(r,a,n,s){i.resetLoader(e,u),l(new ya({type:b.NETWORK_ERROR,details:k.FRAG_LOAD_ERROR,fatal:!1,frag:e,part:t,response:d({url:h.url,data:void 0},r),error:new Error("HTTP Error "+r.code+" "+r.text),networkDetails:n,stats:s}))},onAbort:function(r,a,n){e.stats.aborted=t.stats.aborted,i.resetLoader(e,u),l(new ya({type:b.NETWORK_ERROR,details:k.INTERNAL_ABORTED,fatal:!1,frag:e,part:t,error:new Error("Aborted"),networkDetails:n,stats:r}))},onTimeout:function(r,a,n){i.resetLoader(e,u),l(new ya({type:b.NETWORK_ERROR,details:k.FRAG_LOAD_TIMEOUT,fatal:!1,frag:e,part:t,error:new Error("Timeout after "+c.timeout+"ms"),networkDetails:n,stats:r}))}})}}))},t.updateStatsFromPart=function(e,t){var r=e.stats,i=t.stats,a=i.total;if(r.loaded+=i.loaded,a){var n=Math.round(e.duration/t.duration),s=Math.min(Math.round(r.loaded/a),n),o=(n-s)*Math.round(r.loaded/s);r.total=r.loaded+o}else r.total=Math.max(r.loaded,r.total);var l=r.loading,u=i.loading;l.start?l.first+=u.first-u.start:(l.start=u.start,l.first=u.first),l.end=u.end},t.resetLoader=function(e,t){e.loader=null,this.loader===t&&(self.clearTimeout(this.partLoadTimeout),this.loader=null),t.destroy()},e}();function ma(e,t){void 0===t&&(t=null);var r,i=t||e,a={frag:e,part:t,responseType:"arraybuffer",url:i.url,headers:{},rangeStart:0,rangeEnd:0},n=i.byteRangeStartOffset,s=i.byteRangeEndOffset;if(L(n)&&L(s)){var o,l=n,u=s;if("initSegment"===e.sn&&("AES-128"===(r=null==(o=e.decryptdata)?void 0:o.method)||"AES-256"===r)){var d=s-n;d%16&&(u=s+(16-d%16)),0!==n&&(a.resetIV=!0,l=n-16)}a.rangeStart=l,a.rangeEnd=u}return a}function pa(e,t){var r=new Error("GAP "+(e.gap?"tag":"attribute")+" found"),i={type:b.MEDIA_ERROR,details:k.FRAG_GAP,fatal:!1,frag:e,error:r,networkDetails:null};return t&&(i.part=t),(t||e).stats.aborted=!0,new ya(i)}var ya=function(e){function t(t){var r;return(r=e.call(this,t.error.message)||this).data=void 0,r.data=t,r}return o(t,e),t}(c(Error)),Ea=function(e){function t(t,r){var i;return(i=e.call(this,t,r)||this)._boundTick=void 0,i._tickTimer=null,i._tickInterval=null,i._tickCallCount=0,i._boundTick=i.tick.bind(i),i}o(t,e);var r=t.prototype;return r.destroy=function(){this.onHandlerDestroying(),this.onHandlerDestroyed()},r.onHandlerDestroying=function(){this.clearNextTick(),this.clearInterval()},r.onHandlerDestroyed=function(){},r.hasInterval=function(){return!!this._tickInterval},r.hasNextTick=function(){return!!this._tickTimer},r.setInterval=function(e){return!this._tickInterval&&(this._tickCallCount=0,this._tickInterval=self.setInterval(this._boundTick,e),!0)},r.clearInterval=function(){return!!this._tickInterval&&(self.clearInterval(this._tickInterval),this._tickInterval=null,!0)},r.clearNextTick=function(){return!!this._tickTimer&&(self.clearTimeout(this._tickTimer),this._tickTimer=null,!0)},r.tick=function(){this._tickCallCount++,1===this._tickCallCount&&(this.doTick(),this._tickCallCount>1&&this.tickImmediate(),this._tickCallCount=0)},r.tickImmediate=function(){this.clearNextTick(),this._tickTimer=self.setTimeout(this._boundTick,0)},r.doTick=function(){},t}(N),Ta=function(e,t,r,i,a,n){void 0===i&&(i=0),void 0===a&&(a=-1),void 0===n&&(n=!1),this.level=void 0,this.sn=void 0,this.part=void 0,this.id=void 0,this.size=void 0,this.partial=void 0,this.transmuxing={start:0,executeStart:0,executeEnd:0,end:0},this.buffering={audio:{start:0,executeStart:0,executeEnd:0,end:0},video:{start:0,executeStart:0,executeEnd:0,end:0},audiovideo:{start:0,executeStart:0,executeEnd:0,end:0}},this.level=e,this.sn=t,this.id=r,this.size=i,this.part=a,this.partial=n};function Sa(e,t){for(var r=0,i=e.length;r<i;r++){var a;if((null==(a=e[r])?void 0:a.cc)===t)return e[r]}return null}function La(e,t){var r=e.start+t;e.startPTS=r,e.setStart(r),e.endPTS=r+e.duration}function Ra(e,t){for(var r=t.fragments,i=0,a=r.length;i<a;i++)La(r[i],e);t.fragmentHint&&La(t.fragmentHint,e),t.alignedSliding=!0}function Aa(e,t){e&&(function(e,t){if(function(e,t){return!!(e&&t.startCC<e.endCC&&t.endCC>e.startCC)}(t,e)){var r=Math.min(t.endCC,e.endCC),i=Sa(t.fragments,r),a=Sa(e.fragments,r);i&&a&&(j.log("Aligning playlist at start of dicontinuity sequence "+r),Ra(i.start-a.start,e))}}(t,e),t.alignedSliding||function(e,t){if(e.hasProgramDateTime&&t.hasProgramDateTime){var r,i,a=e.fragments,n=t.fragments;if(a.length&&n.length){var s=Math.min(t.endCC,e.endCC);t.startCC<s&&e.startCC<s&&(r=Sa(n,s),i=Sa(a,s)),r&&i||(i=Sa(a,(r=n[Math.floor(n.length/2)]).cc)||a[Math.floor(a.length/2)]);var o=r.programDateTime,l=i.programDateTime;o&&l&&Ra((l-o)/1e3-(i.start-r.start),e)}}}(t,e),t.alignedSliding||t.skippedSegments||vr(e,t,!1))}var ba=function(e){for(var t="",r=e.length,i=0;i<r;i++)t+="["+e.start(i).toFixed(3)+"-"+e.end(i).toFixed(3)+"]";return t},ka={STOPPED:"STOPPED",IDLE:"IDLE",KEY_LOADING:"KEY_LOADING",FRAG_LOADING:"FRAG_LOADING",FRAG_LOADING_WAITING_RETRY:"FRAG_LOADING_WAITING_RETRY",PARSING:"PARSING",PARSED:"PARSED",ENDED:"ENDED",ERROR:"ERROR",WAITING_LEVEL:"WAITING_LEVEL"},Da=function(e){function t(t,r,i,a,n){var s;return(s=e.call(this,a,t.logger)||this).hls=void 0,s.fragPrevious=null,s.fragCurrent=null,s.fragmentTracker=void 0,s.transmuxer=null,s._state=ka.STOPPED,s.playlistType=void 0,s.media=null,s.mediaBuffer=null,s.config=void 0,s.bitrateTest=!1,s.lastCurrentTime=0,s.nextLoadPosition=0,s.startPosition=0,s.startTimeOffset=null,s.retryDate=0,s.levels=null,s.fragmentLoader=void 0,s.keyLoader=void 0,s.levelLastLoaded=null,s.startFragRequested=!1,s.decrypter=void 0,s.initPTS=[],s.buffering=!0,s.loadingParts=!1,s.loopSn=void 0,s.onMediaSeeking=function(){var e=s,t=e.config,r=e.fragCurrent,i=e.media,a=e.mediaBuffer,n=e.state,o=i?i.currentTime:0,l=_t.bufferInfo(a||i,o,t.maxBufferHole),u=!l.len;if(s.log("Media seeking to "+(L(o)?o.toFixed(3):o)+", state: "+n+", "+(u?"out of":"in")+" buffer"),s.state===ka.ENDED)s.resetLoadingState();else if(r){var d=t.maxFragLookUpTolerance,h=r.start-d,f=r.start+r.duration+d;if(u||f<l.start||h>l.end){var c=o>f;(o<h||c)&&(c&&r.loader&&(s.log("Cancelling fragment load for seek (sn: "+r.sn+")"),r.abortRequests(),s.resetLoadingState()),s.fragPrevious=null)}}if(i&&(s.fragmentTracker.removeFragmentsInRange(o,1/0,s.playlistType,!0),o>s.lastCurrentTime&&(s.lastCurrentTime=o),!s.loadingParts)){var v=Math.max(l.end,o),g=s.shouldLoadParts(s.getLevelDetails(),v);g&&(s.log("LL-Part loading ON after seeking to "+o.toFixed(2)+" with buffer @"+v.toFixed(2)),s.loadingParts=g)}s.hls.hasEnoughToStart||(s.log("Setting "+(u?"startPosition":"nextLoadPosition")+" to "+o+" for seek without enough to start"),s.nextLoadPosition=o,u&&(s.startPosition=o)),s.tickImmediate()},s.onMediaEnded=function(){s.log("setting startPosition to 0 because media ended"),s.startPosition=s.lastCurrentTime=0},s.playlistType=n,s.hls=t,s.fragmentLoader=new ga(t.config),s.keyLoader=i,s.fragmentTracker=r,s.config=t.config,s.decrypter=new ci(t.config),s}o(t,e);var r=t.prototype;return r.registerListeners=function(){var e=this.hls;e.on(D.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(D.ERROR,this.onError,this)},r.unregisterListeners=function(){var e=this.hls;e.off(D.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(D.ERROR,this.onError,this)},r.doTick=function(){this.onTickEnd()},r.onTickEnd=function(){},r.startLoad=function(e){},r.stopLoad=function(){if(this.state!==ka.STOPPED){this.fragmentLoader.abort(),this.keyLoader.abort(this.playlistType);var e=this.fragCurrent;null!=e&&e.loader&&(e.abortRequests(),this.fragmentTracker.removeFragment(e)),this.resetTransmuxer(),this.fragCurrent=null,this.fragPrevious=null,this.clearInterval(),this.clearNextTick(),this.state=ka.STOPPED}},r.pauseBuffering=function(){this.buffering=!1},r.resumeBuffering=function(){this.buffering=!0},r._streamEnded=function(e,t){if(t.live||!this.media)return!1;var r=e.end||0,i=this.config.timelineOffset||0;if(r<=i)return!1;var a=e.buffered;this.config.maxBufferHole&&a&&a.length>1&&(e=_t.bufferedInfo(a,e.start,0));var n=e.nextStart;if(n&&n>i&&n<t.edge)return!1;if(this.media.currentTime<e.start)return!1;var s=t.partList;if(null!=s&&s.length){var o=s[s.length-1];return _t.isBuffered(this.media,o.start+o.duration/2)}var l=t.fragments[t.fragments.length-1].type;return this.fragmentTracker.isEndListAppended(l)},r.getLevelDetails=function(){if(this.levels&&null!==this.levelLastLoaded)return this.levelLastLoaded.details},r.onMediaAttached=function(e,t){var r=this.media=this.mediaBuffer=t.media;r.removeEventListener("seeking",this.onMediaSeeking),r.removeEventListener("ended",this.onMediaEnded),r.addEventListener("seeking",this.onMediaSeeking),r.addEventListener("ended",this.onMediaEnded);var i=this.config;this.levels&&i.autoStartLoad&&this.state===ka.STOPPED&&this.startLoad(i.startPosition)},r.onMediaDetaching=function(e,t){var r=!!t.transferMedia,i=this.media;if(null!==i){if(i.ended&&(this.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0),i.removeEventListener("seeking",this.onMediaSeeking),i.removeEventListener("ended",this.onMediaEnded),this.keyLoader&&!r&&this.keyLoader.detach(),this.media=this.mediaBuffer=null,this.loopSn=void 0,r)return this.resetLoadingState(),void this.resetTransmuxer();this.loadingParts=!1,this.fragmentTracker.removeAllFragments(),this.stopLoad()}},r.onManifestLoading=function(){this.initPTS=[],this.levels=this.levelLastLoaded=this.fragCurrent=null,this.lastCurrentTime=this.startPosition=0,this.startFragRequested=!1},r.onError=function(e,t){},r.onManifestLoaded=function(e,t){this.startTimeOffset=t.startTimeOffset},r.onHandlerDestroying=function(){this.stopLoad(),this.transmuxer&&(this.transmuxer.destroy(),this.transmuxer=null),e.prototype.onHandlerDestroying.call(this),this.hls=this.onMediaSeeking=this.onMediaEnded=null},r.onHandlerDestroyed=function(){this.state=ka.STOPPED,this.fragmentLoader&&this.fragmentLoader.destroy(),this.keyLoader&&this.keyLoader.destroy(),this.decrypter&&this.decrypter.destroy(),this.hls=this.log=this.warn=this.decrypter=this.keyLoader=this.fragmentLoader=this.fragmentTracker=null,e.prototype.onHandlerDestroyed.call(this)},r.loadFragment=function(e,t,r){this.startFragRequested=!0,this._loadFragForPlayback(e,t,r)},r._loadFragForPlayback=function(e,t,r){var i=this;this._doFragLoad(e,t,r,(function(e){var t=e.frag;if(i.fragContextChanged(t))return i.warn(t.type+" sn: "+t.sn+(e.part?" part: "+e.part.index:"")+" of "+i.fragInfo(t,!1,e.part)+") was dropped during download."),void i.fragmentTracker.removeFragment(t);t.stats.chunkCount++,i._handleFragmentLoadProgress(e)})).then((function(e){if(e){var t=i.state,r=e.frag;i.fragContextChanged(r)?(t===ka.FRAG_LOADING||!i.fragCurrent&&t===ka.PARSING)&&(i.fragmentTracker.removeFragment(r),i.state=ka.IDLE):("payload"in e&&(i.log("Loaded "+r.type+" sn: "+r.sn+" of "+i.playlistLabel()+" "+r.level),i.hls.trigger(D.FRAG_LOADED,e)),i._handleFragmentLoadComplete(e))}})).catch((function(t){i.state!==ka.STOPPED&&i.state!==ka.ERROR&&(i.warn("Frag error: "+((null==t?void 0:t.message)||t)),i.resetFragmentLoading(e))}))},r.clearTrackerIfNeeded=function(e){var t,r=this.fragmentTracker;if(r.getState(e)===oa){var i=e.type,a=this.getFwdBufferInfo(this.mediaBuffer,i),n=Math.max(e.duration,a?a.len:this.config.maxBufferLength),s=this.backtrackFragment;(1==(s?e.sn-s.sn:0)||this.reduceMaxBufferLength(n,e.duration))&&r.removeFragment(e)}else 0===(null==(t=this.mediaBuffer)?void 0:t.buffered.length)?r.removeAllFragments():r.hasParts(e.type)&&(r.detectPartialFragments({frag:e,part:null,stats:e.stats,id:e.type}),r.getState(e)===la&&r.removeFragment(e))},r.checkLiveUpdate=function(e){if(e.updated&&!e.live){var t=e.fragments[e.fragments.length-1];this.fragmentTracker.detectPartialFragments({frag:t,part:null,stats:t.stats,id:t.type})}e.fragments[0]||(e.deltaUpdateFailed=!0)},r.waitForLive=function(e){var t=e.details;return(null==t?void 0:t.live)&&"EVENT"!==t.type&&(this.levelLastLoaded!==e||t.expired)},r.flushMainBuffer=function(e,t,r){if(void 0===r&&(r=null),e-t){var i={startOffset:e,endOffset:t,type:r};this.hls.trigger(D.BUFFER_FLUSHING,i)}},r._loadInitSegment=function(e,t){var r=this;this._doFragLoad(e,t).then((function(e){var t=null==e?void 0:e.frag;if(!t||r.fragContextChanged(t)||!r.levels)throw new Error("init load aborted");return e})).then((function(e){var t=r.hls,i=e.frag,a=e.payload,n=i.decryptdata;if(a&&a.byteLength>0&&null!=n&&n.key&&n.iv&&Wt(n.method)){var s=self.performance.now();return r.decrypter.decrypt(new Uint8Array(a),n.key.buffer,n.iv.buffer,Yt(n.method)).catch((function(e){throw t.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.FRAG_DECRYPT_ERROR,fatal:!1,error:e,reason:e.message,frag:i}),e})).then((function(a){var n=self.performance.now();return t.trigger(D.FRAG_DECRYPTED,{frag:i,payload:a,stats:{tstart:s,tdecrypt:n}}),e.payload=a,r.completeInitSegmentLoad(e)}))}return r.completeInitSegmentLoad(e)})).catch((function(t){r.state!==ka.STOPPED&&r.state!==ka.ERROR&&(r.warn(t),r.resetFragmentLoading(e))}))},r.completeInitSegmentLoad=function(e){if(!this.levels)throw new Error("init load aborted, missing levels");var t=e.frag.stats;this.state!==ka.STOPPED&&(this.state=ka.IDLE),e.frag.data=new Uint8Array(e.payload),t.parsing.start=t.buffering.start=self.performance.now(),t.parsing.end=t.buffering.end=self.performance.now(),this.tick()},r.unhandledEncryptionError=function(e,t){var r,i,a=e.tracks;if(a&&!t.encrypted&&(null!=(r=a.audio)&&r.encrypted||null!=(i=a.video)&&i.encrypted)&&(!this.config.emeEnabled||!this.keyLoader.emeController)){var n=this.media,s=new Error("Encrypted track with no key in "+this.fragInfo(t)+" (media "+(n?"attached mediaKeys: "+n.mediaKeys:"detached")+")");return this.warn(s.message),!(!n||n.mediaKeys)&&(this.hls.trigger(D.ERROR,{type:b.KEY_SYSTEM_ERROR,details:k.KEY_SYSTEM_NO_KEYS,fatal:!1,error:s,frag:t}),this.resetTransmuxer(),!0)}return!1},r.fragContextChanged=function(e){var t=this.fragCurrent;return!e||!t||e.sn!==t.sn||e.level!==t.level},r.fragBufferedComplete=function(e,t){var r=this.mediaBuffer?this.mediaBuffer:this.media;if(this.log("Buffered "+e.type+" sn: "+e.sn+(t?" part: "+t.index:"")+" of "+this.fragInfo(e,!1,t)+" > buffer:"+(r?ba(_t.getBuffered(r)):"(detached)")+")"),ae(e)){var i;if(e.type!==O){var a=e.elementaryStreams;if(!Object.keys(a).some((function(e){return!!a[e]})))return void(this.state=ka.IDLE)}var n=null==(i=this.levels)?void 0:i[e.level];null!=n&&n.fragmentError&&(this.log("Resetting level fragment error count of "+n.fragmentError+" on frag buffered"),n.fragmentError=0)}this.state=ka.IDLE},r._handleFragmentLoadComplete=function(e){var t=this.transmuxer;if(t){var r=e.frag,i=e.part,a=e.partsLoaded,n=!a||0===a.length||a.some((function(e){return!e})),s=new Ta(r.level,r.sn,r.stats.chunkCount+1,0,i?i.index:-1,!n);t.flush(s)}},r._handleFragmentLoadProgress=function(e){},r._doFragLoad=function(e,t,r,i){var a,n=this;void 0===r&&(r=null),this.fragCurrent=e;var s=t.details;if(!this.levels||!s)throw new Error("frag load aborted, missing level"+(s?"":" detail")+"s");var o=null;!e.encrypted||null!=(a=e.decryptdata)&&a.key?e.encrypted||(o=this.keyLoader.loadClear(e,s.encryptedFragments,this.startFragRequested))&&this.log("[eme] blocking frag load until media-keys acquired"):(this.log("Loading key for "+e.sn+" of ["+s.startSN+"-"+s.endSN+"], "+this.playlistLabel()+" "+e.level),this.state=ka.KEY_LOADING,this.fragCurrent=e,o=this.keyLoader.load(e).then((function(e){if(!n.fragContextChanged(e.frag))return n.hls.trigger(D.KEY_LOADED,e),n.state===ka.KEY_LOADING&&(n.state=ka.IDLE),e})),this.hls.trigger(D.KEY_LOADING,{frag:e}),null===this.fragCurrent&&(o=Promise.reject(new Error("frag load aborted, context changed in KEY_LOADING"))));var l,u=this.fragPrevious;if(ae(e)&&(!u||e.sn!==u.sn)){var d=this.shouldLoadParts(t.details,e.end);d!==this.loadingParts&&(this.log("LL-Part loading "+(d?"ON":"OFF")+" loading sn "+(null==u?void 0:u.sn)+"->"+e.sn),this.loadingParts=d)}if(r=Math.max(e.start,r||0),this.loadingParts&&ae(e)){var h=s.partList;if(h&&i){r>e.end&&s.fragmentHint&&(e=s.fragmentHint);var f=this.getNextPart(h,e,r);if(f>-1){var c,v=h[f];return e=this.fragCurrent=v.fragment,this.log("Loading "+e.type+" sn: "+e.sn+" part: "+v.index+" ("+f+"/"+(h.length-1)+") of "+this.fragInfo(e,!1,v)+") cc: "+e.cc+" ["+s.startSN+"-"+s.endSN+"], target: "+parseFloat(r.toFixed(3))),this.nextLoadPosition=v.start+v.duration,this.state=ka.FRAG_LOADING,c=o?o.then((function(r){return!r||n.fragContextChanged(r.frag)?null:n.doFragPartsLoad(e,v,t,i)})).catch((function(e){return n.handleFragLoadError(e)})):this.doFragPartsLoad(e,v,t,i).catch((function(e){return n.handleFragLoadError(e)})),this.hls.trigger(D.FRAG_LOADING,{frag:e,part:v,targetBufferTime:r}),null===this.fragCurrent?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING parts")):c}if(!e.url||this.loadedEndOfParts(h,r))return Promise.resolve(null)}}if(ae(e)&&this.loadingParts)this.log("LL-Part loading OFF after next part miss @"+r.toFixed(2)+" Check buffer at sn: "+e.sn+" loaded parts: "+(null==(l=s.partList)?void 0:l.filter((function(e){return e.loaded})).map((function(e){return"["+e.start+"-"+e.end+"]"})))),this.loadingParts=!1;else if(!e.url)return Promise.resolve(null);this.log("Loading "+e.type+" sn: "+e.sn+" of "+this.fragInfo(e,!1)+") cc: "+e.cc+" ["+s.startSN+"-"+s.endSN+"], target: "+parseFloat(r.toFixed(3))),L(e.sn)&&!this.bitrateTest&&(this.nextLoadPosition=e.start+e.duration),this.state=ka.FRAG_LOADING;var g,m=this.config.progressive;return g=m&&o?o.then((function(t){return!t||n.fragContextChanged(t.frag)?null:n.fragmentLoader.load(e,i)})).catch((function(e){return n.handleFragLoadError(e)})):Promise.all([this.fragmentLoader.load(e,m?i:void 0),o]).then((function(e){var t=e[0];return!m&&i&&i(t),t})).catch((function(e){return n.handleFragLoadError(e)})),this.hls.trigger(D.FRAG_LOADING,{frag:e,targetBufferTime:r}),null===this.fragCurrent?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING")):g},r.doFragPartsLoad=function(e,t,r,i){var a=this;return new Promise((function(n,s){var o,l=[],u=null==(o=r.details)?void 0:o.partList,d=function(t){a.fragmentLoader.loadPart(e,t,i).then((function(i){l[t.index]=i;var s=i.part;a.hls.trigger(D.FRAG_LOADED,i);var o=pr(r.details,e.sn,t.index+1)||yr(u,e.sn,t.index+1);if(!o)return n({frag:e,part:s,partsLoaded:l});d(o)})).catch(s)};d(t)}))},r.handleFragLoadError=function(e){if("data"in e){var t=e.data;t&&t.details===k.INTERNAL_ABORTED?this.handleFragLoadAborted(t.frag,t.part):this.hls.trigger(D.ERROR,t)}else this.hls.trigger(D.ERROR,{type:b.OTHER_ERROR,details:k.INTERNAL_EXCEPTION,err:e,error:e,fatal:!0});return null},r._handleTransmuxerFlush=function(e){var t=this.getCurrentContext(e);if(t&&this.state===ka.PARSING){var r=t.frag,i=t.part,a=t.level,n=self.performance.now();r.stats.parsing.end=n,i&&(i.stats.parsing.end=n);var s=this.getLevelDetails(),o=s&&r.sn>s.endSN||this.shouldLoadParts(s,r.end);o!==this.loadingParts&&(this.log("LL-Part loading "+(o?"ON":"OFF")+" after parsing segment ending @"+r.end.toFixed(2)),this.loadingParts=o),this.updateLevelTiming(r,i,a,e.partial)}else this.fragCurrent||this.state===ka.STOPPED||this.state===ka.ERROR||(this.state=ka.IDLE)},r.shouldLoadParts=function(e,t){if(this.config.lowLatencyMode){if(!e)return this.loadingParts;if(e.partList){var r,i,a=e.partList[0];if(t>=a.end+((null==(r=e.fragmentHint)?void 0:r.duration)||0)&&(this.hls.hasEnoughToStart?(null==(i=this.media)?void 0:i.currentTime)||this.lastCurrentTime:this.getLoadPosition())>a.start-a.fragment.duration)return!0}}return!1},r.getCurrentContext=function(e){var t=this.levels,r=this.fragCurrent,i=e.level,a=e.sn,n=e.part;if(null==t||!t[i])return this.warn("Levels object was unset while buffering fragment "+a+" of "+this.playlistLabel()+" "+i+". The current chunk will not be buffered."),null;var s=t[i],o=s.details,l=n>-1?pr(o,a,n):null,u=l?l.fragment:mr(o,a,r);return u?(r&&r!==u&&(u.stats=r.stats),{frag:u,part:l,level:s}):null},r.bufferFragmentData=function(e,t,r,i,a){if(this.state===ka.PARSING){var n=e.data1,s=e.data2,o=n;if(s&&(o=be(n,s)),o.length){var l=this.initPTS[t.cc],u=l?-l.baseTime/l.timescale:void 0,d={type:e.type,frag:t,part:r,chunkMeta:i,offset:u,parent:t.type,data:o};if(this.hls.trigger(D.BUFFER_APPENDING,d),e.dropped&&e.independent&&!r){if(a)return;this.flushBufferGap(t)}}}},r.flushBufferGap=function(e){var t=this.media;if(t)if(_t.isBuffered(t,t.currentTime)){var r=t.currentTime,i=_t.bufferInfo(t,r,0),a=e.duration,n=Math.min(2*this.config.maxFragLookUpTolerance,.25*a),s=Math.max(Math.min(e.start-n,i.end-n),r+n);e.start-s>n&&this.flushMainBuffer(s,e.start)}else this.flushMainBuffer(0,e.start)},r.getFwdBufferInfo=function(e,t){var r,i=this.getLoadPosition();if(!L(i))return null;var a=this.lastCurrentTime>i||null!=(r=this.media)&&r.paused?0:this.config.maxBufferHole;return this.getFwdBufferInfoAtPos(e,i,t,a)},r.getFwdBufferInfoAtPos=function(e,t,r,i){var a=_t.bufferInfo(e,t,i);if(0===a.len&&void 0!==a.nextStart){var n=this.fragmentTracker.getBufferedFrag(t,r);if(n&&(a.nextStart<=n.end||n.gap)){var s=Math.max(Math.min(a.nextStart,n.end)-t,i);return _t.bufferInfo(e,t,s)}}return a},r.getMaxBufferLength=function(e){var t,r=this.config;return t=e?Math.max(8*r.maxBufferSize/e,r.maxBufferLength):r.maxBufferLength,Math.min(t,r.maxMaxBufferLength)},r.reduceMaxBufferLength=function(e,t){var r=this.config,i=Math.max(Math.min(e-t,r.maxBufferLength),t),a=Math.max(e-3*t,r.maxMaxBufferLength/2,i);return a>=i&&(r.maxMaxBufferLength=a,this.warn("Reduce max buffer length to "+a+"s"),!0)},r.getAppendedFrag=function(e,t){void 0===t&&(t=x);var r=this.fragmentTracker?this.fragmentTracker.getAppendedFrag(e,t):null;return r&&"fragment"in r?r.fragment:r},r.getNextFragment=function(e,t){var r=t.fragments,i=r.length;if(!i)return null;var a=this.config,n=r[0].start,s=a.lowLatencyMode&&!!t.partList,o=null;if(t.live){var l=a.initialLiveManifestSize;if(i<l)return this.warn("Not enough fragments to start playback (have: "+i+", need: "+l+")"),null;if(!t.PTSKnown&&!this.startFragRequested&&-1===this.startPosition||e<n){var u;s&&!this.loadingParts&&(this.log("LL-Part loading ON for initial live fragment"),this.loadingParts=!0),o=this.getInitialLiveFragment(t);var d=this.hls.startPosition,h=this.hls.liveSyncPosition,f=o?(-1!==d&&d>=n?d:h)||o.start:e;this.log("Setting startPosition to "+f+" to match start frag at live edge. mainStart: "+d+" liveSyncPosition: "+h+" frag.start: "+(null==(u=o)?void 0:u.start)),this.startPosition=this.nextLoadPosition=f}}else e<=n&&(o=r[0]);if(!o){var c=this.loadingParts?t.partEnd:t.fragmentEnd;o=this.getFragmentAtPosition(e,c,t)}var v=this.filterReplacedPrimary(o,t);if(!v&&o){var g=o.sn-t.startSN;v=this.filterReplacedPrimary(r[g+1]||null,t)}return this.mapToInitFragWhenRequired(v)},r.isLoopLoading=function(e,t){var r=this.fragmentTracker.getState(e);return(r===ua||r===la&&!!e.gap)&&this.nextLoadPosition>t},r.getNextFragmentLoopLoading=function(e,t,r,i,a){var n=null;if(e.gap&&(n=this.getNextFragment(this.nextLoadPosition,t))&&!n.gap&&r.nextStart){var s=this.getFwdBufferInfoAtPos(this.mediaBuffer?this.mediaBuffer:this.media,r.nextStart,i,0);if(null!==s&&r.len+s.len>=a){var o=n.sn;return this.loopSn!==o&&(this.log('buffer full after gaps in "'+i+'" playlist starting at sn: '+o),this.loopSn=o),null}}return this.loopSn=void 0,n},r.filterReplacedPrimary=function(e,t){return e?(this.config,e):e},r.mapToInitFragWhenRequired=function(e){return null==e||!e.initSegment||e.initSegment.data||this.bitrateTest?e:e.initSegment},r.getNextPart=function(e,t,r){for(var i=-1,a=!1,n=!0,s=0,o=e.length;s<o;s++){var l=e[s];if(n=n&&!l.independent,i>-1&&r<l.start)break;var u=l.loaded;u?i=-1:(a||(l.independent||n)&&l.fragment===t)&&(l.fragment!==t&&this.warn("Need buffer at "+r+" but next unloaded part starts at "+l.start),i=s),a=u}return i},r.loadedEndOfParts=function(e,t){for(var r,i=e.length;i--;){if(!(r=e[i]).loaded)return!1;if(t>r.start)return!0}return!1},r.getInitialLiveFragment=function(e){var t=e.fragments,r=this.fragPrevious,i=null;if(r){if(e.hasProgramDateTime&&(this.log("Live playlist, switching playlist, load frag with same PDT: "+r.programDateTime),i=function(e,t,r){if(null===t||!Array.isArray(e)||!e.length||!L(t))return null;if(t<(e[0].programDateTime||0))return null;if(t>=(e[e.length-1].endProgramDateTime||0))return null;for(var i=0;i<e.length;++i){var a=e[i];if(ft(t,r,a))return a}return null}(t,r.endProgramDateTime,this.config.maxFragLookUpTolerance)),!i){var a=r.sn+1;if(a>=e.startSN&&a<=e.endSN){var n=t[a-e.startSN];r.cc===n.cc&&(i=n,this.log("Live playlist, switching playlist, load frag with next SN: "+i.sn))}i||(i=function(e,t,r){if(e&&e.startCC<=t&&e.endCC>=t){var i,a=e.fragments,n=e.fragmentHint;return n&&(a=a.concat(n)),ut(a,(function(e){return e.cc<t?1:e.cc>t?-1:(i=e,e.end<=r?1:e.start>r?-1:0)})),i||null}return null}(e,r.cc,r.end),i&&this.log("Live playlist, switching playlist, load frag with same CC: "+i.sn))}}else{var s=this.hls.liveSyncPosition;null!==s&&(i=this.getFragmentAtPosition(s,this.bitrateTest?e.fragmentEnd:e.edge,e))}return i},r.getFragmentAtPosition=function(e,t,r){var i,a,n=this.config,s=this.fragPrevious,o=r.fragments,l=r.endSN,u=r.fragmentHint,d=n.maxFragLookUpTolerance,h=r.partList,f=!!(this.loadingParts&&null!=h&&h.length&&u);if(f&&!this.bitrateTest&&h[h.length-1].fragment.sn===u.sn&&(o=o.concat(u),l=u.sn),i=e<t?dt(s,o,e,e<this.lastCurrentTime||e>t-d||null!=(a=this.media)&&a.paused||!this.startFragRequested?0:d):o[o.length-1]){var c=i.sn-r.startSN,v=this.fragmentTracker.getState(i);if((v===ua||v===la&&i.gap)&&(s=i),s&&i.sn===s.sn&&(!f||h[0].fragment.sn>i.sn||!r.live)&&i.level===s.level){var g=o[c+1];i=i.sn<l&&this.fragmentTracker.getState(g)!==ua?g:null}}return i},r.alignPlaylists=function(e,t,r){var i=e.fragments.length;if(!i)return this.warn("No fragments in live playlist"),0;var a=e.fragmentStart,n=!t,s=e.alignedSliding&&L(a);if(n||!s&&!a){Aa(r,e);var o=e.fragmentStart;return this.log("Live playlist sliding: "+o.toFixed(2)+" start-sn: "+(t?t.startSN:"na")+"->"+e.startSN+" fragments: "+i),o}return a},r.waitForCdnTuneIn=function(e){return e.live&&e.canBlockReload&&e.partTarget&&e.tuneInGoal>Math.max(e.partHoldBack,3*e.partTarget)},r.setStartPosition=function(e,t){var r=this.startPosition;r<t&&(r=-1);var i=this.timelineOffset;if(-1===r){var a=null!==this.startTimeOffset,n=a?this.startTimeOffset:e.startTimeOffset;null!==n&&L(n)?(r=t+n,n<0&&(r+=e.edge),r=Math.min(Math.max(t,r),t+e.totalduration),this.log("Setting startPosition to "+r+" for start time offset "+n+" found in "+(a?"multivariant":"media")+" playlist"),this.startPosition=r):e.live?(r=this.hls.liveSyncPosition||t,this.log("Setting startPosition to -1 to start at live edge "+r),this.startPosition=-1):(this.log("setting startPosition to 0 by default"),this.startPosition=r=0),this.lastCurrentTime=r+i}this.nextLoadPosition=r+i},r.getLoadPosition=function(){var e,t=this.media,r=0;return null!=(e=this.hls)&&e.hasEnoughToStart&&t?r=t.currentTime:this.nextLoadPosition>=0&&(r=this.nextLoadPosition),r},r.handleFragLoadAborted=function(e,t){this.transmuxer&&e.type===this.playlistType&&ae(e)&&e.stats.aborted&&(this.log("Fragment "+e.sn+(t?" part "+t.index:"")+" of "+this.playlistLabel()+" "+e.level+" was aborted"),this.resetFragmentLoading(e))},r.resetFragmentLoading=function(e){this.fragCurrent&&(this.fragContextChanged(e)||this.state===ka.FRAG_LOADING_WAITING_RETRY)||(this.state=ka.IDLE)},r.onFragmentOrKeyLoadError=function(e,t){if(t.chunkMeta&&!t.frag){var r=this.getCurrentContext(t.chunkMeta);r&&(t.frag=r.frag)}var i=t.frag;if(i&&i.type===e&&this.levels)if(this.fragContextChanged(i)){var a;this.warn("Frag load error must match current frag to retry "+i.url+" > "+(null==(a=this.fragCurrent)?void 0:a.url))}else{var n=t.details===k.FRAG_GAP;n&&this.fragmentTracker.fragBuffered(i,!0);var s=t.errorAction,o=s||{},l=o.action,u=o.flags,d=o.retryCount,h=void 0===d?0:d,f=o.retryConfig,c=!!s&&!!f,v=c&&l===St,g=c&&!s.resolved&&u===Rt;if(!v&&g&&ae(i)&&!i.endList)this.resetFragmentErrors(e),this.treatAsGap(i),s.resolved=!0;else if((v||g)&&h<f.maxNumRetry){this.resetStartWhenNotLoaded(this.levelLastLoaded);var m=gt(f,h);this.warn("Fragment "+i.sn+" of "+e+" "+i.level+" errored with "+t.details+", retrying loading "+(h+1)+"/"+f.maxNumRetry+" in "+m+"ms"),s.resolved=!0,this.retryDate=self.performance.now()+m,this.state=ka.FRAG_LOADING_WAITING_RETRY}else if(f&&s){if(this.resetFragmentErrors(e),!(h<f.maxNumRetry))return void this.warn(t.details+" reached or exceeded max retry ("+h+")");n||l===Tt||(s.resolved=!0)}else this.state=l===Et?ka.WAITING_LEVEL:ka.ERROR;this.tickImmediate()}},r.reduceLengthAndFlushBuffer=function(e){if(this.state===ka.PARSING||this.state===ka.PARSED){var t=e.frag,r=e.parent,i=this.getFwdBufferInfo(this.mediaBuffer,r),a=i&&i.len>.5;a&&this.reduceMaxBufferLength(i.len,(null==t?void 0:t.duration)||10);var n=!a;return n&&this.warn("Buffer full error while media.currentTime ("+this.getLoadPosition()+") is not buffered, flush "+r+" buffer"),t&&(this.fragmentTracker.removeFragment(t),this.nextLoadPosition=t.start),this.resetLoadingState(),n}return!1},r.resetFragmentErrors=function(e){e===w&&(this.fragCurrent=null),this.hls.hasEnoughToStart||(this.startFragRequested=!1),this.state!==ka.STOPPED&&(this.state=ka.IDLE)},r.afterBufferFlushed=function(e,t,r){if(e){var i=_t.getBuffered(e);this.fragmentTracker.detectEvictedFragments(t,i,r),this.state===ka.ENDED&&this.resetLoadingState()}},r.resetLoadingState=function(){this.log("Reset loading state"),this.fragCurrent=null,this.fragPrevious=null,this.state!==ka.STOPPED&&(this.state=ka.IDLE)},r.resetStartWhenNotLoaded=function(e){if(!this.hls.hasEnoughToStart){this.startFragRequested=!1;var t=e?e.details:null;null!=t&&t.live?(this.log("resetting startPosition for live start"),this.startPosition=-1,this.setStartPosition(t,t.fragmentStart),this.resetLoadingState()):this.nextLoadPosition=this.startPosition}},r.resetWhenMissingContext=function(e){this.warn("The loading context changed while buffering fragment "+e.sn+" of "+this.playlistLabel()+" "+e.level+". This chunk will not be buffered."),this.removeUnbufferedFrags(),this.resetStartWhenNotLoaded(this.levelLastLoaded),this.resetLoadingState()},r.removeUnbufferedFrags=function(e){void 0===e&&(e=0),this.fragmentTracker.removeFragmentsInRange(e,1/0,this.playlistType,!1,!0)},r.updateLevelTiming=function(e,t,r,i){var a=this,n=r.details;if(n){var s;if(!Object.keys(e.elementaryStreams).reduce((function(t,s){var o=e.elementaryStreams[s];if(o){var l=o.endPTS-o.startPTS;if(l<=0)return a.warn("Could not parse fragment "+e.sn+" "+s+" duration reliably ("+l+")"),t||!1;var u=i?0:hr(n,e,o.startPTS,o.endPTS,o.startDTS,o.endDTS,a);return a.hls.trigger(D.LEVEL_PTS_UPDATED,{details:n,level:r,drift:u,type:s,frag:e,start:o.startPTS,end:o.endPTS}),!0}return t}),!1)&&(0===r.fragmentError&&this.treatAsGap(e,r),null===(null==(s=this.transmuxer)?void 0:s.error))){var o=new Error("Found no media in fragment "+e.sn+" of "+this.playlistLabel()+" "+e.level+" resetting transmuxer to fallback to playlist timing");if(this.warn(o.message),this.hls.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.FRAG_PARSING_ERROR,fatal:!1,error:o,frag:e,reason:"Found no media in msn "+e.sn+" of "+this.playlistLabel()+' "'+r.url+'"'}),!this.hls)return;this.resetTransmuxer()}this.state=ka.PARSED,this.log("Parsed "+e.type+" sn: "+e.sn+(t?" part: "+t.index:"")+" of "+this.fragInfo(e,!1,t)+")"),this.hls.trigger(D.FRAG_PARSED,{frag:e,part:t})}else this.warn("level.details undefined")},r.playlistLabel=function(){return this.playlistType===x?"level":"track"},r.fragInfo=function(e,t,r){var i,a;return void 0===t&&(t=!0),this.playlistLabel()+" "+e.level+" ("+(r?"part":"frag")+":["+(null!=(i=t&&!r?e.startPTS:(r||e).start)?i:NaN).toFixed(3)+"-"+(null!=(a=t&&!r?e.endPTS:(r||e).end)?a:NaN).toFixed(3)+"]"+(r&&"main"===e.type?"INDEPENDENT="+(r.independent?"YES":"NO"):"")},r.treatAsGap=function(e,t){t&&t.fragmentError++,e.gap=!0,this.fragmentTracker.removeFragment(e),this.fragmentTracker.fragBuffered(e,!0)},r.resetTransmuxer=function(){var e;null==(e=this.transmuxer)||e.reset()},r.recoverWorkerError=function(e){"demuxerWorker"===e.event&&(this.fragmentTracker.removeAllFragments(),this.transmuxer&&(this.transmuxer.destroy(),this.transmuxer=null),this.resetStartWhenNotLoaded(this.levelLastLoaded),this.resetLoadingState())},i(t,[{key:"startPositionValue",get:function(){var e=this.nextLoadPosition,t=this.startPosition;return-1===t&&e?e:t}},{key:"bufferingEnabled",get:function(){return this.buffering}},{key:"inFlightFrag",get:function(){return{frag:this.fragCurrent,state:this.state}}},{key:"timelineOffset",get:function(){var e,t=this.config.timelineOffset;return t?(null==(e=this.getLevelDetails())?void 0:e.appliedTimelineOffset)||t:0}},{key:"primaryPrefetch",get:function(){return this.config,!1}},{key:"state",get:function(){return this._state},set:function(e){var t=this._state;t!==e&&(this._state=e,this.log(t+"->"+e))}}])}(Ea);function _a(e,t,r){Ia(e,t,r),e.addEventListener(t,r)}function Ia(e,t,r){e.removeEventListener(t,r)}var Ca=function(e){function t(t,r){var i;return(i=e.call(this,"gap-controller",t.logger)||this).hls=void 0,i.fragmentTracker=void 0,i.media=null,i.mediaSource=void 0,i.nudgeRetry=0,i.stallReported=!1,i.stalled=null,i.moved=!1,i.seeking=!1,i.buffered={},i.lastCurrentTime=0,i.ended=0,i.waiting=0,i.onMediaPlaying=function(){i.ended=0,i.waiting=0},i.onMediaWaiting=function(){var e;null!=(e=i.media)&&e.seeking||(i.waiting=self.performance.now(),i.tick())},i.onMediaEnded=function(){var e;i.hls&&(i.ended=(null==(e=i.media)?void 0:e.currentTime)||1,i.hls.trigger(D.MEDIA_ENDED,{stalled:!1}))},i.hls=t,i.fragmentTracker=r,i.registerListeners(),i}o(t,e);var r=t.prototype;return r.registerListeners=function(){var e=this.hls;e&&(e.on(D.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(D.BUFFER_APPENDED,this.onBufferAppended,this))},r.unregisterListeners=function(){var e=this.hls;e&&(e.off(D.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(D.BUFFER_APPENDED,this.onBufferAppended,this))},r.destroy=function(){e.prototype.destroy.call(this),this.unregisterListeners(),this.media=this.hls=this.fragmentTracker=null,this.mediaSource=void 0},r.onMediaAttached=function(e,t){this.setInterval(100),this.mediaSource=t.mediaSource;var r=this.media=t.media;_a(r,"playing",this.onMediaPlaying),_a(r,"waiting",this.onMediaWaiting),_a(r,"ended",this.onMediaEnded)},r.onMediaDetaching=function(e,t){this.clearInterval();var r=this.media;r&&(Ia(r,"playing",this.onMediaPlaying),Ia(r,"waiting",this.onMediaWaiting),Ia(r,"ended",this.onMediaEnded),this.media=null),this.mediaSource=void 0},r.onBufferAppended=function(e,t){this.buffered=t.timeRanges},r.tick=function(){var e;if(null!=(e=this.media)&&e.readyState&&this.hasBuffered){var t=this.media.currentTime;this.poll(t,this.lastCurrentTime),this.lastCurrentTime=t}},r.poll=function(e,t){var r,i,a=null==(r=this.hls)?void 0:r.config;if(a){var n=this.media;if(n){var s=n.seeking,o=this.seeking&&!s,l=!this.seeking&&s,u=n.paused&&!s||n.ended||0===n.playbackRate;if(this.seeking=s,e!==t)return t&&(this.ended=0),this.moved=!0,s||(this.nudgeRetry=0,a.nudgeOnVideoHole&&!u&&e>t&&this.nudgeOnVideoHole(e,t)),void(0===this.waiting&&this.stallResolved(e));if(l||o)o&&this.stallResolved(e);else{if(u)return this.nudgeRetry=0,this.stallResolved(e),void(!this.ended&&n.ended&&this.hls&&(this.ended=e||1,this.hls.trigger(D.MEDIA_ENDED,{stalled:!1})));if(_t.getBuffered(n).length){var d=_t.bufferInfo(n,e,0),h=d.nextStart||0,f=this.fragmentTracker;if(s&&f&&this.hls){var c=Pa(this.hls.inFlightFragments,e),v=d.len>2,g=!h||c||h-e>2&&!f.getPartialFragment(e);if(v||g)return;this.moved=!1}var m=null==(i=this.hls)?void 0:i.latestLevelDetails;if(!this.moved&&null!==this.stalled&&f){if(!(d.len>0||h))return;var p=Math.max(h,d.start||0)-e,y=null!=m&&m.live?2*m.targetduration:2,E=wa(e,f);if(p>0&&(p<=y||E))return void(n.paused||this._trySkipBufferHole(E))}var T=a.detectStallWithCurrentTimeMs,S=self.performance.now(),L=this.waiting,R=this.stalled;if(null===R){if(!(L>0&&S-L<T))return void(this.stalled=S);R=this.stalled=L}var A=S-R;if(!s&&(A>=T||L)&&this.hls){var b;if("ended"===(null==(b=this.mediaSource)?void 0:b.readyState)&&(null==m||!m.live)&&Math.abs(e-((null==m?void 0:m.edge)||0))<1){if(this.ended)return;return this.ended=e||1,void this.hls.trigger(D.MEDIA_ENDED,{stalled:!0})}if(this._reportStall(d),!this.media||!this.hls)return}var k=_t.bufferInfo(n,e,a.maxBufferHole);this._tryFixBufferStall(k,A,e)}else this.nudgeRetry=0}}}},r.stallResolved=function(e){var t=this.stalled;if(t&&this.hls&&(this.stalled=null,this.stallReported)){var r=self.performance.now()-t;this.log("playback not stuck anymore @"+e+", after "+Math.round(r)+"ms"),this.stallReported=!1,this.waiting=0,this.hls.trigger(D.STALL_RESOLVED,{})}},r.nudgeOnVideoHole=function(e,t){var r,i=this.buffered.video;if(this.hls&&this.media&&this.fragmentTracker&&null!=(r=this.buffered.audio)&&r.length&&i&&i.length>1&&e>i.end(0)){var a=_t.bufferedInfo(_t.timeRangesToArray(this.buffered.audio),e,0);if(a.len>1&&t>=a.start){var n=_t.timeRangesToArray(i),s=_t.bufferedInfo(n,t,0).bufferedIndex;if(s>-1&&s<n.length-1){var o=_t.bufferedInfo(n,e,0).bufferedIndex,l=n[s].end,u=n[s+1].start;if((-1===o||o>s)&&u-l<1&&e-l<2){var d=new Error("nudging playhead to flush pipeline after video hole. currentTime: "+e+" hole: "+l+" -> "+u+" buffered index: "+o);this.warn(d.message),this.media.currentTime+=1e-6;var h=wa(e,this.fragmentTracker);h&&"fragment"in h?h=h.fragment:h||(h=void 0);var f=_t.bufferInfo(this.media,e,0);this.hls.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.BUFFER_SEEK_OVER_HOLE,fatal:!1,error:d,reason:d.message,frag:h,buffer:f.len,bufferInfo:f})}}}}},r._tryFixBufferStall=function(e,t,r){var i,a,n=this.fragmentTracker,s=this.media,o=null==(i=this.hls)?void 0:i.config;if(s&&n&&o){var l=null==(a=this.hls)?void 0:a.latestLevelDetails,u=wa(r,n);if((u||null!=l&&l.live&&r<l.fragmentStart)&&(this._trySkipBufferHole(u)||!this.media))return;var d=e.buffered,h=this.adjacentTraversal(e,r);(d&&d.length>1&&e.len>o.maxBufferHole||e.nextStart&&(e.nextStart-r<o.maxBufferHole||h))&&(t>1e3*o.highBufferWatchdogPeriod||this.waiting)&&(this.warn("Trying to nudge playhead over buffer-hole"),this._tryNudgeBuffer(e))}},r.adjacentTraversal=function(e,t){var r=this.fragmentTracker,i=e.nextStart;if(r&&i){var a=r.getFragAtPos(t,x),n=r.getFragAtPos(i,x);if(a&&n)return n.sn-a.sn<2}return!1},r._reportStall=function(e){var t=this.hls,r=this.media,i=this.stallReported,a=this.stalled;if(!i&&null!==a&&r&&t){this.stallReported=!0;var n=new Error("Playback stalling at @"+r.currentTime+" due to low buffer ("+it(e)+")");this.warn(n.message),t.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.BUFFER_STALLED_ERROR,fatal:!1,error:n,buffer:e.len,bufferInfo:e,stalled:{start:a}})}},r._trySkipBufferHole=function(e){var t,r=this.fragmentTracker,i=this.media,a=null==(t=this.hls)?void 0:t.config;if(!i||!r||!a)return 0;var n=i.currentTime,s=_t.bufferInfo(i,n,0),o=n<s.start?s.start:s.nextStart;if(o&&this.hls){var l=s.len<=a.maxBufferHole,u=s.len>0&&s.len<1&&i.readyState<3,d=o-n;if(d>0&&(l||u)){if(d>a.maxBufferHole){var h=!1;if(0===n){var f=r.getAppendedFrag(0,x);f&&o<f.end&&(h=!0)}if(!h&&e){var c;if(null==(c=this.hls.loadLevelObj)||!c.details)return 0;if(Pa(this.hls.inFlightFragments,o))return 0;for(var v=!1,g=e.end;g<o;){var m=wa(g,r);if(!m){v=!0;break}g+=m.duration}if(v)return 0}}var p=Math.max(o+.05,n+.1);if(this.warn("skipping hole, adjusting currentTime from "+n+" to "+p),this.moved=!0,i.currentTime=p,null==e||!e.gap){var y=new Error("fragment loaded with buffer holes, seeking from "+n+" to "+p),E={type:b.MEDIA_ERROR,details:k.BUFFER_SEEK_OVER_HOLE,fatal:!1,error:y,reason:y.message,buffer:s.len,bufferInfo:s};e&&("fragment"in e?E.part=e:E.frag=e),this.hls.trigger(D.ERROR,E)}return p}}return 0},r._tryNudgeBuffer=function(e){var t=this.hls,r=this.media,i=this.nudgeRetry,a=null==t?void 0:t.config;if(!r||!a)return 0;var n=r.currentTime;if(this.nudgeRetry++,i<a.nudgeMaxRetry){var s=n+(i+1)*a.nudgeOffset,o=new Error("Nudging 'currentTime' from "+n+" to "+s);this.warn(o.message),r.currentTime=s,t.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.BUFFER_NUDGE_ON_STALL,error:o,fatal:!1,buffer:e.len,bufferInfo:e})}else{var l=new Error("Playhead still not moving while enough data buffered @"+n+" after "+a.nudgeMaxRetry+" nudges");this.error(l.message),t.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.BUFFER_STALLED_ERROR,error:l,fatal:!0,buffer:e.len,bufferInfo:e})}},i(t,[{key:"hasBuffered",get:function(){return Object.keys(this.buffered).length>0}}])}(Ea);function Pa(e,t){var r=xa(e.main);if(r&&r.start<=t)return r;var i=xa(e.audio);return i&&i.start<=t?i:null}function xa(e){if(!e)return null;switch(e.state){case ka.IDLE:case ka.STOPPED:case ka.ENDED:case ka.ERROR:return null}return e.frag}function wa(e,t){return t.getAppendedFrag(e,x)||t.getPartialFragment(e)}function Oa(e,t){var r;try{r=new Event("addtrack")}catch(e){(r=document.createEvent("Event")).initEvent("addtrack",!1,!1)}r.track=e,t.dispatchEvent(r)}function Fa(e,t,r,i){var a=e.mode;if("disabled"===a&&(e.mode="hidden"),e.cues&&e.cues.length>0)for(var n=function(e,t,r){var i=[],a=function(e,t){if(t<=e[0].startTime)return 0;var r=e.length-1;if(t>e[r].endTime)return-1;for(var i,a=0,n=r;a<=n;)if(t<e[i=Math.floor((n+a)/2)].startTime)n=i-1;else{if(!(t>e[i].startTime&&a<r))return i;a=i+1}return e[a].startTime-t<t-e[n].startTime?a:n}(e,t);if(a>-1)for(var n=a,s=e.length;n<s;n++){var o=e[n];if(o.startTime>=t&&o.endTime<=r)i.push(o);else if(o.startTime>r)return i}return i}(e.cues,t,r),s=0;s<n.length;s++)i&&!i(n[s])||e.removeCue(n[s]);"disabled"===a&&(e.mode=a)}function Ma(){if("undefined"!=typeof self)return self.VTTCue||self.TextTrackCue}function Na(e,t,r,i,a){var n=new e(t,r,"");try{n.value=i,a&&(n.type=a)}catch(s){n=new e(t,r,it(a?d({type:a},i):i))}return n}var Ba=function(){var e=Ma();try{e&&new e(0,Number.POSITIVE_INFINITY,"")}catch(e){return Number.MAX_VALUE}return Number.POSITIVE_INFINITY}(),Ua=function(){function e(e){var t=this;this.hls=void 0,this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.removeCues=!0,this.assetCue=void 0,this.onEventCueEnter=function(){t.hls&&t.hls.trigger(D.EVENT_CUE_ENTER,{})},this.hls=e,this._registerListeners()}var t=e.prototype;return t.destroy=function(){this._unregisterListeners(),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=this.onEventCueEnter=null},t._registerListeners=function(){var e=this.hls;e&&(e.on(D.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(D.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),e.on(D.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(D.LEVEL_UPDATED,this.onLevelUpdated,this),e.on(D.LEVEL_PTS_UPDATED,this.onLevelPtsUpdated,this))},t._unregisterListeners=function(){var e=this.hls;e&&(e.off(D.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(D.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),e.off(D.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(D.LEVEL_UPDATED,this.onLevelUpdated,this),e.off(D.LEVEL_PTS_UPDATED,this.onLevelPtsUpdated,this))},t.onMediaAttaching=function(e,t){var r;this.media=t.media,!1===(null==(r=t.overrides)?void 0:r.cueRemoval)&&(this.removeCues=!1)},t.onMediaAttached=function(){var e,t=null==(e=this.hls)?void 0:e.latestLevelDetails;t&&this.updateDateRangeCues(t)},t.onMediaDetaching=function(e,t){this.media=null,t.transferMedia||(this.id3Track&&(this.removeCues&&function(e,t){var r=e.mode;if("disabled"===r&&(e.mode="hidden"),e.cues)for(var i=e.cues.length;i--;)t&&e.cues[i].removeEventListener("enter",t),e.removeCue(e.cues[i]);"disabled"===r&&(e.mode=r)}(this.id3Track,this.onEventCueEnter),this.id3Track=null),this.dateRangeCuesAppended={})},t.onManifestLoading=function(){this.dateRangeCuesAppended={}},t.createTrack=function(e){var t=this.getID3Track(e.textTracks);return t.mode="hidden",t},t.getID3Track=function(e){if(this.media){for(var t=0;t<e.length;t++){var r=e[t];if("metadata"===r.kind&&"id3"===r.label)return Oa(r,this.media),r}return this.media.addTextTrack("metadata","id3")}},t.onFragParsingMetadata=function(e,t){if(this.media&&this.hls){var r=this.hls.config,i=r.enableEmsgMetadataCues,a=r.enableID3MetadataCues;if(i||a){var n=t.samples;this.id3Track||(this.id3Track=this.createTrack(this.media));var s=Ma();if(s)for(var o=0;o<n.length;o++){var l=n[o].type;if((l!==Xr.emsg||i)&&a){var u=Wr(n[o].data),d=n[o].pts,h=d+n[o].duration;h>Ba&&(h=Ba),h-d<=0&&(h=d+.25);for(var f=0;f<u.length;f++){var c=u[f];if(!Yr(c)){this.updateId3CueEnds(d,l);var v=Na(s,d,h,c,l);v&&this.id3Track.addCue(v)}}}}}}},t.updateId3CueEnds=function(e,t){var r,i=null==(r=this.id3Track)?void 0:r.cues;if(i)for(var a=i.length;a--;){var n=i[a];n.type===t&&n.startTime<e&&n.endTime===Ba&&(n.endTime=e)}},t.onBufferFlushing=function(e,t){var r=t.startOffset,i=t.endOffset,a=t.type,n=this.id3Track,s=this.hls;if(s){var o=s.config,l=o.enableEmsgMetadataCues,u=o.enableID3MetadataCues;n&&(l||u)&&Fa(n,r,i,"audio"===a?function(e){return e.type===Xr.audioId3&&u}:"video"===a?function(e){return e.type===Xr.emsg&&l}:function(e){return e.type===Xr.audioId3&&u||e.type===Xr.emsg&&l})}},t.onLevelUpdated=function(e,t){var r=t.details;this.updateDateRangeCues(r,!0)},t.onLevelPtsUpdated=function(e,t){Math.abs(t.drift)>.01&&this.updateDateRangeCues(t.details)},t.updateDateRangeCues=function(e,t){var r=this;if(this.hls&&this.media){var i=this.hls.config;i.assetPlayerId,i.timelineOffset;var a=i.enableDateRangeMetadataCues;if(i.interstitialsController,a){var n=Ma();if(e.hasProgramDateTime){var s,o=this.id3Track,l=e.dateRanges,u=Object.keys(l),d=this.dateRangeCuesAppended;if(o&&t)if(null!=(s=o.cues)&&s.length)for(var h=Object.keys(d).filter((function(e){return!u.includes(e)})),f=function(){var e,t=h[c],i=null==(e=d[t])?void 0:e.cues;delete d[t],i&&Object.keys(i).forEach((function(e){var t=i[e];if(t){t.removeEventListener("enter",r.onEventCueEnter);try{o.removeCue(t)}catch(e){}}}))},c=h.length;c--;)f();else d=this.dateRangeCuesAppended={};var v=e.fragments[e.fragments.length-1];if(0!==u.length&&L(null==v?void 0:v.programDateTime)){this.id3Track||(this.id3Track=this.createTrack(this.media));for(var g=function(){var e=u[m],t=l[e],i=t.startTime,a=d[e],s=(null==a?void 0:a.cues)||{},o=(null==a?void 0:a.durationKnown)||!1,h=Ba,f=t.duration;if(t.endDate&&null!==f)h=i+f,o=!0;else if(t.endOnNext&&!o){var c=u.reduce((function(e,r){if(r!==t.id){var i=l[r];if(i.class===t.class&&i.startDate>t.startDate&&(!e||t.startDate<e.startDate))return i}return e}),null);c&&(h=c.startTime,o=!0)}for(var v,g=Object.keys(t.attr),p=0;p<g.length;p++){var y=g[p];if("ID"!==(v=y)&&"CLASS"!==v&&"CUE"!==v&&"START-DATE"!==v&&"DURATION"!==v&&"END-DATE"!==v&&"END-ON-NEXT"!==v){var E=s[y];if(E)!o||null!=a&&a.durationKnown?Math.abs(E.startTime-i)>.01&&(E.startTime=i,E.endTime=h):E.endTime=h;else if(n){var T=t.attr[y];Ut(y)&&(T=Z(T));var S=Na(n,i,h,{key:y,data:T},Xr.dateRange);S&&(S.id=e,r.id3Track.addCue(S),s[y]=S)}}}d[e]={cues:s,dateRange:t,durationKnown:o}},m=0;m<u.length;m++)g()}}}}},e}(),Ga=function(){function e(e){var t=this;this.hls=void 0,this.config=void 0,this.media=null,this.currentTime=0,this.stallCount=0,this._latency=null,this._targetLatencyUpdated=!1,this.onTimeupdate=function(){var e=t.media,r=t.levelDetails;if(e&&r){t.currentTime=e.currentTime;var i=t.computeLatency();if(null!==i){t._latency=i;var a=t.config,n=a.lowLatencyMode,s=a.maxLiveSyncPlaybackRate;if(n&&1!==s&&r.live){var o=t.targetLatency;if(null!==o){var l=i-o;if(l<Math.min(t.maxLatency,o+r.targetduration)&&l>.05&&t.forwardBufferLength>1){var u=Math.min(2,Math.max(1,s)),d=Math.round(2/(1+Math.exp(-.75*l-t.edgeStalled))*20)/20,h=Math.min(u,Math.max(1,d));t.changeMediaPlaybackRate(e,h)}else 1!==e.playbackRate&&0!==e.playbackRate&&t.changeMediaPlaybackRate(e,1)}}}}},this.hls=e,this.config=e.config,this.registerListeners()}var t=e.prototype;return t.destroy=function(){this.unregisterListeners(),this.onMediaDetaching(),this.hls=null},t.registerListeners=function(){var e=this.hls;e&&(e.on(D.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.LEVEL_UPDATED,this.onLevelUpdated,this),e.on(D.ERROR,this.onError,this))},t.unregisterListeners=function(){var e=this.hls;e&&(e.off(D.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(D.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.LEVEL_UPDATED,this.onLevelUpdated,this),e.off(D.ERROR,this.onError,this))},t.onMediaAttached=function(e,t){this.media=t.media,this.media.addEventListener("timeupdate",this.onTimeupdate)},t.onMediaDetaching=function(){this.media&&(this.media.removeEventListener("timeupdate",this.onTimeupdate),this.media=null)},t.onManifestLoading=function(){this._latency=null,this.stallCount=0},t.onLevelUpdated=function(e,t){var r=t.details;r.advanced&&this.onTimeupdate(),!r.live&&this.media&&this.media.removeEventListener("timeupdate",this.onTimeupdate)},t.onError=function(e,t){var r;t.details===k.BUFFER_STALLED_ERROR&&(this.stallCount++,this.hls&&null!=(r=this.levelDetails)&&r.live&&this.hls.logger.warn("[latency-controller]: Stall detected, adjusting target latency"))},t.changeMediaPlaybackRate=function(e,t){var r,i;e.playbackRate!==t&&(null==(r=this.hls)||r.logger.debug("[latency-controller]: latency="+this.latency.toFixed(3)+", targetLatency="+(null==(i=this.targetLatency)?void 0:i.toFixed(3))+", forwardBufferLength="+this.forwardBufferLength.toFixed(3)+": adjusting playback rate from "+e.playbackRate+" to "+t),e.playbackRate=t)},t.estimateLiveEdge=function(){var e=this.levelDetails;return null===e?null:e.edge+e.age},t.computeLatency=function(){var e=this.estimateLiveEdge();return null===e?null:e-this.currentTime},i(e,[{key:"levelDetails",get:function(){var e;return(null==(e=this.hls)?void 0:e.latestLevelDetails)||null}},{key:"latency",get:function(){return this._latency||0}},{key:"maxLatency",get:function(){var e=this.config;if(void 0!==e.liveMaxLatencyDuration)return e.liveMaxLatencyDuration;var t=this.levelDetails;return t?e.liveMaxLatencyDurationCount*t.targetduration:0}},{key:"targetLatency",get:function(){var e=this.levelDetails;if(null===e||null===this.hls)return null;var t=e.holdBack,r=e.partHoldBack,i=e.targetduration,a=this.config,n=a.liveSyncDuration,s=a.liveSyncDurationCount,o=a.lowLatencyMode,l=this.hls.userConfig,u=o&&r||t;(this._targetLatencyUpdated||l.liveSyncDuration||l.liveSyncDurationCount||0===u)&&(u=void 0!==n?n:s*i);var d=i;return u+Math.min(this.stallCount*this.config.liveSyncOnStallIncrease,d)},set:function(e){this.stallCount=0,this.config.liveSyncDuration=e,this._targetLatencyUpdated=!0}},{key:"liveSyncPosition",get:function(){var e=this.estimateLiveEdge(),t=this.targetLatency;if(null===e||null===t)return null;var r=this.levelDetails;if(null===r)return null;var i=r.edge,a=e-t-this.edgeStalled,n=i-r.totalduration,s=i-(this.config.lowLatencyMode&&r.partTarget||r.targetduration);return Math.min(Math.max(n,a),s)}},{key:"drift",get:function(){var e=this.levelDetails;return null===e?1:e.drift}},{key:"edgeStalled",get:function(){var e=this.levelDetails;if(null===e)return 0;var t=3*(this.config.lowLatencyMode&&e.partTarget||e.targetduration);return Math.max(e.age-t,0)}},{key:"forwardBufferLength",get:function(){var e=this.media,t=this.levelDetails;if(!e||!t)return 0;var r=e.buffered.length;return(r?e.buffered.end(r-1):t.edge)-this.currentTime}}])}(),Va=function(e){function t(t,r){var i;return(i=e.call(this,r,t.logger)||this).hls=void 0,i.canLoad=!1,i.timer=-1,i.hls=t,i}o(t,e);var r=t.prototype;return r.destroy=function(){this.clearTimer(),this.hls=this.log=this.warn=null},r.clearTimer=function(){-1!==this.timer&&(self.clearTimeout(this.timer),this.timer=-1)},r.startLoad=function(){this.canLoad=!0,this.loadPlaylist()},r.stopLoad=function(){this.canLoad=!1,this.clearTimer()},r.switchParams=function(e,t,r){var i=null==t?void 0:t.renditionReports;if(i){for(var a=-1,n=0;n<i.length;n++){var s=i[n],o=void 0;try{o=new self.URL(s.URI,t.url).href}catch(e){this.warn("Could not construct new URL for Rendition Report: "+e),o=s.URI||""}if(o===e){a=n;break}o===e.substring(0,o.length)&&(a=n)}if(-1!==a){var l=i[a],u=parseInt(l["LAST-MSN"])||t.lastPartSn,d=parseInt(l["LAST-PART"])||t.lastPartIndex;if(this.hls.config.lowLatencyMode){var h=Math.min(t.age-t.partTarget,t.targetduration);d>=0&&h>t.partTarget&&(d+=1)}var f=r&&Ze(r);return new Je(u,d>=0?d:void 0,f)}}},r.loadPlaylist=function(e){this.clearTimer()},r.loadingPlaylist=function(e,t){this.clearTimer()},r.shouldLoadPlaylist=function(e){return this.canLoad&&!!e&&!!e.url&&(!e.details||e.details.live)},r.getUrlWithDirectives=function(e,t){if(t)try{return t.addDirectives(e)}catch(e){this.warn("Could not construct new URL with HLS Delivery Directives: "+e)}return e},r.playlistLoaded=function(e,t,r){var i=t.details,a=t.stats,n=self.performance.now(),s=a.loading.first?Math.max(0,n-a.loading.first):0;i.advancedDateTime=Date.now()-s;var o=this.hls.config.timelineOffset;if(o!==i.appliedTimelineOffset){var l=Math.max(o||0,0);i.appliedTimelineOffset=l,i.fragments.forEach((function(e){e.setStart(e.playlistOffset+l)}))}if(i.live||null!=r&&r.live){var u="levelInfo"in t?t.levelInfo:t.track;if(i.reloaded(r),r&&i.fragments.length>0){fr(r,i,this);var d=i.playlistParsingError;if(d){this.warn(d);var h=this.hls;if(!h.config.ignorePlaylistParsingErrors){var f,c=t.networkDetails;return void h.trigger(D.ERROR,{type:b.NETWORK_ERROR,details:k.LEVEL_PARSING_ERROR,fatal:!1,url:i.url,error:d,reason:d.message,level:t.level||void 0,parent:null==(f=i.fragments[0])?void 0:f.type,networkDetails:c,stats:a})}i.playlistParsingError=null}}-1===i.requestScheduled&&(i.requestScheduled=a.loading.start);var v,g=this.hls.mainForwardBufferInfo,m=g?g.end-g.len:0,p=gr(i,1e3*(i.edge-m));if(i.requestScheduled+p<n?i.requestScheduled=n:i.requestScheduled+=p,this.log("live playlist "+e+" "+(i.advanced?"REFRESHED "+i.lastPartSn+"-"+i.lastPartIndex:i.updated?"UPDATED":"MISSED")),!this.canLoad||!i.live)return;var y=void 0,E=void 0;if(i.canBlockReload&&i.endSN&&i.advanced){var T=this.hls.config.lowLatencyMode,S=i.lastPartSn,L=i.endSN,R=i.lastPartIndex,A=S===L;-1!==R?A?(y=L+1,E=T?0:R):(y=S,E=T?R+1:i.maxPartIndex):y=L+1;var _=i.age,I=_+i.ageHeader,C=Math.min(I-i.partTarget,1.5*i.targetduration);if(C>0){if(I>3*i.targetduration)this.log("Playlist last advanced "+_.toFixed(2)+"s ago. Omitting segment and part directives."),y=void 0,E=void 0;else if(null!=r&&r.tuneInGoal&&I-i.partTarget>r.tuneInGoal)this.warn("CDN Tune-in goal increased from: "+r.tuneInGoal+" to: "+C+" with playlist age: "+i.age),C=0;else{var P=Math.floor(C/i.targetduration);y+=P,void 0!==E&&(E+=Math.round(C%i.targetduration/i.partTarget)),this.log("CDN Tune-in age: "+i.ageHeader+"s last advanced "+_.toFixed(2)+"s goal: "+C+" skip sn "+P+" to part "+E)}i.tuneInGoal=C}if(v=this.getDeliveryDirectives(i,t.deliveryDirectives,y,E),T||!A)return i.requestScheduled=n,void this.loadingPlaylist(u,v)}else(i.canBlockReload||i.canSkipUntil)&&(v=this.getDeliveryDirectives(i,t.deliveryDirectives,y,E));v&&void 0!==y&&i.canBlockReload&&(i.requestScheduled=a.loading.first+Math.max(p-2*s,p/2)),this.scheduleLoading(u,v,i)}else this.clearTimer()},r.scheduleLoading=function(e,t,r){var i=this,a=r||e.details;if(a){var n=self.performance.now(),s=a.requestScheduled;if(n>=s)this.loadingPlaylist(e,t);else{var o=s-n;this.log("reload live playlist "+(e.name||e.bitrate+"bps")+" in "+Math.round(o)+" ms"),this.clearTimer(),this.timer=self.setTimeout((function(){return i.loadingPlaylist(e,t)}),o)}}else this.loadingPlaylist(e,t)},r.getDeliveryDirectives=function(e,t,r,i){var a=Ze(e);return null!=t&&t.skip&&e.deltaUpdateFailed&&(r=t.msn,i=t.part,a=ze),new Je(r,i,a)},r.checkRetry=function(e){var t=this,r=e.details,i=ct(e),a=e.errorAction,n=a||{},s=n.action,o=n.retryCount,l=void 0===o?0:o,u=n.retryConfig,d=!!a&&!!u&&(s===St||!a.resolved&&s===Et);if(d){var h;if(l>=u.maxNumRetry)return!1;if(i&&null!=(h=e.context)&&h.deliveryDirectives)this.warn("Retrying playlist loading "+(l+1)+"/"+u.maxNumRetry+' after "'+r+'" without delivery-directives'),this.loadPlaylist();else{var f=gt(u,l);this.clearTimer(),this.timer=self.setTimeout((function(){return t.loadPlaylist()}),f),this.warn("Retrying playlist loading "+(l+1)+"/"+u.maxNumRetry+' after "'+r+'" in '+f+"ms")}e.levelRetry=!0,a.resolved=!0}return d},t}(N),Ha=function(e){function t(t,r){var i;return(i=e.call(this,t,"level-controller")||this)._levels=[],i._firstLevel=-1,i._maxAutoLevel=-1,i._startLevel=void 0,i.currentLevel=null,i.currentLevelIndex=-1,i.manualLevelIndex=-1,i.steering=void 0,i.onParsedComplete=void 0,i.steering=r,i._registerListeners(),i}o(t,e);var r=t.prototype;return r._registerListeners=function(){var e=this.hls;e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(D.LEVEL_LOADED,this.onLevelLoaded,this),e.on(D.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(D.FRAG_BUFFERED,this.onFragBuffered,this),e.on(D.ERROR,this.onError,this)},r._unregisterListeners=function(){var e=this.hls;e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(D.LEVEL_LOADED,this.onLevelLoaded,this),e.off(D.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(D.FRAG_BUFFERED,this.onFragBuffered,this),e.off(D.ERROR,this.onError,this)},r.destroy=function(){this._unregisterListeners(),this.steering=null,this.resetLevels(),e.prototype.destroy.call(this)},r.stopLoad=function(){this._levels.forEach((function(e){e.loadError=0,e.fragmentError=0})),e.prototype.stopLoad.call(this)},r.resetLevels=function(){this._startLevel=void 0,this.manualLevelIndex=-1,this.currentLevelIndex=-1,this.currentLevel=null,this._levels=[],this._maxAutoLevel=-1},r.onManifestLoading=function(e,t){this.resetLevels()},r.onManifestLoaded=function(e,t){var r=this,i=this.hls.config.preferManagedMediaSource,a=[],n={},s={},o=!1,l=!1,u=!1;t.levels.forEach((function(e){var t=e.attrs,d=e.audioCodec,h=e.videoCodec;d&&(e.audioCodec=d=Ve(d,i)||void 0),h&&(h=e.videoCodec=function(e){for(var t=e.split(","),r=0;r<t.length;r++){var i=t[r].split(".");i.length>2&&"avc1"===i[0]&&(t[r]="avc1."+parseInt(i[1]).toString(16)+("000"+parseInt(i[2]).toString(16)).slice(-4))}return t.join(",")}(h));var f=e.width,c=e.height,v=e.unknownCodecs,g=v?v.length:0;if(v)for(var m=g;m--;){var p=v[m];r.isAudioSupported(p)?(e.audioCodec=d=d?d+","+p:p,g--,xe.audio[d.substring(0,4)]=2):r.isVideoSupported(p)&&(e.videoCodec=h=h?h+","+p:p,g--,xe.video[h.substring(0,4)]=2)}if(o||(o=!(!f||!c)),l||(l=!!h),u||(u=!!d),g||d&&!r.isAudioSupported(d)||h&&!r.isVideoSupported(h))r.log('Some or all CODECS not supported "'+t.CODECS+'"');else{var y=t.CODECS,E=t["FRAME-RATE"],T=t["HDCP-LEVEL"],S=t["PATHWAY-ID"],L=t.RESOLUTION,R=t["VIDEO-RANGE"],A=(S||".")+"-"+e.bitrate+"-"+L+"-"+E+"-"+y+"-"+R+"-"+T;if(n[A])if(n[A].uri===e.url||e.attrs["PATHWAY-ID"])n[A].addGroupId("audio",t.AUDIO),n[A].addGroupId("text",t.SUBTITLES);else{var b=s[A]+=1;e.attrs["PATHWAY-ID"]=new Array(b+1).join(".");var k=r.createLevel(e);n[A]=k,a.push(k)}else{var D=r.createLevel(e);n[A]=D,s[A]=1,a.push(D)}}})),this.filterAndSortMediaOptions(a,t,o,l,u)},r.createLevel=function(e){var t=new et(e),r=e.supplemental;if(null!=r&&r.videoCodec&&!this.isVideoSupported(r.videoCodec)){var i=new Error('SUPPLEMENTAL-CODECS not supported "'+r.videoCodec+'"');this.log(i.message),t.supportedResult=q.getUnsupportedResult(i,[])}return t},r.isAudioSupported=function(e){return Oe(e,"audio",this.hls.config.preferManagedMediaSource)},r.isVideoSupported=function(e){return Oe(e,"video",this.hls.config.preferManagedMediaSource)},r.filterAndSortMediaOptions=function(e,t,r,i,a){var n=this,s=[],o=[],l=e;if((r||i)&&a&&(l=l.filter((function(e){var t,r=e.videoCodec,i=e.videoRange,a=e.width,n=e.height;return(!!r||!(!a||!n))&&!!(t=i)&&Xe.indexOf(t)>-1}))),0!==l.length){t.audioTracks&&Ka(s=t.audioTracks.filter((function(e){return!e.audioCodec||n.isAudioSupported(e.audioCodec)}))),t.subtitles&&Ka(o=t.subtitles);var u=l.slice(0);l.sort((function(e,t){if(e.attrs["HDCP-LEVEL"]!==t.attrs["HDCP-LEVEL"])return(e.attrs["HDCP-LEVEL"]||"")>(t.attrs["HDCP-LEVEL"]||"")?1:-1;if(r&&e.height!==t.height)return e.height-t.height;if(e.frameRate!==t.frameRate)return e.frameRate-t.frameRate;if(e.videoRange!==t.videoRange)return Xe.indexOf(e.videoRange)-Xe.indexOf(t.videoRange);if(e.videoCodec!==t.videoCodec){var i=Ne(e.videoCodec),a=Ne(t.videoCodec);if(i!==a)return a-i}if(e.uri===t.uri&&e.codecSet!==t.codecSet){var n=Be(e.codecSet),s=Be(t.codecSet);if(n!==s)return s-n}return e.averageBitrate!==t.averageBitrate?e.averageBitrate-t.averageBitrate:0}));var d=u[0];if(this.steering&&(l=this.steering.filterParsedLevels(l)).length!==u.length)for(var h=0;h<u.length;h++)if(u[h].pathwayId===l[0].pathwayId){d=u[h];break}this._levels=l;for(var f=0;f<l.length;f++)if(l[f]===d){var c;this._firstLevel=f;var v=d.bitrate,g=this.hls.bandwidthEstimate;if(this.log("manifest loaded, "+l.length+" level(s) found, first bitrate: "+v),void 0===(null==(c=this.hls.userConfig)?void 0:c.abrEwmaDefaultEstimate)){var m=Math.min(v,this.hls.config.abrEwmaDefaultEstimateMax);m>g&&g===this.hls.abrEwmaDefaultEstimate&&(this.hls.bandwidthEstimate=m)}break}var p=a&&!i,y=this.hls.config,E=!(!y.audioStreamController||!y.audioTrackController),T={levels:l,audioTracks:s,subtitleTracks:o,sessionData:t.sessionData,sessionKeys:t.sessionKeys,firstLevel:this._firstLevel,stats:t.stats,audio:a,video:i,altAudio:E&&!p&&s.some((function(e){return!!e.url}))};this.hls.trigger(D.MANIFEST_PARSED,T)}else Promise.resolve().then((function(){if(n.hls){var e="no level with compatible codecs found in manifest",r=e;t.levels.length&&(r="one or more CODECS in variant not supported: "+it(t.levels.map((function(e){return e.attrs.CODECS})).filter((function(e,t,r){return r.indexOf(e)===t}))),n.warn(r),e+=" ("+r+")");var i=new Error(e);n.hls.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:t.url,error:i,reason:r})}}))},r.onError=function(e,t){!t.fatal&&t.context&&t.context.type===I&&t.context.level===this.level&&this.checkRetry(t)},r.onFragBuffered=function(e,t){var r=t.frag;if(void 0!==r&&r.type===x){var i=r.elementaryStreams;if(!Object.keys(i).some((function(e){return!!i[e]})))return;var a=this._levels[r.level];null!=a&&a.loadError&&(this.log("Resetting level error count of "+a.loadError+" on frag buffered"),a.loadError=0)}},r.onLevelLoaded=function(e,t){var r,i,a=t.level,n=t.details,s=t.levelInfo;if(!s)return this.warn("Invalid level index "+a),void(null!=(i=t.deliveryDirectives)&&i.skip&&(n.deltaUpdateFailed=!0));if(s===this.currentLevel||t.withoutMultiVariant){0===s.fragmentError&&(s.loadError=0);var o=s.details;o===t.details&&o.advanced&&(o=void 0),this.playlistLoaded(a,t,o)}else null!=(r=t.deliveryDirectives)&&r.skip&&(n.deltaUpdateFailed=!0)},r.loadPlaylist=function(t){e.prototype.loadPlaylist.call(this),this.shouldLoadPlaylist(this.currentLevel)&&this.scheduleLoading(this.currentLevel,t)},r.loadingPlaylist=function(t,r){e.prototype.loadingPlaylist.call(this,t,r);var i=this.getUrlWithDirectives(t.uri,r),a=this.currentLevelIndex,n=t.attrs["PATHWAY-ID"],s=t.details,o=null==s?void 0:s.age;this.log("Loading level index "+a+(void 0!==(null==r?void 0:r.msn)?" at sn "+r.msn+" part "+r.part:"")+(n?" Pathway "+n:"")+(o&&s.live?" age "+o.toFixed(1)+(s.type&&" "+s.type||""):"")+" "+i),this.hls.trigger(D.LEVEL_LOADING,{url:i,level:a,levelInfo:t,pathwayId:t.attrs["PATHWAY-ID"],id:0,deliveryDirectives:r||null})},r.removeLevel=function(e){var t,r=this;if(1!==this._levels.length){var i=this._levels.filter((function(t,i){return i!==e||(r.steering&&r.steering.removeLevel(t),t===r.currentLevel&&(r.currentLevel=null,r.currentLevelIndex=-1,t.details&&t.details.fragments.forEach((function(e){return e.level=-1}))),!1)}));Er(i),this._levels=i,this.currentLevelIndex>-1&&null!=(t=this.currentLevel)&&t.details&&(this.currentLevelIndex=this.currentLevel.details.fragments[0].level),this.manualLevelIndex>-1&&(this.manualLevelIndex=this.currentLevelIndex);var a=i.length-1;this._firstLevel=Math.min(this._firstLevel,a),this._startLevel&&(this._startLevel=Math.min(this._startLevel,a)),this.hls.trigger(D.LEVELS_UPDATED,{levels:i})}},r.onLevelsUpdated=function(e,t){var r=t.levels;this._levels=r},r.checkMaxAutoUpdated=function(){var e=this.hls,t=e.autoLevelCapping,r=e.maxAutoLevel,i=e.maxHdcpLevel;this._maxAutoLevel!==r&&(this._maxAutoLevel=r,this.hls.trigger(D.MAX_AUTO_LEVEL_UPDATED,{autoLevelCapping:t,levels:this.levels,maxAutoLevel:r,minAutoLevel:this.hls.minAutoLevel,maxHdcpLevel:i}))},i(t,[{key:"levels",get:function(){return 0===this._levels.length?null:this._levels}},{key:"loadLevelObj",get:function(){return this.currentLevel}},{key:"level",get:function(){return this.currentLevelIndex},set:function(e){var t=this._levels;if(0!==t.length){if(e<0||e>=t.length){var r=new Error("invalid level idx"),i=e<0;if(this.hls.trigger(D.ERROR,{type:b.OTHER_ERROR,details:k.LEVEL_SWITCH_ERROR,level:e,fatal:i,error:r,reason:r.message}),i)return;e=Math.min(e,t.length-1)}var a=this.currentLevelIndex,n=this.currentLevel,s=n?n.attrs["PATHWAY-ID"]:void 0,o=t[e],l=o.attrs["PATHWAY-ID"];if(this.currentLevelIndex=e,this.currentLevel=o,a!==e||!n||s!==l){this.log("Switching to level "+e+" ("+(o.height?o.height+"p ":"")+(o.videoRange?o.videoRange+" ":"")+(o.codecSet?o.codecSet+" ":"")+"@"+o.bitrate+")"+(l?" with Pathway "+l:"")+" from level "+a+(s?" with Pathway "+s:""));var u={level:e,attrs:o.attrs,details:o.details,bitrate:o.bitrate,averageBitrate:o.averageBitrate,maxBitrate:o.maxBitrate,realBitrate:o.realBitrate,width:o.width,height:o.height,codecSet:o.codecSet,audioCodec:o.audioCodec,videoCodec:o.videoCodec,audioGroups:o.audioGroups,subtitleGroups:o.subtitleGroups,loaded:o.loaded,loadError:o.loadError,fragmentError:o.fragmentError,name:o.name,id:o.id,uri:o.uri,url:o.url,urlId:0,audioGroupIds:o.audioGroupIds,textGroupIds:o.textGroupIds};this.hls.trigger(D.LEVEL_SWITCHING,u);var d=o.details;if(!d||d.live){var h=this.switchParams(o.uri,null==n?void 0:n.details,d);this.loadPlaylist(h)}}}}},{key:"manualLevel",get:function(){return this.manualLevelIndex},set:function(e){this.manualLevelIndex=e,void 0===this._startLevel&&(this._startLevel=e),-1!==e&&(this.level=e)}},{key:"firstLevel",get:function(){return this._firstLevel},set:function(e){this._firstLevel=e}},{key:"startLevel",get:function(){if(void 0===this._startLevel){var e=this.hls.config.startLevel;return void 0!==e?e:this.hls.firstAutoLevel}return this._startLevel},set:function(e){this._startLevel=e}},{key:"pathways",get:function(){return this.steering?this.steering.pathways():[]}},{key:"pathwayPriority",get:function(){return this.steering?this.steering.pathwayPriority:null},set:function(e){if(this.steering){var t=this.steering.pathways(),r=e.filter((function(e){return-1!==t.indexOf(e)}));if(e.length<1)return void this.warn("pathwayPriority "+e+" should contain at least one pathway from list: "+t);this.steering.pathwayPriority=r}}},{key:"nextLoadLevel",get:function(){return-1!==this.manualLevelIndex?this.manualLevelIndex:this.hls.nextAutoLevel},set:function(e){this.level=e,-1===this.manualLevelIndex&&(this.hls.nextAutoLevel=e)}}])}(Va);function Ka(e){var t={};e.forEach((function(e){var r=e.groupId||"";e.id=t[r]=t[r]||0,t[r]++}))}var Wa=[];function Ya(e,t,r){if(!((i=t.remuxResult).audio||i.video||i.text||i.id3||i.initSegment))return!1;var i,a=[],n=t.remuxResult,s=n.audio,o=n.video;return s&&ja(a,s),o&&ja(a,o),e.postMessage({event:"transmuxComplete",data:t,instanceNo:r},a),!0}function ja(e,t){t.data1&&e.push(t.data1.buffer),t.data2&&e.push(t.data2.buffer)}function qa(e,t,r,i){t.reduce((function(t,r){return Ya(e,r,i)||t}),!1)||e.postMessage({event:"transmuxComplete",data:t[0],instanceNo:i}),e.postMessage({event:"flush",data:r,instanceNo:i})}function Xa(e,t,r){self.postMessage({event:e,data:t,instanceNo:r})}void 0!==t&&t&&self.addEventListener("message",(function(e){var t=e.data,r=t.instanceNo;if(void 0!==r){var i=Wa[r];if("reset"===t.cmd&&(delete Wa[t.resetNo],i&&i.destroy(),t.cmd="init"),"init"===t.cmd){var a=JSON.parse(t.config),n=new E;n.on(D.FRAG_DECRYPTED,Xa),n.on(D.ERROR,Xa);var s=K(a.debug,t.id);return function(e,t){var r=function(r){e[r]=function(){var e=Array.prototype.join.call(arguments," ");Xa("workerLog",{logType:r,message:e},t)}};for(var i in e)r(i)}(s,r),Wa[r]=new Yi(n,t.typeSupported,a,"",t.id,s),void Xa("init",null,r)}if(i)switch(t.cmd){case"configure":i.configure(t.config);break;case"demux":var o=i.push(t.data,t.decryptdata,t.chunkMeta,t.state);qi(o)?o.then((function(e){Ya(self,e,r)})).catch((function(e){Xa(D.ERROR,{instanceNo:r,type:b.MEDIA_ERROR,details:k.FRAG_PARSING_ERROR,chunkMeta:t.chunkMeta,fatal:!1,error:e,err:e,reason:"transmuxer-worker push error"},r)})):Ya(self,o,r);break;case"flush":var l=t.chunkMeta,u=i.flush(l);qi(u)?u.then((function(e){qa(self,e,l,r)})).catch((function(e){Xa(D.ERROR,{type:b.MEDIA_ERROR,details:k.FRAG_PARSING_ERROR,chunkMeta:t.chunkMeta,fatal:!1,error:e,err:e,reason:"transmuxer-worker flush error"},r)})):qa(self,u,l,r)}}}));var za="1.6.9",Qa={},$a=0,Za=function(){function t(t,r,i,a){var n=this;this.error=null,this.hls=void 0,this.id=void 0,this.instanceNo=$a++,this.observer=void 0,this.frag=null,this.part=null,this.useWorker=void 0,this.workerContext=null,this.transmuxer=null,this.onTransmuxComplete=void 0,this.onFlush=void 0,this.onWorkerMessage=function(e){var t=e.data,r=n.hls;if(r&&null!=t&&t.event&&t.instanceNo===n.instanceNo)switch(t.event){case"init":var i,a=null==(i=n.workerContext)?void 0:i.objectURL;a&&self.URL.revokeObjectURL(a);break;case"transmuxComplete":n.handleTransmuxComplete(t.data);break;case"flush":n.onFlush(t.data);break;case"workerLog":r.logger[t.data.logType]&&r.logger[t.data.logType](t.data.message);break;default:t.data=t.data||{},t.data.frag=n.frag,t.data.part=n.part,t.data.id=n.id,r.trigger(t.event,t.data)}},this.onWorkerError=function(e){if(n.hls){var t=new Error(e.message+"  ("+e.filename+":"+e.lineno+")");n.hls.config.enableWorker=!1,n.hls.logger.warn('Error in "'+n.id+'" Web Worker, fallback to inline'),n.hls.trigger(D.ERROR,{type:b.OTHER_ERROR,details:k.INTERNAL_EXCEPTION,fatal:!1,event:"demuxerWorker",error:t})}};var s=t.config;this.hls=t,this.id=r,this.useWorker=!!s.enableWorker,this.onTransmuxComplete=i,this.onFlush=a;var o=function(e,t){(t=t||{}).frag=n.frag||void 0,e===D.ERROR&&(t.parent=n.id,t.part=n.part,n.error=t.error),n.hls.trigger(e,t)};this.observer=new E,this.observer.on(D.FRAG_DECRYPTED,o),this.observer.on(D.ERROR,o);var l=Ye(s.preferManagedMediaSource);if(this.useWorker&&"undefined"!=typeof Worker){var u=this.hls.logger;s.workerPath;try{s.workerPath?(u.log("loading Web Worker "+s.workerPath+' for "'+r+'"'),this.workerContext=function(e){var t=Qa[e];if(t)return t.clientCount++,t;var r=new self.URL(e,self.location.href).href,i={worker:new self.Worker(r),scriptURL:r,clientCount:1};return Qa[e]=i,i}(s.workerPath)):(u.log('injecting Web Worker for "'+r+'"'),this.workerContext=function(){var t=Qa[za];if(t)return t.clientCount++,t;var r=new self.Blob(["var exports={};var module={exports:exports};function define(f){f()};define.amd=true;("+e.toString()+")(true);"],{type:"text/javascript"}),i=self.URL.createObjectURL(r),a={worker:new self.Worker(i),objectURL:i,clientCount:1};return Qa[za]=a,a}());var d=this.workerContext.worker;d.addEventListener("message",this.onWorkerMessage),d.addEventListener("error",this.onWorkerError),d.postMessage({instanceNo:this.instanceNo,cmd:"init",typeSupported:l,id:r,config:it(s)})}catch(e){u.warn('Error setting up "'+r+'" Web Worker, fallback to inline',e),this.terminateWorker(),this.error=null,this.transmuxer=new Yi(this.observer,l,s,"",r,t.logger)}}else this.transmuxer=new Yi(this.observer,l,s,"",r,t.logger)}var r=t.prototype;return r.reset=function(){if(this.frag=null,this.part=null,this.workerContext){var e=this.instanceNo;this.instanceNo=$a++;var t=this.hls.config,r=Ye(t.preferManagedMediaSource);this.workerContext.worker.postMessage({instanceNo:this.instanceNo,cmd:"reset",resetNo:e,typeSupported:r,id:this.id,config:it(t)})}},r.terminateWorker=function(){if(this.workerContext){var e=this.workerContext.worker;this.workerContext=null,e.removeEventListener("message",this.onWorkerMessage),e.removeEventListener("error",this.onWorkerError),function(e){var t=Qa[e||za];if(t&&1==t.clientCount--){var r=t.worker,i=t.objectURL;delete Qa[e||za],i&&self.URL.revokeObjectURL(i),r.terminate()}}(this.hls.config.workerPath)}},r.destroy=function(){if(this.workerContext)this.terminateWorker(),this.onWorkerMessage=this.onWorkerError=null;else{var e=this.transmuxer;e&&(e.destroy(),this.transmuxer=null)}var t=this.observer;t&&t.removeAllListeners(),this.frag=null,this.part=null,this.observer=null,this.hls=null},r.push=function(e,t,r,i,a,n,s,o,l,u){var d,h,f=this;l.transmuxing.start=self.performance.now();var c=this.instanceNo,v=this.transmuxer,g=n?n.start:a.start,m=a.decryptdata,p=this.frag,y=!(p&&a.cc===p.cc),E=!(p&&l.level===p.level),T=p?l.sn-p.sn:-1,S=this.part?l.part-this.part.index:-1,L=0===T&&l.id>1&&l.id===(null==p?void 0:p.stats.chunkCount),R=!E&&(1===T||0===T&&(1===S||L&&S<=0)),A=self.performance.now();(E||T||0===a.stats.parsing.start)&&(a.stats.parsing.start=A),!n||!S&&R||(n.stats.parsing.start=A);var b=!(p&&(null==(d=a.initSegment)?void 0:d.url)===(null==(h=p.initSegment)?void 0:h.url)),k=new zi(y,R,o,E,g,b);if(!R||y||b){this.hls.logger.log("[transmuxer-interface]: Starting new transmux session for "+a.type+" sn: "+l.sn+(l.part>-1?" part: "+l.part:"")+" "+(this.id===x?"level":"track")+": "+l.level+" id: "+l.id+"\n        discontinuity: "+y+"\n        trackSwitch: "+E+"\n        contiguous: "+R+"\n        accurateTimeOffset: "+o+"\n        timeOffset: "+g+"\n        initSegmentChange: "+b);var D=new Xi(r,i,t,s,u);this.configureTransmuxer(D)}if(this.frag=a,this.part=n,this.workerContext)this.workerContext.worker.postMessage({instanceNo:c,cmd:"demux",data:e,decryptdata:m,chunkMeta:l,state:k},e instanceof ArrayBuffer?[e]:[]);else if(v){var _=v.push(e,m,l,k);qi(_)?_.then((function(e){f.handleTransmuxComplete(e)})).catch((function(e){f.transmuxerError(e,l,"transmuxer-interface push error")})):this.handleTransmuxComplete(_)}},r.flush=function(e){var t=this;e.transmuxing.start=self.performance.now();var r=this.instanceNo,i=this.transmuxer;if(this.workerContext)this.workerContext.worker.postMessage({instanceNo:r,cmd:"flush",chunkMeta:e});else if(i){var a=i.flush(e);qi(a)?a.then((function(r){t.handleFlushResult(r,e)})).catch((function(r){t.transmuxerError(r,e,"transmuxer-interface flush error")})):this.handleFlushResult(a,e)}},r.transmuxerError=function(e,t,r){this.hls&&(this.error=e,this.hls.trigger(D.ERROR,{type:b.MEDIA_ERROR,details:k.FRAG_PARSING_ERROR,chunkMeta:t,frag:this.frag||void 0,part:this.part||void 0,fatal:!1,error:e,err:e,reason:r}))},r.handleFlushResult=function(e,t){var r=this;e.forEach((function(e){r.handleTransmuxComplete(e)})),this.onFlush(t)},r.configureTransmuxer=function(e){var t=this.instanceNo,r=this.transmuxer;this.workerContext?this.workerContext.worker.postMessage({instanceNo:t,cmd:"configure",config:e}):r&&r.configure(e)},r.handleTransmuxComplete=function(e){e.chunkMeta.transmuxing.end=self.performance.now(),this.onTransmuxComplete(e)},t}();function Ja(){return self.SourceBuffer||self.WebKitSourceBuffer}function en(){if(!z())return!1;var e=Ja();return!e||e.prototype&&"function"==typeof e.prototype.appendBuffer&&"function"==typeof e.prototype.remove}var tn=function(e){function t(t,r,i){var a;return(a=e.call(this,t,r,i,"stream-controller",x)||this).audioCodecSwap=!1,a.level=-1,a._forceStartLoad=!1,a._hasEnoughToStart=!1,a.altAudio=0,a.audioOnly=!1,a.fragPlaying=null,a.fragLastKbps=0,a.couldBacktrack=!1,a.backtrackFragment=null,a.audioCodecSwitch=!1,a.videoBuffer=null,a.onMediaPlaying=function(){a.tick()},a.onMediaSeeked=function(){var e=a.media,t=e?e.currentTime:null;if(null!==t&&L(t)&&(a.log("Media seeked to "+t.toFixed(3)),a.getBufferedFrag(t))){var r=a.getFwdBufferInfoAtPos(e,t,x,0);null!==r&&0!==r.len?a.tick():a.warn("Main forward buffer length at "+t+' on "seeked" event '+(r?r.len:"empty")+")")}},a.registerListeners(),a}o(t,e);var r=t.prototype;return r.registerListeners=function(){e.prototype.registerListeners.call(this);var t=this.hls;t.on(D.MANIFEST_PARSED,this.onManifestParsed,this),t.on(D.LEVEL_LOADING,this.onLevelLoading,this),t.on(D.LEVEL_LOADED,this.onLevelLoaded,this),t.on(D.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),t.on(D.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.on(D.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.on(D.BUFFER_CREATED,this.onBufferCreated,this),t.on(D.BUFFER_FLUSHED,this.onBufferFlushed,this),t.on(D.LEVELS_UPDATED,this.onLevelsUpdated,this),t.on(D.FRAG_BUFFERED,this.onFragBuffered,this)},r.unregisterListeners=function(){e.prototype.unregisterListeners.call(this);var t=this.hls;t.off(D.MANIFEST_PARSED,this.onManifestParsed,this),t.off(D.LEVEL_LOADED,this.onLevelLoaded,this),t.off(D.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),t.off(D.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.off(D.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.off(D.BUFFER_CREATED,this.onBufferCreated,this),t.off(D.BUFFER_FLUSHED,this.onBufferFlushed,this),t.off(D.LEVELS_UPDATED,this.onLevelsUpdated,this),t.off(D.FRAG_BUFFERED,this.onFragBuffered,this)},r.onHandlerDestroying=function(){this.onMediaPlaying=this.onMediaSeeked=null,this.unregisterListeners(),e.prototype.onHandlerDestroying.call(this)},r.startLoad=function(e,t){if(this.levels){var r=this.lastCurrentTime,i=this.hls;if(this.stopLoad(),this.setInterval(100),this.level=-1,!this.startFragRequested){var a=i.startLevel;-1===a&&(i.config.testBandwidth&&this.levels.length>1?(a=0,this.bitrateTest=!0):a=i.firstAutoLevel),i.nextLoadLevel=a,this.level=i.loadLevel,this._hasEnoughToStart=!!t}r>0&&-1===e&&!t&&(this.log("Override startPosition with lastCurrentTime @"+r.toFixed(3)),e=r),this.state=ka.IDLE,this.nextLoadPosition=this.lastCurrentTime=e+this.timelineOffset,this.startPosition=t?-1:e,this.tick()}else this._forceStartLoad=!0,this.state=ka.STOPPED},r.stopLoad=function(){this._forceStartLoad=!1,e.prototype.stopLoad.call(this)},r.doTick=function(){switch(this.state){case ka.WAITING_LEVEL:var e=this.levels,t=this.level,r=null==e?void 0:e[t],i=null==r?void 0:r.details;if(i&&(!i.live||this.levelLastLoaded===r&&!this.waitForLive(r))){if(this.waitForCdnTuneIn(i))break;this.state=ka.IDLE;break}if(this.hls.nextLoadLevel!==this.level){this.state=ka.IDLE;break}break;case ka.FRAG_LOADING_WAITING_RETRY:var a,n=self.performance.now(),s=this.retryDate;if(!s||n>=s||null!=(a=this.media)&&a.seeking){var o=this.levels,l=this.level,u=null==o?void 0:o[l];this.resetStartWhenNotLoaded(u||null),this.state=ka.IDLE}}this.state===ka.IDLE&&this.doTickIdle(),this.onTickEnd()},r.onTickEnd=function(){var t;e.prototype.onTickEnd.call(this),null!=(t=this.media)&&t.readyState&&!1===this.media.seeking&&(this.lastCurrentTime=this.media.currentTime),this.checkFragmentChanged()},r.doTickIdle=function(){var e=this.hls,t=this.levelLastLoaded,r=this.levels,i=this.media;if(null!==t&&(i||this.primaryPrefetch||!this.startFragRequested&&e.config.startFragPrefetch)&&(!this.altAudio||!this.audioOnly)){var a=this.buffering?e.nextLoadLevel:e.loadLevel;if(null!=r&&r[a]){var n=r[a],s=this.getMainFwdBufferInfo();if(null!==s){var o=this.getLevelDetails();if(o&&this._streamEnded(s,o)){var l={};return 2===this.altAudio&&(l.type="video"),this.hls.trigger(D.BUFFER_EOS,l),void(this.state=ka.ENDED)}if(this.buffering){e.loadLevel!==a&&-1===e.manualLevel&&this.log("Adapting to level "+a+" from level "+this.level),this.level=e.nextLoadLevel=a;var u=n.details;if(!u||this.state===ka.WAITING_LEVEL||this.waitForLive(n))return this.level=a,this.state=ka.WAITING_LEVEL,void(this.startFragRequested=!1);var d=s.len,h=this.getMaxBufferLength(n.maxBitrate);if(!(d>=h)){this.backtrackFragment&&this.backtrackFragment.start>s.end&&(this.backtrackFragment=null);var f=this.backtrackFragment?this.backtrackFragment.start:s.end,c=this.getNextFragment(f,u);if(this.couldBacktrack&&!this.fragPrevious&&c&&ae(c)&&this.fragmentTracker.getState(c)!==ua){var v,g=(null!=(v=this.backtrackFragment)?v:c).sn-u.startSN,m=u.fragments[g-1];m&&c.cc===m.cc&&(c=m,this.fragmentTracker.removeFragment(m))}else this.backtrackFragment&&s.len&&(this.backtrackFragment=null);if(c&&this.isLoopLoading(c,f)){if(!c.gap){var p=this.audioOnly&&!this.altAudio?ee:te,y=(p===te?this.videoBuffer:this.mediaBuffer)||this.media;y&&this.afterBufferFlushed(y,p,x)}c=this.getNextFragmentLoopLoading(c,u,s,x,h)}c&&(!c.initSegment||c.initSegment.data||this.bitrateTest||(c=c.initSegment),this.loadFragment(c,n,f))}}}}}},r.loadFragment=function(t,r,i){var a=this.fragmentTracker.getState(t);a===sa||a===la?ae(t)?this.bitrateTest?(this.log("Fragment "+t.sn+" of level "+t.level+" is being downloaded to test bitrate and will not be buffered"),this._loadBitrateTestFrag(t,r)):e.prototype.loadFragment.call(this,t,r,i):this._loadInitSegment(t,r):this.clearTrackerIfNeeded(t)},r.getBufferedFrag=function(e){return this.fragmentTracker.getBufferedFrag(e,x)},r.followingBufferedFrag=function(e){return e?this.getBufferedFrag(e.end+.5):null},r.immediateLevelSwitch=function(){this.abortCurrentFrag(),this.flushMainBuffer(0,Number.POSITIVE_INFINITY)},r.nextLevelSwitch=function(){var e=this.levels,t=this.media;if(null!=t&&t.readyState){var r,i=this.getAppendedFrag(t.currentTime);i&&i.start>1&&this.flushMainBuffer(0,i.start-1);var a=this.getLevelDetails();if(null!=a&&a.live){var n=this.getMainFwdBufferInfo();if(!n||n.len<2*a.targetduration)return}if(!t.paused&&e){var s=e[this.hls.nextLoadLevel],o=this.fragLastKbps;r=o&&this.fragCurrent?this.fragCurrent.duration*s.maxBitrate/(1e3*o)+1:0}else r=0;var l=this.getBufferedFrag(t.currentTime+r);if(l){var u=this.followingBufferedFrag(l);if(u){this.abortCurrentFrag();var d=u.maxStartPTS?u.maxStartPTS:u.start,h=u.duration,f=Math.max(l.end,d+Math.min(Math.max(h-this.config.maxFragLookUpTolerance,h*(this.couldBacktrack?.5:.125)),h*(this.couldBacktrack?.75:.25)));this.flushMainBuffer(f,Number.POSITIVE_INFINITY)}}}},r.abortCurrentFrag=function(){var e=this.fragCurrent;switch(this.fragCurrent=null,this.backtrackFragment=null,e&&(e.abortRequests(),this.fragmentTracker.removeFragment(e)),this.state){case ka.KEY_LOADING:case ka.FRAG_LOADING:case ka.FRAG_LOADING_WAITING_RETRY:case ka.PARSING:case ka.PARSED:this.state=ka.IDLE}this.nextLoadPosition=this.getLoadPosition()},r.flushMainBuffer=function(t,r){e.prototype.flushMainBuffer.call(this,t,r,2===this.altAudio?"video":null)},r.onMediaAttached=function(t,r){e.prototype.onMediaAttached.call(this,t,r);var i=r.media;_a(i,"playing",this.onMediaPlaying),_a(i,"seeked",this.onMediaSeeked)},r.onMediaDetaching=function(t,r){var i=this.media;i&&(Ia(i,"playing",this.onMediaPlaying),Ia(i,"seeked",this.onMediaSeeked)),this.videoBuffer=null,this.fragPlaying=null,e.prototype.onMediaDetaching.call(this,t,r),r.transferMedia||(this._hasEnoughToStart=!1)},r.onManifestLoading=function(){e.prototype.onManifestLoading.call(this),this.log("Trigger BUFFER_RESET"),this.hls.trigger(D.BUFFER_RESET,void 0),this.couldBacktrack=!1,this.fragLastKbps=0,this.fragPlaying=this.backtrackFragment=null,this.altAudio=0,this.audioOnly=!1},r.onManifestParsed=function(e,t){for(var r,i,a=!1,n=!1,s=0;s<t.levels.length;s++){var o=t.levels[s].audioCodec;o&&(a=a||-1!==o.indexOf("mp4a.40.2"),n=n||-1!==o.indexOf("mp4a.40.5"))}this.audioCodecSwitch=a&&n&&!("function"==typeof(null==(i=Ja())||null==(r=i.prototype)?void 0:r.changeType)),this.audioCodecSwitch&&this.log("Both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.levels=t.levels,this.startFragRequested=!1},r.onLevelLoading=function(e,t){if(this.levels&&this.state===ka.IDLE){var r=t.levelInfo;(!r.details||r.details.live&&(this.levelLastLoaded!==r||r.details.expired)||this.waitForCdnTuneIn(r.details))&&(this.state=ka.WAITING_LEVEL)}},r.onLevelLoaded=function(e,t){var r,i=this.levels,a=this.startFragRequested,n=t.level,s=t.details,o=s.totalduration;if(i){this.log("Level "+n+" loaded ["+s.startSN+","+s.endSN+"]"+(s.lastPartSn?"[part-"+s.lastPartSn+"-"+s.lastPartIndex+"]":"")+", cc ["+s.startCC+", "+s.endCC+"] duration:"+o);var l=t.levelInfo,u=this.fragCurrent;!u||this.state!==ka.FRAG_LOADING&&this.state!==ka.FRAG_LOADING_WAITING_RETRY||u.level!==t.level&&u.loader&&this.abortCurrentFrag();var d=0;if(s.live||null!=(r=l.details)&&r.live){var h;if(this.checkLiveUpdate(s),s.deltaUpdateFailed)return;d=this.alignPlaylists(s,l.details,null==(h=this.levelLastLoaded)?void 0:h.details)}if(l.details=s,this.levelLastLoaded=l,a||this.setStartPosition(s,d),this.hls.trigger(D.LEVEL_UPDATED,{details:s,level:n}),this.state===ka.WAITING_LEVEL){if(this.waitForCdnTuneIn(s))return;this.state=ka.IDLE}a&&s.live&&this.synchronizeToLiveEdge(s),this.tick()}else this.warn("Levels were reset while loading level "+n)},r.synchronizeToLiveEdge=function(e){var t=this.config,r=this.media;if(r){var i=this.hls.liveSyncPosition,a=this.getLoadPosition(),n=e.fragmentStart,s=e.edge,o=a>=n-t.maxFragLookUpTolerance&&a<=s;if(null!==i&&r.duration>i&&(a<i||!o)){var l=void 0!==t.liveMaxLatencyDuration?t.liveMaxLatencyDuration:t.liveMaxLatencyDurationCount*e.targetduration;if((!o&&r.readyState<4||a<s-l)&&(this._hasEnoughToStart||(this.nextLoadPosition=i),r.readyState))if(this.warn("Playback: "+a.toFixed(3)+" is located too far from the end of live sliding playlist: "+s+", reset currentTime to : "+i.toFixed(3)),"buffered"===this.config.liveSyncMode){var u,d=_t.bufferInfo(r,i,0);if(null==(u=d.buffered)||!u.length)return void(r.currentTime=i);if(d.start<=a)return void(r.currentTime=i);var h=_t.bufferedInfo(d.buffered,a,0).nextStart;h&&(r.currentTime=h)}else r.currentTime=i}}},r._handleFragmentLoadProgress=function(e){var t,r=e.frag,i=e.part,a=e.payload,n=this.levels;if(n){var s=n[r.level];if(s){var o=s.details;if(!o)return this.warn("Dropping fragment "+r.sn+" of level "+r.level+" after level details were reset"),void this.fragmentTracker.removeFragment(r);var l=s.videoCodec,u=o.PTSKnown||!o.live,d=null==(t=r.initSegment)?void 0:t.data,h=this._getAudioCodec(s),f=this.transmuxer=this.transmuxer||new Za(this.hls,x,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)),c=i?i.index:-1,v=-1!==c,g=new Ta(r.level,r.sn,r.stats.chunkCount,a.byteLength,c,v),m=this.initPTS[r.cc];f.push(a,d,h,l,r,i,o.totalduration,u,g,m)}else this.warn("Level "+r.level+" not found on progress")}else this.warn("Levels were reset while fragment load was in progress. Fragment "+r.sn+" of level "+r.level+" will not be buffered")},r.onAudioTrackSwitching=function(e,t){var r=this,i=this.hls,a=2===this.altAudio;if(st(t.url,i))this.altAudio=1;else{if(this.mediaBuffer!==this.media){this.log("Switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media;var n=this.fragCurrent;n&&(this.log("Switching to main audio track, cancel main fragment load"),n.abortRequests(),this.fragmentTracker.removeFragment(n)),this.resetTransmuxer(),this.resetLoadingState()}else this.audioOnly&&this.resetTransmuxer();if(a)return this.fragmentTracker.removeAllFragments(),i.once(D.BUFFER_FLUSHED,(function(){r.hls&&r.hls.trigger(D.AUDIO_TRACK_SWITCHED,t)})),void i.trigger(D.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:null});i.trigger(D.AUDIO_TRACK_SWITCHED,t)}},r.onAudioTrackSwitched=function(e,t){var r=st(t.url,this.hls);if(r){var i=this.videoBuffer;i&&this.mediaBuffer!==i&&(this.log("Switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=i)}this.altAudio=r?2:0,this.tick()},r.onBufferCreated=function(e,t){var r,i,a=t.tracks,n=!1;for(var s in a){var o=a[s];if("main"===o.id){if(i=s,r=o,"video"===s){var l=a[s];l&&(this.videoBuffer=l.buffer)}}else n=!0}n&&r?(this.log("Alternate track found, use "+i+".buffered to schedule main fragment loading"),this.mediaBuffer=r.buffer):this.mediaBuffer=this.media},r.onFragBuffered=function(e,t){var r=t.frag,i=t.part,a=r.type===x;if(a){if(this.fragContextChanged(r))return this.warn("Fragment "+r.sn+(i?" p: "+i.index:"")+" of level "+r.level+" finished buffering, but was aborted. state: "+this.state),void(this.state===ka.PARSED&&(this.state=ka.IDLE));var n=i?i.stats:r.stats;this.fragLastKbps=Math.round(8*n.total/(n.buffering.end-n.loading.first)),ae(r)&&(this.fragPrevious=r),this.fragBufferedComplete(r,i)}var s=this.media;s&&(!this._hasEnoughToStart&&_t.getBuffered(s).length&&(this._hasEnoughToStart=!0,this.seekToStartPos()),a&&this.tick())},r.onError=function(e,t){var r;if(t.fatal)this.state=ka.ERROR;else switch(t.details){case k.FRAG_GAP:case k.FRAG_PARSING_ERROR:case k.FRAG_DECRYPT_ERROR:case k.FRAG_LOAD_ERROR:case k.FRAG_LOAD_TIMEOUT:case k.KEY_LOAD_ERROR:case k.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(x,t);break;case k.LEVEL_LOAD_ERROR:case k.LEVEL_LOAD_TIMEOUT:case k.LEVEL_PARSING_ERROR:t.levelRetry||this.state!==ka.WAITING_LEVEL||(null==(r=t.context)?void 0:r.type)!==I||(this.state=ka.IDLE);break;case k.BUFFER_ADD_CODEC_ERROR:case k.BUFFER_APPEND_ERROR:if("main"!==t.parent)return;this.reduceLengthAndFlushBuffer(t)&&this.resetLoadingState();break;case k.BUFFER_FULL_ERROR:if("main"!==t.parent)return;this.reduceLengthAndFlushBuffer(t)&&(!this.config.interstitialsController&&this.config.assetPlayerId?this._hasEnoughToStart=!0:this.flushMainBuffer(0,Number.POSITIVE_INFINITY));break;case k.INTERNAL_EXCEPTION:this.recoverWorkerError(t)}},r.onFragLoadEmergencyAborted=function(){this.state=ka.IDLE,this._hasEnoughToStart||(this.startFragRequested=!1,this.nextLoadPosition=this.lastCurrentTime),this.tickImmediate()},r.onBufferFlushed=function(e,t){var r=t.type;if(r!==ee||!this.altAudio){var i=(r===te?this.videoBuffer:this.mediaBuffer)||this.media;i&&(this.afterBufferFlushed(i,r,x),this.tick())}},r.onLevelsUpdated=function(e,t){this.level>-1&&this.fragCurrent&&(this.level=this.fragCurrent.level,-1===this.level&&this.resetWhenMissingContext(this.fragCurrent)),this.levels=t.levels},r.swapAudioCodec=function(){this.audioCodecSwap=!this.audioCodecSwap},r.seekToStartPos=function(){var e=this.media;if(e){var t=e.currentTime,r=this.startPosition;if(r>=0&&t<r){if(e.seeking)return void this.log("could not seek to "+r+", already seeking at "+t);var i=this.timelineOffset;i&&r&&(r+=i);var a=this.getLevelDetails(),n=_t.getBuffered(e),s=n.length?n.start(0):0,o=s-r,l=Math.max(this.config.maxBufferHole,this.config.maxFragLookUpTolerance);(this.config.startOnSegmentBoundary||o>0&&(o<l||this.loadingParts&&o<2*((null==a?void 0:a.partTarget)||0)))&&(this.log("adjusting start position by "+o+" to match buffer start"),r+=o,this.startPosition=r),t<r&&(this.log("seek to target start position "+r+" from current time "+t+" buffer start "+s),e.currentTime=r)}}},r._getAudioCodec=function(e){var t=this.config.defaultAudioCodec||e.audioCodec;return this.audioCodecSwap&&t&&(this.log("Swapping audio codec"),t=-1!==t.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5"),t},r._loadBitrateTestFrag=function(e,t){var r=this;e.bitrateTest=!0,this._doFragLoad(e,t).then((function(e){var i=r.hls,a=null==e?void 0:e.frag;if(a&&!r.fragContextChanged(a)){t.fragmentError=0,r.state=ka.IDLE,r.startFragRequested=!1,r.bitrateTest=!1;var n=a.stats;n.parsing.start=n.parsing.end=n.buffering.start=n.buffering.end=self.performance.now(),i.trigger(D.FRAG_LOADED,e),a.bitrateTest=!1}}))},r._handleTransmuxComplete=function(e){var t=this.playlistType,r=this.hls,i=e.remuxResult,a=e.chunkMeta,n=this.getCurrentContext(a);if(n){var s=n.frag,o=n.part,l=n.level,u=i.video,d=i.text,h=i.id3,f=i.initSegment,c=l.details,v=this.altAudio?void 0:i.audio;if(this.fragContextChanged(s))this.fragmentTracker.removeFragment(s);else{if(this.state=ka.PARSING,f){var g=f.tracks;if(g){var m=s.initSegment||s;if(this.unhandledEncryptionError(f,s))return;this._bufferInitSegment(l,g,m,a),r.trigger(D.FRAG_PARSING_INIT_SEGMENT,{frag:m,id:t,tracks:g})}var p=f.initPTS,y=f.timescale,E=this.initPTS[s.cc];if(L(p)&&(!E||E.baseTime!==p||E.timescale!==y)){var T=f.trackId;this.initPTS[s.cc]={baseTime:p,timescale:y,trackId:T},r.trigger(D.INIT_PTS_FOUND,{frag:s,id:t,initPTS:p,timescale:y,trackId:T})}}if(u&&c){v&&"audiovideo"===u.type&&this.logMuxedErr(s);var S=c.fragments[s.sn-1-c.startSN],R=s.sn===c.startSN,A=!S||s.cc>S.cc;if(!1!==i.independent){var b=u.startPTS,k=u.endPTS,_=u.startDTS,I=u.endDTS;if(o)o.elementaryStreams[u.type]={startPTS:b,endPTS:k,startDTS:_,endDTS:I};else if(u.firstKeyFrame&&u.independent&&1===a.id&&!A&&(this.couldBacktrack=!0),u.dropped&&u.independent){var C=this.getMainFwdBufferInfo(),P=(C?C.end:this.getLoadPosition())+this.config.maxBufferHole,x=u.firstKeyFramePTS?u.firstKeyFramePTS:b;if(!R&&P<x-this.config.maxBufferHole&&!A)return void this.backtrack(s);A&&(s.gap=!0),s.setElementaryStreamInfo(u.type,s.start,k,s.start,I,!0)}else R&&b-(c.appliedTimelineOffset||0)>2&&(s.gap=!0);s.setElementaryStreamInfo(u.type,b,k,_,I),this.backtrackFragment&&(this.backtrackFragment=s),this.bufferFragmentData(u,s,o,a,R||A)}else{if(!R&&!A)return void this.backtrack(s);s.gap=!0}}if(v){var w=v.startPTS,O=v.endPTS,F=v.startDTS,M=v.endDTS;o&&(o.elementaryStreams[ee]={startPTS:w,endPTS:O,startDTS:F,endDTS:M}),s.setElementaryStreamInfo(ee,w,O,F,M),this.bufferFragmentData(v,s,o,a)}if(c&&null!=h&&h.samples.length){var N={id:t,frag:s,details:c,samples:h.samples};r.trigger(D.FRAG_PARSING_METADATA,N)}if(c&&d){var B={id:t,frag:s,details:c,samples:d.samples};r.trigger(D.FRAG_PARSING_USERDATA,B)}}}else this.resetWhenMissingContext(a)},r.logMuxedErr=function(e){this.warn((ae(e)?"Media":"Init")+" segment with muxed audiovideo where only video expected: "+e.url)},r._bufferInitSegment=function(e,t,r,i){var a=this;if(this.state===ka.PARSING){this.audioOnly=!!t.audio&&!t.video,this.altAudio&&!this.audioOnly&&(delete t.audio,t.audiovideo&&this.logMuxedErr(r));var n=t.audio,s=t.video,o=t.audiovideo;if(n){var l=e.audioCodec,u=He(n.codec,l);"mp4a"===u&&(u="mp4a.40.5");var d=navigator.userAgent.toLowerCase();if(this.audioCodecSwitch){u&&(u=-1!==u.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5");var h=n.metadata;h&&"channelCount"in h&&1!==(h.channelCount||1)&&-1===d.indexOf("firefox")&&(u="mp4a.40.5")}u&&-1!==u.indexOf("mp4a.40.5")&&-1!==d.indexOf("android")&&"audio/mpeg"!==n.container&&(u="mp4a.40.2",this.log("Android: force audio codec to "+u)),l&&l!==u&&this.log('Swapping manifest audio codec "'+l+'" for "'+u+'"'),n.levelCodec=u,n.id=x,this.log("Init audio buffer, container:"+n.container+", codecs[selected/level/parsed]=["+(u||"")+"/"+(l||"")+"/"+n.codec+"]"),delete t.audiovideo}if(s){s.levelCodec=e.videoCodec,s.id=x;var f=s.codec;if(4===(null==f?void 0:f.length))switch(f){case"hvc1":case"hev1":s.codec="hvc1.1.6.L120.90";break;case"av01":s.codec="av01.0.04M.08";break;case"avc1":s.codec="avc1.42e01e"}this.log("Init video buffer, container:"+s.container+", codecs[level/parsed]=["+(e.videoCodec||"")+"/"+f+"]"+(s.codec!==f?" parsed-corrected="+s.codec:"")+(s.supplemental?" supplemental="+s.supplemental:"")),delete t.audiovideo}o&&(this.log("Init audiovideo buffer, container:"+o.container+", codecs[level/parsed]=["+e.codecs+"/"+o.codec+"]"),delete t.video,delete t.audio);var c=Object.keys(t);if(c.length){if(this.hls.trigger(D.BUFFER_CODECS,t),!this.hls)return;c.forEach((function(e){var n=t[e].initSegment;null!=n&&n.byteLength&&a.hls.trigger(D.BUFFER_APPENDING,{type:e,data:n,frag:r,part:null,chunkMeta:i,parent:r.type})}))}this.tickImmediate()}},r.getMainFwdBufferInfo=function(){var e=this.mediaBuffer&&2===this.altAudio?this.mediaBuffer:this.media;return this.getFwdBufferInfo(e,x)},r.backtrack=function(e){this.couldBacktrack=!0,this.backtrackFragment=e,this.resetTransmuxer(),this.flushBufferGap(e),this.fragmentTracker.removeFragment(e),this.fragPrevious=null,this.nextLoadPosition=e.start,this.state=ka.IDLE},r.checkFragmentChanged=function(){var e=this.media,t=null;if(e&&e.readyState>1&&!1===e.seeking){var r=e.currentTime;if(_t.isBuffered(e,r)?t=this.getAppendedFrag(r):_t.isBuffered(e,r+.1)&&(t=this.getAppendedFrag(r+.1)),t){this.backtrackFragment=null;var i=this.fragPlaying,a=t.level;i&&t.sn===i.sn&&i.level===a||(this.fragPlaying=t,this.hls.trigger(D.FRAG_CHANGED,{frag:t}),i&&i.level===a||this.hls.trigger(D.LEVEL_SWITCHED,{level:a}))}}},i(t,[{key:"hasEnoughToStart",get:function(){return this._hasEnoughToStart}},{key:"maxBufferLength",get:function(){var e=this.levels,t=this.level,r=null==e?void 0:e[t];return r?this.getMaxBufferLength(r.maxBitrate):this.config.maxBufferLength}},{key:"nextLevel",get:function(){var e=this.nextBufferedFrag;return e?e.level:-1}},{key:"currentFrag",get:function(){var e;if(this.fragPlaying)return this.fragPlaying;var t=(null==(e=this.media)?void 0:e.currentTime)||this.lastCurrentTime;return L(t)?this.getAppendedFrag(t):null}},{key:"currentProgramDateTime",get:function(){var e,t=(null==(e=this.media)?void 0:e.currentTime)||this.lastCurrentTime;if(L(t)){var r=this.getLevelDetails(),i=this.currentFrag||(r?dt(null,r.fragments,t):null);if(i){var a=i.programDateTime;if(null!==a){var n=a+1e3*(t-i.start);return new Date(n)}}}return null}},{key:"currentLevel",get:function(){var e=this.currentFrag;return e?e.level:-1}},{key:"nextBufferedFrag",get:function(){var e=this.currentFrag;return e?this.followingBufferedFrag(e):null}},{key:"forceStartLoad",get:function(){return this._forceStartLoad}}])}(Da),rn=function(){function e(e){this.config=void 0,this.keyUriToKeyInfo={},this.emeController=null,this.config=e}var t=e.prototype;return t.abort=function(e){for(var t in this.keyUriToKeyInfo){var r=this.keyUriToKeyInfo[t].loader;if(r){var i;if(e&&e!==(null==(i=r.context)?void 0:i.frag.type))return;r.abort()}}},t.detach=function(){for(var e in this.keyUriToKeyInfo){var t=this.keyUriToKeyInfo[e];(t.mediaKeySessionContext||t.decryptdata.isCommonEncryption)&&delete this.keyUriToKeyInfo[e]}},t.destroy=function(){for(var e in this.detach(),this.keyUriToKeyInfo){var t=this.keyUriToKeyInfo[e].loader;t&&t.destroy()}this.keyUriToKeyInfo={}},t.createKeyLoadError=function(e,t,r,i,a){return void 0===t&&(t=k.KEY_LOAD_ERROR),new ya({type:b.NETWORK_ERROR,details:t,fatal:!1,frag:e,response:a,error:r,networkDetails:i})},t.loadClear=function(e,t,r){var i=this;if(this.emeController&&this.config.emeEnabled&&!this.emeController.getSelectedKeySystemFormats().length){if(t.length)for(var a,n=function(){var a=t[s];if(e.cc<=a.cc&&(!ae(e)||!ae(a)||e.sn<a.sn)||!r&&s==o-1)return{v:i.emeController.selectKeySystemFormat(a).then((function(e){if(i.emeController){a.setKeyFormat(e);var t=q.keySystemFormatToKeySystemDomain(e);return t?i.emeController.getKeySystemAccess([t]):void 0}}))}},s=0,o=t.length;s<o;s++)if(a=n())return a.v;if(this.config.requireKeySystemAccessOnStart){var l=q.getKeySystemsForConfig(this.config);if(l.length)return this.emeController.getKeySystemAccess(l)}}return null},t.load=function(e){var t=this;return!e.decryptdata&&e.encrypted&&this.emeController&&this.config.emeEnabled?this.emeController.selectKeySystemFormat(e).then((function(r){return t.loadInternal(e,r)})):this.loadInternal(e)},t.loadInternal=function(e,t){var r,i;t&&e.setKeyFormat(t);var a=e.decryptdata;if(!a){var n=new Error(t?"Expected frag.decryptdata to be defined after setting format "+t:"Missing decryption data on fragment in onKeyLoading");return Promise.reject(this.createKeyLoadError(e,k.KEY_LOAD_ERROR,n))}var s=a.uri;if(!s)return Promise.reject(this.createKeyLoadError(e,k.KEY_LOAD_ERROR,new Error('Invalid key URI: "'+s+'"')));var o,l=this.keyUriToKeyInfo[s];if(null!=(r=l)&&r.decryptdata.key)return a.key=l.decryptdata.key,Promise.resolve({frag:e,keyInfo:l});if(null!=(i=l)&&i.keyLoadPromise)switch(null==(o=l.mediaKeySessionContext)?void 0:o.keyStatus){case void 0:case"status-pending":case"usable":case"usable-in-future":return l.keyLoadPromise.then((function(t){return a.key=t.keyInfo.decryptdata.key,{frag:e,keyInfo:l}}))}switch(l=this.keyUriToKeyInfo[s]={decryptdata:a,keyLoadPromise:null,loader:null,mediaKeySessionContext:null},a.method){case"ISO-23001-7":case"SAMPLE-AES":case"SAMPLE-AES-CENC":case"SAMPLE-AES-CTR":return"identity"===a.keyFormat?this.loadKeyHTTP(l,e):this.loadKeyEME(l,e);case"AES-128":case"AES-256":case"AES-256-CTR":return this.loadKeyHTTP(l,e);default:return Promise.reject(this.createKeyLoadError(e,k.KEY_LOAD_ERROR,new Error('Key supplied with unsupported METHOD: "'+a.method+'"')))}},t.loadKeyEME=function(e,t){var r={frag:t,keyInfo:e};if(this.emeController&&this.config.emeEnabled){var i=this.emeController.loadKey(r);if(i)return(e.keyLoadPromise=i.then((function(t){return e.mediaKeySessionContext=t,r}))).catch((function(t){throw e.keyLoadPromise=null,t}))}return Promise.resolve(r)},t.loadKeyHTTP=function(e,t){var r=this,i=this.config,a=new(0,i.loader)(i);return t.keyLoader=e.loader=a,e.keyLoadPromise=new Promise((function(n,s){var o={keyInfo:e,frag:t,responseType:"arraybuffer",url:e.decryptdata.uri},l=i.keyLoadPolicy.default,u={loadPolicy:l,timeout:l.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0},h={onSuccess:function(e,t,i,a){var o=i.frag,l=i.keyInfo,u=i.url;if(!o.decryptdata||l!==r.keyUriToKeyInfo[u])return s(r.createKeyLoadError(o,k.KEY_LOAD_ERROR,new Error("after key load, decryptdata unset or changed"),a));l.decryptdata.key=o.decryptdata.key=new Uint8Array(e.data),o.keyLoader=null,l.loader=null,n({frag:o,keyInfo:l})},onError:function(e,i,a,n){r.resetLoader(i),s(r.createKeyLoadError(t,k.KEY_LOAD_ERROR,new Error("HTTP Error "+e.code+" loading key "+e.text),a,d({url:o.url,data:void 0},e)))},onTimeout:function(e,i,a){r.resetLoader(i),s(r.createKeyLoadError(t,k.KEY_LOAD_TIMEOUT,new Error("key loading timed out"),a))},onAbort:function(e,i,a){r.resetLoader(i),s(r.createKeyLoadError(t,k.INTERNAL_ABORTED,new Error("key loading aborted"),a))}};a.load(o,u,h)}))},t.resetLoader=function(e){var t=e.frag,r=e.keyInfo,i=e.url,a=r.loader;t.keyLoader===a&&(t.keyLoader=null,r.loader=null),delete this.keyUriToKeyInfo[i],a&&a.destroy()},e}();function an(e){switch(e.type){case C:return w;case P:return O;default:return x}}function nn(e,t){var r=e.url;return void 0!==r&&0!==r.indexOf("data:")||(r=t.url),r}var sn=function(){function e(e){this.hls=void 0,this.loaders=Object.create(null),this.variableList=null,this.onManifestLoaded=this.checkAutostartLoad,this.hls=e,this.registerListeners()}var t=e.prototype;return t.startLoad=function(e){},t.stopLoad=function(){this.destroyInternalLoaders()},t.registerListeners=function(){var e=this.hls;e.on(D.MANIFEST_LOADING,this.onManifestLoading,this),e.on(D.LEVEL_LOADING,this.onLevelLoading,this),e.on(D.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),e.on(D.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this),e.on(D.LEVELS_UPDATED,this.onLevelsUpdated,this)},t.unregisterListeners=function(){var e=this.hls;e.off(D.MANIFEST_LOADING,this.onManifestLoading,this),e.off(D.LEVEL_LOADING,this.onLevelLoading,this),e.off(D.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),e.off(D.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this),e.off(D.LEVELS_UPDATED,this.onLevelsUpdated,this)},t.createInternalLoader=function(e){var t=this.hls.config,r=t.pLoader,i=t.loader,a=new(r||i)(t);return this.loaders[e.type]=a,a},t.getInternalLoader=function(e){return this.loaders[e.type]},t.resetInternalLoader=function(e){this.loaders[e]&&delete this.loaders[e]},t.destroyInternalLoaders=function(){for(var e in this.loaders){var t=this.loaders[e];t&&t.destroy(),this.resetInternalLoader(e)}},t.destroy=function(){this.variableList=null,this.unregisterListeners(),this.destroyInternalLoaders()},t.onManifestLoading=function(e,t){var r=t.url;this.variableList=null,this.load({id:null,level:0,responseType:"text",type:_,url:r,deliveryDirectives:null,levelOrTrack:null})},t.onLevelLoading=function(e,t){var r=t.id,i=t.level,a=t.pathwayId,n=t.url,s=t.deliveryDirectives,o=t.levelInfo;this.load({id:r,level:i,pathwayId:a,responseType:"text",type:I,url:n,deliveryDirectives:s,levelOrTrack:o})},t.onAudioTrackLoading=function(e,t){var r=t.id,i=t.groupId,a=t.url,n=t.deliveryDirectives,s=t.track;this.load({id:r,groupId:i,level:null,responseType:"text",type:C,url:a,deliveryDirectives:n,levelOrTrack:s})},t.onSubtitleTrackLoading=function(e,t){var r=t.id,i=t.groupId,a=t.url,n=t.deliveryDirectives,s=t.track;this.load({id:r,groupId:i,level:null,responseType:"text",type:P,url:a,deliveryDirectives:n,levelOrTrack:s})},t.onLevelsUpdated=function(e,t){var r=this.loaders[I];if(r){var i=r.context;i&&!t.levels.some((function(e){return e===i.levelOrTrack}))&&(r.abort(),delete this.loaders[I])}},t.load=function(e){var t,r,i,a=this,s=this.hls.config,o=this.getInternalLoader(e);if(o){var l=this.hls.logger,u=o.context;if(u&&u.levelOrTrack===e.levelOrTrack&&(u.url===e.url||u.deliveryDirectives&&!e.deliveryDirectives))return void(u.url===e.url?l.log("[playlist-loader]: ignore "+e.url+" ongoing request"):l.log("[playlist-loader]: ignore "+e.url+" in favor of "+u.url));l.log("[playlist-loader]: aborting previous loader for type: "+e.type),o.abort()}if(r=e.type===_?s.manifestLoadPolicy.default:n({},s.playlistLoadPolicy.default,{timeoutRetry:null,errorRetry:null}),o=this.createInternalLoader(e),L(null==(t=e.deliveryDirectives)?void 0:t.part)&&(e.type===I&&null!==e.level?i=this.hls.levels[e.level].details:e.type===C&&null!==e.id?i=this.hls.audioTracks[e.id].details:e.type===P&&null!==e.id&&(i=this.hls.subtitleTracks[e.id].details),i)){var d=i.partTarget,h=i.targetduration;if(d&&h){var f=1e3*Math.max(3*d,.8*h);r=n({},r,{maxTimeToFirstByteMs:Math.min(f,r.maxTimeToFirstByteMs),maxLoadTimeMs:Math.min(f,r.maxTimeToFirstByteMs)})}}var c=r.errorRetry||r.timeoutRetry||{},v={loadPolicy:r,timeout:r.maxLoadTimeMs,maxRetry:c.maxNumRetry||0,retryDelay:c.retryDelayMs||0,maxRetryDelay:c.maxRetryDelayMs||0},g={onSuccess:function(e,t,r,i){var n=a.getInternalLoader(r);a.resetInternalLoader(r.type);var s=e.data;0===s.indexOf("#EXTM3U")?(t.parsing.start=performance.now(),Zt.isMediaPlaylist(s)||r.type!==_?a.handleTrackOrLevelPlaylist(e,t,r,i||null,n):a.handleMasterPlaylist(e,t,r,i)):a.handleManifestParsingError(e,r,new Error("no EXTM3U delimiter"),i||null,t)},onError:function(e,t,r,i){a.handleNetworkError(t,r,!1,e,i)},onTimeout:function(e,t,r){a.handleNetworkError(t,r,!0,void 0,e)}};o.load(e,v,g)},t.checkAutostartLoad=function(){if(this.hls){var e=this.hls,t=e.config,r=t.autoStartLoad,i=t.startPosition,a=e.forceStartLoad;(r||a)&&(this.hls.logger.log((r?"auto":"force")+" startLoad with configured startPosition "+i),this.hls.startLoad(i))}},t.handleMasterPlaylist=function(e,t,r,i){var a=this.hls,n=e.data,s=nn(e,r),o=Zt.parseMasterPlaylist(n,s);if(o.playlistParsingError)this.handleManifestParsingError(e,r,o.playlistParsingError,i,t);else{var l=o.contentSteering,u=o.levels,d=o.sessionData,h=o.sessionKeys,f=o.startTimeOffset,c=o.variableList;this.variableList=c;var v=Zt.parseMasterPlaylistMedia(n,s,o),g=v.AUDIO,m=void 0===g?[]:g,p=v.SUBTITLES,y=v["CLOSED-CAPTIONS"];m.length&&(m.some((function(e){return!e.url}))||!u[0].audioCodec||u[0].attrs.AUDIO||(this.hls.logger.log("[playlist-loader]: audio codec signaled in quality level, but no embedded audio track signaled, create one"),m.unshift({type:"main",name:"main",groupId:"main",default:!1,autoselect:!1,forced:!1,id:-1,attrs:new Bt({}),bitrate:0,url:""}))),a.trigger(D.MANIFEST_LOADED,{levels:u,audioTracks:m,subtitles:p,captions:y,contentSteering:l,url:s,stats:t,networkDetails:i,sessionData:d,sessionKeys:h,startTimeOffset:f,variableList:c})}},t.handleTrackOrLevelPlaylist=function(e,t,r,i,a){var n=this.hls,s=r.id,o=r.level,l=r.type,u=nn(e,r),d=L(o)?o:L(s)?s:0,h=an(r),f=Zt.parseLevelPlaylist(e.data,u,d,h,0,this.variableList);if(l===_){var c={attrs:new Bt({}),bitrate:0,details:f,name:"",url:u};f.requestScheduled=t.loading.start+gr(f,0),n.trigger(D.MANIFEST_LOADED,{levels:[c],audioTracks:[],url:u,stats:t,networkDetails:i,sessionData:null,sessionKeys:null,contentSteering:null,startTimeOffset:null,variableList:null})}t.parsing.end=performance.now(),r.levelDetails=f,this.handlePlaylistLoaded(f,e,t,r,i,a)},t.handleManifestParsingError=function(e,t,r,i,a){this.hls.trigger(D.ERROR,{type:b.NETWORK_ERROR,details:k.MANIFEST_PARSING_ERROR,fatal:t.type===_,url:e.url,err:r,error:r,reason:r.message,response:e,context:t,networkDetails:i,stats:a})},t.handleNetworkError=function(e,t,r,i,a){void 0===r&&(r=!1);var n="A network "+(r?"timeout":"error"+(i?" (status "+i.code+")":""))+" occurred while loading "+e.type;e.type===I?n+=": "+e.level+" id: "+e.id:e.type!==C&&e.type!==P||(n+=" id: "+e.id+' group-id: "'+e.groupId+'"');var s=new Error(n);this.hls.logger.warn("[playlist-loader]: "+n);var o=k.UNKNOWN,l=!1,u=this.getInternalLoader(e);switch(e.type){case _:o=r?k.MANIFEST_LOAD_TIMEOUT:k.MANIFEST_LOAD_ERROR,l=!0;break;case I:o=r?k.LEVEL_LOAD_TIMEOUT:k.LEVEL_LOAD_ERROR,l=!1;break;case C:o=r?k.AUDIO_TRACK_LOAD_TIMEOUT:k.AUDIO_TRACK_LOAD_ERROR,l=!1;break;case P:o=r?k.SUBTITLE_TRACK_LOAD_TIMEOUT:k.SUBTITLE_LOAD_ERROR,l=!1}u&&this.resetInternalLoader(e.type);var h={type:b.NETWORK_ERROR,details:o,fatal:l,url:e.url,loader:u,context:e,error:s,networkDetails:t,stats:a};if(i){var f=(null==t?void 0:t.url)||e.url;h.response=d({url:f,data:void 0},i)}this.hls.trigger(D.ERROR,h)},t.handlePlaylistLoaded=function(e,t,r,i,a,n){var s=this.hls,o=i.type,l=i.level,u=i.id,d=i.groupId,h=i.deliveryDirectives,f=nn(t,i),c=an(i),v="number"==typeof i.level&&c===x?l:void 0;if(e.fragments.length){e.targetduration||(e.playlistParsingError=new Error("Missing Target Duration"));var g=e.playlistParsingError;if(g){if(this.hls.logger.warn(g+" "+e.url),!s.config.ignorePlaylistParsingErrors)return void s.trigger(D.ERROR,{type:b.NETWORK_ERROR,details:k.LEVEL_PARSING_ERROR,fatal:!1,url:f,error:g,reason:g.message,response:t,context:i,level:v,parent:c,networkDetails:a,stats:r});e.playlistParsingError=null}switch(e.live&&n&&(n.getCacheAge&&(e.ageHeader=n.getCacheAge()||0),n.getCacheAge&&!isNaN(e.ageHeader)||(e.ageHeader=0)),o){case _:case I:s.trigger(D.LEVEL_LOADED,{details:e,levelInfo:i.levelOrTrack||s.levels[0],level:v||0,id:u||0,stats:r,networkDetails:a,deliveryDirectives:h,withoutMultiVariant:o===_});break;case C:s.trigger(D.AUDIO_TRACK_LOADED,{details:e,track:i.levelOrTrack,id:u||0,groupId:d||"",stats:r,networkDetails:a,deliveryDirectives:h});break;case P:s.trigger(D.SUBTITLE_TRACK_LOADED,{details:e,track:i.levelOrTrack,id:u||0,groupId:d||"",stats:r,networkDetails:a,deliveryDirectives:h})}}else{var m=e.playlistParsingError=new Error("No Segments found in Playlist");s.trigger(D.ERROR,{type:b.NETWORK_ERROR,details:k.LEVEL_EMPTY_ERROR,fatal:!1,url:f,error:m,reason:m.message,response:t,context:i,level:v,parent:c,networkDetails:a,stats:r})}},e}(),on={supported:!0,configurations:[],decodingInfoResults:[{supported:!0,powerEfficient:!0,smooth:!0}]};function ln(e,t,r,i){void 0===i&&(i={});var a=e.videoCodec;if(!a&&!e.audioCodec||!r)return Promise.resolve(on);for(var n=[],s=function(e){var t,r=null==(t=e.videoCodec)?void 0:t.split(","),i=dn(e),a=e.width||640,n=e.height||480,s=e.frameRate||30,o=e.videoRange.toLowerCase();return r?r.map((function(e){var t={contentType:Me(We(e),"video"),width:a,height:n,bitrate:i,framerate:s};return"sdr"!==o&&(t.transferFunction=o),t})):[]}(e),o=s.length,l=function(e,t,r){var i,a=null==(i=e.audioCodec)?void 0:i.split(","),n=dn(e);return a&&e.audioGroups?e.audioGroups.reduce((function(e,i){var s,o=i?null==(s=t.groups[i])?void 0:s.tracks:null;return o?o.reduce((function(e,t){if(t.groupId===i){var s=parseFloat(t.channels||"");a.forEach((function(t){var i={contentType:Me(t,"audio"),bitrate:r?un(t,n):n};s&&(i.channels=""+s),e.push(i)}))}return e}),e):e}),[]):[]}(e,t,o>0),u=l.length,d=o||1*u||1;d--;){var h={type:"media-source"};if(o&&(h.video=s[d%o]),u){h.audio=l[d%u];var f=h.audio.bitrate;h.video&&f&&(h.video.bitrate-=f)}n.push(h)}if(a){var c=navigator.userAgent;if(a.split(",").some((function(e){return De(e)}))&&Pe())return Promise.resolve(function(e,t){return{supported:!1,configurations:t,decodingInfoResults:[{supported:!1,smooth:!1,powerEfficient:!1}],error:e}}(new Error("Overriding Windows Firefox HEVC MediaCapabilities result based on user-agent string: ("+c+")"),n))}return Promise.all(n.map((function(e){var t,a,n,s,o=(a="",n=(t=e).audio,(s=t.video)&&(a+=je(s.contentType)+"_r"+s.height+"x"+s.width+"f"+Math.ceil(s.framerate)+(s.transferFunction||"sd")+"_"+Math.ceil(s.bitrate/1e5)),n&&(a+=(s?"_":"")+je(n.contentType)+"_c"+n.channels),a);return i[o]||(i[o]=r.decodingInfo(e))}))).then((function(e){return{supported:!e.some((function(e){return!e.supported})),configurations:n,decodingInfoResults:e}})).catch((function(e){return{supported:!1,configurations:n,decodingInfoResults:[],error:e}}))}function un(e,t){if(t<=1)return 1;var r=128e3;return"ec-3"===e?r=768e3:"ac-3"===e&&(r=64e4),Math.min(t/2,r)}function dn(e){return 1e3*Math.ceil(Math.max(.9*e.bitrate,e.averageBitrate)/1e3)||1}var hn=function(){function e(t){void 0===t&&(t={}),this.config=void 0,this.userConfig=void 0,this.logger=void 0,this.coreComponents=void 0,this.networkControllers=void 0,this._emitter=new E,this._autoLevelCapping=-1,this._maxHdcpLevel=null,this.abrController=void 0,this.bufferController=void 0,this.capLevelController=void 0,this.latencyController=void 0,this.levelController=void 0,this.streamController=void 0,this.audioStreamController=void 0,this.subtititleStreamController=void 0,this.audioTrackController=void 0,this.subtitleTrackController=void 0,this.interstitialsController=void 0,this.gapController=void 0,this.emeController=void 0,this.cmcdController=void 0,this._media=null,this._url=null,this._sessionId=void 0,this.triggeringException=void 0,this.started=!1;var r=this.logger=K(t.debug||!1,"Hls instance",t.assetPlayerId),i=this.config=function(e,t,r){if((t.liveSyncDurationCount||t.liveMaxLatencyDurationCount)&&(t.liveSyncDuration||t.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");if(void 0!==t.liveMaxLatencyDurationCount&&(void 0===t.liveSyncDurationCount||t.liveMaxLatencyDurationCount<=t.liveSyncDurationCount))throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be greater than "liveSyncDurationCount"');if(void 0!==t.liveMaxLatencyDuration&&(void 0===t.liveSyncDuration||t.liveMaxLatencyDuration<=t.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be greater than "liveSyncDuration"');var i=aa(e),a=["TimeOut","MaxRetry","RetryDelay","MaxRetryTimeout"];return["manifest","level","frag"].forEach((function(e){var n=("level"===e?"playlist":e)+"LoadPolicy",s=void 0===t[n],o=[];a.forEach((function(r){var a=e+"Loading"+r,l=t[a];if(void 0!==l&&s){o.push(a);var u=i[n].default;switch(t[n]={default:u},r){case"TimeOut":u.maxLoadTimeMs=l,u.maxTimeToFirstByteMs=l;break;case"MaxRetry":u.errorRetry.maxNumRetry=l,u.timeoutRetry.maxNumRetry=l;break;case"RetryDelay":u.errorRetry.retryDelayMs=l,u.timeoutRetry.retryDelayMs=l;break;case"MaxRetryTimeout":u.errorRetry.maxRetryDelayMs=l,u.timeoutRetry.maxRetryDelayMs=l}}})),o.length&&r.warn('hls.js config: "'+o.join('", "')+'" setting(s) are deprecated, use "'+n+'": '+it(t[n]))})),d(d({},i),t)}(e.DefaultConfig,t,r);this.userConfig=t,i.progressive&&na(i,r);var a=i.abrController,n=i.bufferController,s=i.capLevelController,o=i.errorController,l=i.fpsController,u=new o(this),h=this.abrController=new a(this),f=new da(this),c=i.interstitialsController,v=c?this.interstitialsController=new c(this,e):null,g=this.bufferController=new n(this,f),m=this.capLevelController=new s(this),p=new l(this),y=new sn(this),T=i.contentSteeringController,S=T?new T(this):null,L=this.levelController=new Ha(this,S),R=new Ua(this),A=new rn(this.config),b=this.streamController=new tn(this,f,A),k=this.gapController=new Ca(this,f);m.setStreamController(b),p.setStreamController(b);var _=[y,L,b];v&&_.splice(1,0,v),S&&_.splice(1,0,S),this.networkControllers=_;var I=[h,g,k,m,p,R,f];this.audioTrackController=this.createController(i.audioTrackController,_);var C=i.audioStreamController;C&&_.push(this.audioStreamController=new C(this,f,A)),this.subtitleTrackController=this.createController(i.subtitleTrackController,_);var P=i.subtitleStreamController;P&&_.push(this.subtititleStreamController=new P(this,f,A)),this.createController(i.timelineController,I),A.emeController=this.emeController=this.createController(i.emeController,I),this.cmcdController=this.createController(i.cmcdController,I),this.latencyController=this.createController(Ga,I),this.coreComponents=I,_.push(u);var x=u.onErrorOut;"function"==typeof x&&this.on(D.ERROR,x,u),this.on(D.MANIFEST_LOADED,y.onManifestLoaded,y)}e.isMSESupported=function(){return en()},e.isSupported=function(){return function(){if(!en())return!1;var e=z();return"function"==typeof(null==e?void 0:e.isTypeSupported)&&(["avc1.42E01E,mp4a.40.2","av01.0.01M.08","vp09.00.50.08"].some((function(t){return e.isTypeSupported(Me(t,"video"))}))||["mp4a.40.2","fLaC"].some((function(t){return e.isTypeSupported(Me(t,"audio"))})))}()},e.getMediaSource=function(){return z()};var t=e.prototype;return t.createController=function(e,t){if(e){var r=new e(this);return t&&t.push(r),r}return null},t.on=function(e,t,r){void 0===r&&(r=this),this._emitter.on(e,t,r)},t.once=function(e,t,r){void 0===r&&(r=this),this._emitter.once(e,t,r)},t.removeAllListeners=function(e){this._emitter.removeAllListeners(e)},t.off=function(e,t,r,i){void 0===r&&(r=this),this._emitter.off(e,t,r,i)},t.listeners=function(e){return this._emitter.listeners(e)},t.emit=function(e,t,r){return this._emitter.emit(e,t,r)},t.trigger=function(e,t){if(this.config.debug)return this.emit(e,e,t);try{return this.emit(e,e,t)}catch(t){if(this.logger.error("An internal error happened while handling event "+e+'. Error message: "'+t.message+'". Here is a stacktrace:',t),!this.triggeringException){this.triggeringException=!0;var r=e===D.ERROR;this.trigger(D.ERROR,{type:b.OTHER_ERROR,details:k.INTERNAL_EXCEPTION,fatal:r,event:e,error:t}),this.triggeringException=!1}}return!1},t.listenerCount=function(e){return this._emitter.listenerCount(e)},t.destroy=function(){this.logger.log("destroy"),this.trigger(D.DESTROYING,void 0),this.detachMedia(),this.removeAllListeners(),this._autoLevelCapping=-1,this._url=null,this.networkControllers.forEach((function(e){return e.destroy()})),this.networkControllers.length=0,this.coreComponents.forEach((function(e){return e.destroy()})),this.coreComponents.length=0;var e=this.config;e.xhrSetup=e.fetchSetup=void 0,this.userConfig=null},t.attachMedia=function(e){if(!e||"media"in e&&!e.media){var t=new Error("attachMedia failed: invalid argument ("+e+")");this.trigger(D.ERROR,{type:b.OTHER_ERROR,details:k.ATTACH_MEDIA_ERROR,fatal:!0,error:t})}else{this.logger.log("attachMedia"),this._media&&(this.logger.warn("media must be detached before attaching"),this.detachMedia());var r="media"in e,i=r?e.media:e,a=r?e:{media:i};this._media=i,this.trigger(D.MEDIA_ATTACHING,a)}},t.detachMedia=function(){this.logger.log("detachMedia"),this.trigger(D.MEDIA_DETACHING,{}),this._media=null},t.transferMedia=function(){this._media=null;var e=this.bufferController.transferMedia();return this.trigger(D.MEDIA_DETACHING,{transferMedia:e}),e},t.loadSource=function(e){this.stopLoad();var t=this.media,r=this._url,i=this._url=S.buildAbsoluteURL(self.location.href,e,{alwaysNormalize:!0});this._autoLevelCapping=-1,this._maxHdcpLevel=null,this.logger.log("loadSource:"+i),t&&r&&(r!==i||this.bufferController.hasSourceTypes())&&(this.detachMedia(),this.attachMedia(t)),this.trigger(D.MANIFEST_LOADING,{url:e})},t.startLoad=function(e,t){void 0===e&&(e=-1),this.logger.log("startLoad("+e+(t?", <skip seek to start>":"")+")"),this.started=!0,this.resumeBuffering();for(var r=0;r<this.networkControllers.length&&(this.networkControllers[r].startLoad(e,t),this.started&&this.networkControllers);r++);},t.stopLoad=function(){this.logger.log("stopLoad"),this.started=!1;for(var e=0;e<this.networkControllers.length&&(this.networkControllers[e].stopLoad(),!this.started&&this.networkControllers);e++);},t.resumeBuffering=function(){this.bufferingEnabled||(this.logger.log("resume buffering"),this.networkControllers.forEach((function(e){e.resumeBuffering&&e.resumeBuffering()})))},t.pauseBuffering=function(){this.bufferingEnabled&&(this.logger.log("pause buffering"),this.networkControllers.forEach((function(e){e.pauseBuffering&&e.pauseBuffering()})))},t.swapAudioCodec=function(){this.logger.log("swapAudioCodec"),this.streamController.swapAudioCodec()},t.recoverMediaError=function(){this.logger.log("recoverMediaError");var e=this._media,t=null==e?void 0:e.currentTime;this.detachMedia(),e&&(this.attachMedia(e),t&&this.startLoad(t))},t.removeLevel=function(e){this.levelController.removeLevel(e)},t.setAudioOption=function(e){var t;return(null==(t=this.audioTrackController)?void 0:t.setAudioOption(e))||null},t.setSubtitleOption=function(e){var t;return(null==(t=this.subtitleTrackController)?void 0:t.setSubtitleOption(e))||null},t.getMediaDecodingInfo=function(e,t){return void 0===t&&(t=this.allAudioTracks),ln(e,nt(t),navigator.mediaCapabilities)},i(e,[{key:"url",get:function(){return this._url}},{key:"hasEnoughToStart",get:function(){return this.streamController.hasEnoughToStart}},{key:"startPosition",get:function(){return this.streamController.startPositionValue}},{key:"loadingEnabled",get:function(){return this.started}},{key:"bufferingEnabled",get:function(){return this.streamController.bufferingEnabled}},{key:"inFlightFragments",get:function(){var e,t=((e={})[x]=this.streamController.inFlightFrag,e);return this.audioStreamController&&(t[w]=this.audioStreamController.inFlightFrag),this.subtititleStreamController&&(t[O]=this.subtititleStreamController.inFlightFrag),t}},{key:"sessionId",get:function(){var e=this._sessionId;return e||(e=this._sessionId=function(){try{return crypto.randomUUID()}catch(i){try{var e=URL.createObjectURL(new Blob),t=e.toString();return URL.revokeObjectURL(e),t.slice(t.lastIndexOf("/")+1)}catch(e){var r=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=(r+16*Math.random())%16|0;return r=Math.floor(r/16),("x"==e?t:3&t|8).toString(16)}))}}}()),e}},{key:"levels",get:function(){var e=this.levelController.levels;return e||[]}},{key:"latestLevelDetails",get:function(){return this.streamController.getLevelDetails()||null}},{key:"loadLevelObj",get:function(){return this.levelController.loadLevelObj}},{key:"currentLevel",get:function(){return this.streamController.currentLevel},set:function(e){this.logger.log("set currentLevel:"+e),this.levelController.manualLevel=e,this.streamController.immediateLevelSwitch()}},{key:"nextLevel",get:function(){return this.streamController.nextLevel},set:function(e){this.logger.log("set nextLevel:"+e),this.levelController.manualLevel=e,this.streamController.nextLevelSwitch()}},{key:"loadLevel",get:function(){return this.levelController.level},set:function(e){this.logger.log("set loadLevel:"+e),this.levelController.manualLevel=e}},{key:"nextLoadLevel",get:function(){return this.levelController.nextLoadLevel},set:function(e){this.levelController.nextLoadLevel=e}},{key:"firstLevel",get:function(){return Math.max(this.levelController.firstLevel,this.minAutoLevel)},set:function(e){this.logger.log("set firstLevel:"+e),this.levelController.firstLevel=e}},{key:"startLevel",get:function(){var e=this.levelController.startLevel;return-1===e&&this.abrController.forcedAutoLevel>-1?this.abrController.forcedAutoLevel:e},set:function(e){this.logger.log("set startLevel:"+e),-1!==e&&(e=Math.max(e,this.minAutoLevel)),this.levelController.startLevel=e}},{key:"capLevelToPlayerSize",get:function(){return this.config.capLevelToPlayerSize},set:function(e){var t=!!e;t!==this.config.capLevelToPlayerSize&&(t?this.capLevelController.startCapping():(this.capLevelController.stopCapping(),this.autoLevelCapping=-1,this.streamController.nextLevelSwitch()),this.config.capLevelToPlayerSize=t)}},{key:"autoLevelCapping",get:function(){return this._autoLevelCapping},set:function(e){this._autoLevelCapping!==e&&(this.logger.log("set autoLevelCapping:"+e),this._autoLevelCapping=e,this.levelController.checkMaxAutoUpdated())}},{key:"bandwidthEstimate",get:function(){var e=this.abrController.bwEstimator;return e?e.getEstimate():NaN},set:function(e){this.abrController.resetEstimator(e)}},{key:"abrEwmaDefaultEstimate",get:function(){var e=this.abrController.bwEstimator;return e?e.defaultEstimate:NaN}},{key:"ttfbEstimate",get:function(){var e=this.abrController.bwEstimator;return e?e.getEstimateTTFB():NaN}},{key:"maxHdcpLevel",get:function(){return this._maxHdcpLevel},set:function(e){(function(e){return qe.indexOf(e)>-1})(e)&&this._maxHdcpLevel!==e&&(this._maxHdcpLevel=e,this.levelController.checkMaxAutoUpdated())}},{key:"autoLevelEnabled",get:function(){return-1===this.levelController.manualLevel}},{key:"manualLevel",get:function(){return this.levelController.manualLevel}},{key:"minAutoLevel",get:function(){var e=this.levels,t=this.config.minAutoBitrate;if(!e)return 0;for(var r=e.length,i=0;i<r;i++)if(e[i].maxBitrate>=t)return i;return 0}},{key:"maxAutoLevel",get:function(){var e,t=this.levels,r=this.autoLevelCapping,i=this.maxHdcpLevel;if(e=-1===r&&null!=t&&t.length?t.length-1:r,i)for(var a=e;a--;){var n=t[a].attrs["HDCP-LEVEL"];if(n&&n<=i)return a}return e}},{key:"firstAutoLevel",get:function(){return this.abrController.firstAutoLevel}},{key:"nextAutoLevel",get:function(){return this.abrController.nextAutoLevel},set:function(e){this.abrController.nextAutoLevel=e}},{key:"playingDate",get:function(){return this.streamController.currentProgramDateTime}},{key:"mainForwardBufferInfo",get:function(){return this.streamController.getMainFwdBufferInfo()}},{key:"maxBufferLength",get:function(){return this.streamController.maxBufferLength}},{key:"allAudioTracks",get:function(){var e=this.audioTrackController;return e?e.allAudioTracks:[]}},{key:"audioTracks",get:function(){var e=this.audioTrackController;return e?e.audioTracks:[]}},{key:"audioTrack",get:function(){var e=this.audioTrackController;return e?e.audioTrack:-1},set:function(e){var t=this.audioTrackController;t&&(t.audioTrack=e)}},{key:"allSubtitleTracks",get:function(){var e=this.subtitleTrackController;return e?e.allSubtitleTracks:[]}},{key:"subtitleTracks",get:function(){var e=this.subtitleTrackController;return e?e.subtitleTracks:[]}},{key:"subtitleTrack",get:function(){var e=this.subtitleTrackController;return e?e.subtitleTrack:-1},set:function(e){var t=this.subtitleTrackController;t&&(t.subtitleTrack=e)}},{key:"media",get:function(){return this._media}},{key:"subtitleDisplay",get:function(){var e=this.subtitleTrackController;return!!e&&e.subtitleDisplay},set:function(e){var t=this.subtitleTrackController;t&&(t.subtitleDisplay=e)}},{key:"lowLatencyMode",get:function(){return this.config.lowLatencyMode},set:function(e){this.config.lowLatencyMode=e}},{key:"liveSyncPosition",get:function(){return this.latencyController.liveSyncPosition}},{key:"latency",get:function(){return this.latencyController.latency}},{key:"maxLatency",get:function(){return this.latencyController.maxLatency}},{key:"targetLatency",get:function(){return this.latencyController.targetLatency},set:function(e){this.latencyController.targetLatency=e}},{key:"drift",get:function(){return this.latencyController.drift}},{key:"forceStartLoad",get:function(){return this.streamController.forceStartLoad}},{key:"pathways",get:function(){return this.levelController.pathways}},{key:"pathwayPriority",get:function(){return this.levelController.pathwayPriority},set:function(e){this.levelController.pathwayPriority=e}},{key:"bufferedToEnd",get:function(){var e;return!(null==(e=this.bufferController)||!e.bufferedToEnd)}},{key:"interstitialsManager",get:function(){var e;return(null==(e=this.interstitialsController)?void 0:e.interstitialsManager)||null}}],[{key:"version",get:function(){return za}},{key:"Events",get:function(){return D}},{key:"MetadataSchema",get:function(){return Xr}},{key:"ErrorTypes",get:function(){return b}},{key:"ErrorDetails",get:function(){return k}},{key:"DefaultConfig",get:function(){return e.defaultConfig?e.defaultConfig:ia},set:function(t){e.defaultConfig=t}}])}();return hn.defaultConfig=void 0,hn},"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(r="undefined"!=typeof globalThis?globalThis:r||self).Hls=i()}(!1);
//# sourceMappingURL=hls.light.min.js.map
