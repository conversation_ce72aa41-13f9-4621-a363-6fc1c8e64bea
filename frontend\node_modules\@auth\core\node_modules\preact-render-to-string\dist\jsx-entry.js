var t;t=function(t,e){if("function"!=typeof Symbol){var n=0;Symbol=function(t){return"@@"+t+ ++n},Symbol.for=function(t){return"@@"+t}}var r=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,o=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,i=/[\s\n\\/='"\0<>]/,a=/^xlink:?./,s=/["&<]/;function f(t){if(!1===s.test(t+=""))return t;for(var e=0,n=0,r="",o="";n<t.length;n++){switch(t.charCodeAt(n)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 60:o="&lt;";break;default:continue}n!==e&&(r+=t.slice(e,n)),r+=o,e=n+1}return n!==e&&(r+=t.slice(e,n)),r}var u=function(t,e){return String(t).replace(/(\n+)/g,"$1"+(e||"\t"))},l=function(t,e,n){return String(t).length>(e||40)||!n&&-1!==String(t).indexOf("\n")||-1!==String(t).indexOf("<")},c={},p=/([A-Z])/g;function d(t){var e="";for(var n in t){var o=t[n];null!=o&&""!==o&&(e&&(e+=" "),e+="-"==n[0]?n:c[n]||(c[n]=n.replace(p,"-$1").toLowerCase()),e="number"==typeof o&&!1===r.test(n)?e+": "+o+"px;":e+": "+o+";")}return e||void 0}function v(t,e){return Array.isArray(e)?e.reduce(v,t):null!=e&&!1!==e&&t.push(e),t}function y(){this.__d=!0}function _(t,e){return{__v:t,context:e,props:t.props,setState:y,forceUpdate:y,__d:!0,__h:[]}}function g(t,e){var n=t.contextType,r=n&&e[n.__c];return null!=n?r?r.props.value:n.__:e}var b=[];function m(t,n,r,s,c,p){if(null==t||"boolean"==typeof t)return"";if("object"!=typeof t)return f(t);var y=r.pretty,h=y&&"string"==typeof y?y:"\t";if(Array.isArray(t)){for(var j="",x=0;x<t.length;x++)y&&x>0&&(j+="\n"),j+=m(t[x],n,r,s,c,p);return j}var S,k=t.type,A=t.props,O=!1;if("function"==typeof k){if(O=!0,!r.shallow||!s&&!1!==r.renderRootComponent){if(k===e.Fragment){var w=[];return v(w,t.props.children),m(w,n,r,!1!==r.shallowHighOrder,c,p)}var F,C=t.__c=_(t,n);e.options.__b&&e.options.__b(t);var E=e.options.__r;if(k.prototype&&"function"==typeof k.prototype.render){var M=g(k,n);(C=t.__c=new k(A,M)).__v=t,C._dirty=C.__d=!0,C.props=A,null==C.state&&(C.state={}),null==C._nextState&&null==C.__s&&(C._nextState=C.__s=C.state),C.context=M,k.getDerivedStateFromProps?C.state=Object.assign({},C.state,k.getDerivedStateFromProps(C.props,C.state)):C.componentWillMount&&(C.componentWillMount(),C.state=C._nextState!==C.state?C._nextState:C.__s!==C.state?C.__s:C.state),E&&E(t),F=C.render(C.props,C.state,C.context)}else for(var H=g(k,n),N=0;C.__d&&N++<25;)C.__d=!1,E&&E(t),F=k.call(t.__c,A,H);return C.getChildContext&&(n=Object.assign({},n,C.getChildContext())),e.options.diffed&&e.options.diffed(t),m(F,n,r,!1!==r.shallowHighOrder,c,p)}k=(S=k).displayName||S!==Function&&S.name||function(t){var e=(Function.prototype.toString.call(t).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!e){for(var n=-1,r=b.length;r--;)if(b[r]===t){n=r;break}n<0&&(n=b.push(t)-1),e="UnnamedComponent"+n}return e}(S)}var D,I,L="<"+k;if(A){var W=Object.keys(A);r&&!0===r.sortAttributes&&W.sort();for(var $=0;$<W.length;$++){var P=W[$],T=A[P];if("children"!==P){if(!i.test(P)&&(r&&r.allAttributes||"key"!==P&&"ref"!==P&&"__self"!==P&&"__source"!==P)){if("defaultValue"===P)P="value";else if("defaultChecked"===P)P="checked";else if("defaultSelected"===P)P="selected";else if("className"===P){if(void 0!==A.class)continue;P="class"}else c&&a.test(P)&&(P=P.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===P){if(A.for)continue;P="for"}"style"===P&&T&&"object"==typeof T&&(T=d(T)),"a"===P[0]&&"r"===P[1]&&"boolean"==typeof T&&(T=String(T));var U=r.attributeHook&&r.attributeHook(P,T,n,r,O);if(U||""===U)L+=U;else if("dangerouslySetInnerHTML"===P)I=T&&T.__html;else if("textarea"===k&&"value"===P)D=T;else if((T||0===T||""===T)&&"function"!=typeof T){if(!(!0!==T&&""!==T||(T=P,r&&r.xml))){L=L+" "+P;continue}if("value"===P){if("select"===k){p=T;continue}"option"===k&&p==T&&void 0===A.selected&&(L+=" selected")}L=L+" "+P+'="'+f(T)+'"'}}}else D=T}}if(y){var R=L.replace(/\n\s*/," ");R===L||~R.indexOf("\n")?y&&~L.indexOf("\n")&&(L+="\n"):L=R}if(L+=">",i.test(k))throw new Error(k+" is not a valid HTML tag name in "+L);var J,V=o.test(k)||r.voidElements&&r.voidElements.test(k),q=[];if(I)y&&l(I)&&(I="\n"+h+u(I,h)),L+=I;else if(null!=D&&v(J=[],D).length){for(var z=y&&~L.indexOf("\n"),B=!1,G=0;G<J.length;G++){var Z=J[G];if(null!=Z&&!1!==Z){var K=m(Z,n,r,!0,"svg"===k||"foreignObject"!==k&&c,p);if(y&&!z&&l(K)&&(z=!0),K)if(y){var Q=K.length>0&&"<"!=K[0];B&&Q?q[q.length-1]+=K:q.push(K),B=Q}else q.push(K)}}if(y&&z)for(var X=q.length;X--;)q[X]="\n"+h+u(q[X],h)}if(q.length||I)L+=q.join("");else if(r&&r.xml)return L.substring(0,L.length-1)+" />";return!V||J||I?(y&&~L.indexOf("\n")&&(L+="\n"),L=L+"</"+k+">"):L=L.replace(/>$/," />"),L}var h={shallow:!0};x.render=x;var j=[];function x(t,n,r){n=n||{};var o,i=e.options.__s;return e.options.__s=!0,o=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?m(t,n,r):w(t,n,!1,void 0),e.options.__c&&e.options.__c(t,j),e.options.__s=i,j.length=0,o}function S(t,e){return"className"===t?"class":"htmlFor"===t?"for":"defaultValue"===t?"value":"defaultChecked"===t?"checked":"defaultSelected"===t?"selected":e&&a.test(t)?t.toLowerCase().replace(/^xlink:?/,"xlink:"):t}function k(t,e){return"style"===t&&null!=e&&"object"==typeof e?d(e):"a"===t[0]&&"r"===t[1]&&"boolean"==typeof e?String(e):e}var A=Array.isArray,O=Object.assign;function w(t,n,r,a){if(null==t||!0===t||!1===t||""===t)return"";if("object"!=typeof t)return f(t);if(A(t)){for(var s="",u=0;u<t.length;u++)s+=w(t[u],n,r,a);return s}e.options.__b&&e.options.__b(t);var l=t.type,c=t.props;if("function"==typeof l){if(l===e.Fragment)return w(t.props.children,n,r,a);var p;p=l.prototype&&"function"==typeof l.prototype.render?function(t,n){var r=t.type,o=g(r,n),i=new r(t.props,o);t.__c=i,i.__v=t,i.__d=!0,i.props=t.props,null==i.state&&(i.state={}),null==i.__s&&(i.__s=i.state),i.context=o,r.getDerivedStateFromProps?i.state=O({},i.state,r.getDerivedStateFromProps(i.props,i.state)):i.componentWillMount&&(i.componentWillMount(),i.state=i.__s!==i.state?i.__s:i.state);var a=e.options.__r;return a&&a(t),i.render(i.props,i.state,i.context)}(t,n):function(t,n){var r,o=_(t,n),i=g(t.type,n);t.__c=o;for(var a=e.options.__r,s=0;o.__d&&s++<25;)o.__d=!1,a&&a(t),r=t.type.call(o,t.props,i);return r}(t,n);var d=t.__c;d.getChildContext&&(n=O({},n,d.getChildContext()));var v=w(p,n,r,a);return e.options.diffed&&e.options.diffed(t),v}var y,b,m="<";if(m+=l,c)for(var h in y=c.children,c){var j=c[h];if(!("key"===h||"ref"===h||"__self"===h||"__source"===h||"children"===h||"className"===h&&"class"in c||"htmlFor"===h&&"for"in c||i.test(h)))if(j=k(h=S(h,r),j),"dangerouslySetInnerHTML"===h)b=j&&j.__html;else if("textarea"===l&&"value"===h)y=j;else if((j||0===j||""===j)&&"function"!=typeof j){if(!0===j||""===j){j=h,m=m+" "+h;continue}if("value"===h){if("select"===l){a=j;continue}"option"!==l||a!=j||"selected"in c||(m+=" selected")}m=m+" "+h+'="'+f(j)+'"'}}var x=m;if(m+=">",i.test(l))throw new Error(l+" is not a valid HTML tag name in "+m);var F="",C=!1;if(b)F+=b,C=!0;else if("string"==typeof y)F+=f(y),C=!0;else if(A(y))for(var E=0;E<y.length;E++){var M=y[E];if(null!=M&&!1!==M){var H=w(M,n,"svg"===l||"foreignObject"!==l&&r,a);H&&(F+=H,C=!0)}}else if(null!=y&&!1!==y&&!0!==y){var N=w(y,n,"svg"===l||"foreignObject"!==l&&r,a);N&&(F+=N,C=!0)}if(e.options.diffed&&e.options.diffed(t),C)m+=F;else if(o.test(l))return x+" />";return m+"</"+l+">"}x.shallowRender=function(t,e){return x(t,e,h)};var F=/(\\|\"|\')/g,C=Object.prototype.toString,E=Date.prototype.toISOString,M=Error.prototype.toString,H=RegExp.prototype.toString,N=Symbol.prototype.toString,D=/^Symbol\((.*)\)(.*)$/,I=/\n/gi,L=Object.getOwnPropertySymbols||function(t){return[]};function W(t){return"[object Array]"===t||"[object ArrayBuffer]"===t||"[object DataView]"===t||"[object Float32Array]"===t||"[object Float64Array]"===t||"[object Int8Array]"===t||"[object Int16Array]"===t||"[object Int32Array]"===t||"[object Uint8Array]"===t||"[object Uint8ClampedArray]"===t||"[object Uint16Array]"===t||"[object Uint32Array]"===t}function $(t){return""===t.name?"[Function anonymous]":"[Function "+t.name+"]"}function P(t){return N.call(t).replace(D,"Symbol($1)")}function T(t){return"["+M.call(t)+"]"}function U(t){if(!0===t||!1===t)return""+t;if(void 0===t)return"undefined";if(null===t)return"null";var e=typeof t;if("number"===e)return function(t){return t!=+t?"NaN":0===t&&1/t<0?"-0":""+t}(t);if("string"===e)return'"'+function(t){return t.replace(F,"\\$1")}(t)+'"';if("function"===e)return $(t);if("symbol"===e)return P(t);var n=C.call(t);return"[object WeakMap]"===n?"WeakMap {}":"[object WeakSet]"===n?"WeakSet {}":"[object Function]"===n||"[object GeneratorFunction]"===n?$(t,min):"[object Symbol]"===n?P(t):"[object Date]"===n?E.call(t):"[object Error]"===n?T(t):"[object RegExp]"===n?H.call(t):"[object Arguments]"===n&&0===t.length?"Arguments []":W(n)&&0===t.length?t.constructor.name+" []":t instanceof Error&&T(t)}function R(t,e,n,r,o,i,a,s,f,u){var l="";if(t.length){l+=o;for(var c=n+e,p=0;p<t.length;p++)l+=c+q(t[p],e,c,r,o,i,a,s,f,u),p<t.length-1&&(l+=","+r);l+=o+n}return"["+l+"]"}function J(t,e,n,r,o,i,a,s,f,u){if((i=i.slice()).indexOf(t)>-1)return"[Circular]";i.push(t);var l=++s>a;if(!l&&t.toJSON&&"function"==typeof t.toJSON)return q(t.toJSON(),e,n,r,o,i,a,s,f,u);var c=C.call(t);return"[object Arguments]"===c?l?"[Arguments]":function(t,e,n,r,o,i,a,s,f,u){return(u?"":"Arguments ")+R(t,e,n,r,o,i,a,s,f,u)}(t,e,n,r,o,i,a,s,f,u):W(c)?l?"[Array]":function(t,e,n,r,o,i,a,s,f,u){return(u?"":t.constructor.name+" ")+R(t,e,n,r,o,i,a,s,f,u)}(t,e,n,r,o,i,a,s,f,u):"[object Map]"===c?l?"[Map]":function(t,e,n,r,o,i,a,s,f,u){var l="Map {",c=t.entries(),p=c.next();if(!p.done){l+=o;for(var d=n+e;!p.done;)l+=d+q(p.value[0],e,d,r,o,i,a,s,f,u)+" => "+q(p.value[1],e,d,r,o,i,a,s,f,u),(p=c.next()).done||(l+=","+r);l+=o+n}return l+"}"}(t,e,n,r,o,i,a,s,f,u):"[object Set]"===c?l?"[Set]":function(t,e,n,r,o,i,a,s,f,u){var l="Set {",c=t.entries(),p=c.next();if(!p.done){l+=o;for(var d=n+e;!p.done;)l+=d+q(p.value[1],e,d,r,o,i,a,s,f,u),(p=c.next()).done||(l+=","+r);l+=o+n}return l+"}"}(t,e,n,r,o,i,a,s,f,u):"object"==typeof t?l?"[Object]":function(t,e,n,r,o,i,a,s,f,u){var l=(u?"":t.constructor?t.constructor.name+" ":"Object ")+"{",c=Object.keys(t).sort(),p=L(t);if(p.length&&(c=c.filter(function(t){return!("symbol"==typeof t||"[object Symbol]"===C.call(t))}).concat(p)),c.length){l+=o;for(var d=n+e,v=0;v<c.length;v++){var y=c[v];l+=d+q(y,e,d,r,o,i,a,s,f,u)+": "+q(t[y],e,d,r,o,i,a,s,f,u),v<c.length-1&&(l+=","+r)}l+=o+n}return l+"}"}(t,e,n,r,o,i,a,s,f,u):void 0}function V(t,e,n,r,o,i,a,s,f,u){for(var l,c=!1,p=0;p<f.length;p++)if((l=f[p]).test(t)){c=!0;break}return!!c&&l.print(t,function(t){return q(t,e,n,r,o,i,a,s,f,u)},function(t){var r=n+e;return r+t.replace(I,"\n"+r)},{edgeSpacing:o,spacing:r})}function q(t,e,n,r,o,i,a,s,f,u){return U(t)||V(t,e,n,r,o,i,a,s,f,u)||J(t,e,n,r,o,i,a,s,f,u)}var z={indent:2,min:!1,maxDepth:Infinity,plugins:[]};function B(t){return new Array(t+1).join(" ")}var G={test:function(t){return t&&"object"==typeof t&&"type"in t&&"props"in t&&"key"in t},print:function(t,e,n){return x(t,G.context,G.opts)}},Z={plugins:[G]},K={attributeHook:function(t,e,n,r,o){var i=typeof e;if("dangerouslySetInnerHTML"===t)return!1;if(null==e||"function"===i&&!r.functions)return"";if(r.skipFalseAttributes&&!o&&(!1===e||("class"===t||"style"===t)&&""===e))return"";var a="string"==typeof r.pretty?r.pretty:"\t";return"string"!==i?("function"!==i||r.functionNames?(G.context=n,G.opts=r,~(e=function(t,e){var n,r;e?(function(t){if(Object.keys(t).forEach(function(t){if(!z.hasOwnProperty(t))throw new Error("prettyFormat: Invalid option: "+t)}),t.min&&void 0!==t.indent&&0!==t.indent)throw new Error("prettyFormat: Cannot run with min option and indent")}(e),e=function(t){var e={};return Object.keys(z).forEach(function(n){return e[n]=t.hasOwnProperty(n)?t[n]:z[n]}),e.min&&(e.indent=0),e}(e)):e=z;var o=e.min?" ":"\n",i=e.min?"":"\n";if(e&&e.plugins.length){var a=V(t,n=B(e.indent),"",o,i,r=[],e.maxDepth,0,e.plugins,e.min);if(a)return a}return U(t)||(n||(n=B(e.indent)),r||(r=[]),J(t,n,"",o,i,r,e.maxDepth,0,e.plugins,e.min))}(e,Z)).indexOf("\n")&&(e=u("\n"+e,a)+"\n")):e="Function",u("\n"+t+"={"+e+"}",a)):"\n"+a+t+'="'+f(e)+'"'},jsx:!0,xml:!1,functions:!0,functionNames:!0,skipFalseAttributes:!0,pretty:"  "};function Q(t,e,n,r){return x(t,e,n=Object.assign({},K,n||{}))}t.default=Q,t.render=Q},"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("preact")):"function"==typeof define&&define.amd?define(["exports","preact"],t):t(self.preactRenderToString={},(void 0).preact);
//# sourceMappingURL=jsx.js.map
