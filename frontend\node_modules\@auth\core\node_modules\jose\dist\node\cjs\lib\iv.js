"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bitLength = bitLength;
const errors_js_1 = require("../util/errors.js");
const random_js_1 = require("../runtime/random.js");
function bitLength(alg) {
    switch (alg) {
        case 'A128GCM':
        case 'A128GCMKW':
        case 'A192GCM':
        case 'A192GCMKW':
        case 'A256GCM':
        case 'A256GCMKW':
            return 96;
        case 'A128CBC-HS256':
        case 'A192CBC-HS384':
        case 'A256CBC-HS512':
            return 128;
        default:
            throw new errors_js_1.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);
    }
}
exports.default = (alg) => (0, random_js_1.default)(new Uint8Array(bitLength(alg) >> 3));
