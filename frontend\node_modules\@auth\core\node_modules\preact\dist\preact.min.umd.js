!function(n){"function"==typeof define&&define.amd?define(n):n()}(function(){var n,l,u,t,i,o,r,e,f={},c=[],a=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function s(n,l){for(var u in l)n[u]=l[u];return n}function h(n){var l=n.parentNode;l&&l.removeChild(n)}function v(l,u,t){var i,o,r,e={};for(r in u)"key"==r?i=u[r]:"ref"==r?o=u[r]:e[r]=u[r];if(arguments.length>2&&(e.children=arguments.length>3?n.call(arguments,2):t),"function"==typeof l&&null!=l.defaultProps)for(r in l.defaultProps)void 0===e[r]&&(e[r]=l.defaultProps[r]);return y(l,e,i,o,null)}function y(n,t,i,o,r){var e={type:n,props:t,key:i,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==r?++u:r};return null==r&&null!=l.vnode&&l.vnode(e),e}function d(n){return n.children}function p(n,l){this.props=n,this.context=l}function _(n,l){if(null==l)return n.__?_(n.__,n.__.__k.indexOf(n)+1):null;for(var u;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e)return u.__e;return"function"==typeof n.type?_(n):null}function m(n){var l,u;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e){n.__e=n.__c.base=u.__e;break}return m(n)}}function k(n){(!n.__d&&(n.__d=!0)&&i.push(n)&&!b.__r++||o!==l.debounceRendering)&&((o=l.debounceRendering)||setTimeout)(b)}function b(){for(var n;b.__r=i.length;)n=i.sort(function(n,l){return n.__v.__b-l.__v.__b}),i=[],n.some(function(n){var l,u,t,i,o,r;n.__d&&(o=(i=(l=n).__v).__e,(r=l.__P)&&(u=[],(t=s({},i)).__v=i.__v+1,I(r,i,t,l.__n,void 0!==r.ownerSVGElement,null!=i.__h?[o]:null,u,null==o?_(i):o,i.__h),T(u,i),i.__e!=o&&m(i)))})}function g(n,l,u,t,i,o,r,e,a,s){var h,v,p,m,k,b,g,C=t&&t.__k||c,x=C.length;for(u.__k=[],h=0;h<l.length;h++)if(null!=(m=u.__k[h]=null==(m=l[h])||"boolean"==typeof m?null:"string"==typeof m||"number"==typeof m||"bigint"==typeof m?y(null,m,null,null,m):Array.isArray(m)?y(d,{children:m},null,null,null):m.__b>0?y(m.type,m.props,m.key,m.ref?m.ref:null,m.__v):m)){if(m.__=u,m.__b=u.__b+1,null===(p=C[h])||p&&m.key==p.key&&m.type===p.type)C[h]=void 0;else for(v=0;v<x;v++){if((p=C[v])&&m.key==p.key&&m.type===p.type){C[v]=void 0;break}p=null}I(n,m,p=p||f,i,o,r,e,a,s),k=m.__e,(v=m.ref)&&p.ref!=v&&(g||(g=[]),p.ref&&g.push(p.ref,null,m),g.push(v,m.__c||k,m)),null!=k?(null==b&&(b=k),"function"==typeof m.type&&m.__k===p.__k?m.__d=a=w(m,a,n):a=A(n,m,p,C,k,a),"function"==typeof u.type&&(u.__d=a)):a&&p.__e==a&&a.parentNode!=n&&(a=_(p))}for(u.__e=b,h=x;h--;)null!=C[h]&&F(C[h],C[h]);if(g)for(h=0;h<g.length;h++)z(g[h],g[++h],g[++h])}function w(n,l,u){for(var t,i=n.__k,o=0;i&&o<i.length;o++)(t=i[o])&&(t.__=n,l="function"==typeof t.type?w(t,l,u):A(u,t,t,i,t.__e,l));return l}function A(n,l,u,t,i,o){var r,e,f;if(void 0!==l.__d)r=l.__d,l.__d=void 0;else if(null==u||i!=o||null==i.parentNode)n:if(null==o||o.parentNode!==n)n.appendChild(i),r=null;else{for(e=o,f=0;(e=e.nextSibling)&&f<t.length;f+=1)if(e==i)break n;n.insertBefore(i,o),r=o}return void 0!==r?r:i.nextSibling}function C(n,l,u,t,i){var o;for(o in u)"children"===o||"key"===o||o in l||E(n,o,null,u[o],t);for(o in l)i&&"function"!=typeof l[o]||"children"===o||"key"===o||"value"===o||"checked"===o||u[o]===l[o]||E(n,o,l[o],u[o],t)}function x(n,l,u){"-"===l[0]?n.setProperty(l,u):n[l]=null==u?"":"number"!=typeof u||a.test(l)?u:u+"px"}function E(n,l,u,t,i){var o;n:if("style"===l)if("string"==typeof u)n.style.cssText=u;else{if("string"==typeof t&&(n.style.cssText=t=""),t)for(l in t)u&&l in u||x(n.style,l,"");if(u)for(l in u)t&&u[l]===t[l]||x(n.style,l,u[l])}else if("o"===l[0]&&"n"===l[1])o=l!==(l=l.replace(/Capture$/,"")),l=l.toLowerCase()in n?l.toLowerCase().slice(2):l.slice(2),n.l||(n.l={}),n.l[l+o]=u,u?t||n.addEventListener(l,o?H:$,o):n.removeEventListener(l,o?H:$,o);else if("dangerouslySetInnerHTML"!==l){if(i)l=l.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==l&&"list"!==l&&"form"!==l&&"tabIndex"!==l&&"download"!==l&&l in n)try{n[l]=null==u?"":u;break n}catch(n){}"function"==typeof u||(null==u||!1===u&&-1==l.indexOf("-")?n.removeAttribute(l):n.setAttribute(l,u))}}function $(n){this.l[n.type+!1](l.event?l.event(n):n)}function H(n){this.l[n.type+!0](l.event?l.event(n):n)}function I(n,u,t,i,o,r,e,f,c){var a,h,v,y,_,m,k,b,w,A,C,x,E,$,H,I=u.type;if(void 0!==u.constructor)return null;null!=t.__h&&(c=t.__h,f=u.__e=t.__e,u.__h=null,r=[f]),(a=l.__b)&&a(u);try{n:if("function"==typeof I){if(b=u.props,w=(a=I.contextType)&&i[a.__c],A=a?w?w.props.value:a.__:i,t.__c?k=(h=u.__c=t.__c).__=h.__E:("prototype"in I&&I.prototype.render?u.__c=h=new I(b,A):(u.__c=h=new p(b,A),h.constructor=I,h.render=L),w&&w.sub(h),h.props=b,h.state||(h.state={}),h.context=A,h.__n=i,v=h.__d=!0,h.__h=[],h._sb=[]),null==h.__s&&(h.__s=h.state),null!=I.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=s({},h.__s)),s(h.__s,I.getDerivedStateFromProps(b,h.__s))),y=h.props,_=h.state,v)null==I.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(null==I.getDerivedStateFromProps&&b!==y&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(b,A),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(b,h.__s,A)||u.__v===t.__v){for(h.props=b,h.state=h.__s,u.__v!==t.__v&&(h.__d=!1),h.__v=u,u.__e=t.__e,u.__k=t.__k,u.__k.forEach(function(n){n&&(n.__=u)}),C=0;C<h._sb.length;C++)h.__h.push(h._sb[C]);h._sb=[],h.__h.length&&e.push(h);break n}null!=h.componentWillUpdate&&h.componentWillUpdate(b,h.__s,A),null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(y,_,m)})}if(h.context=A,h.props=b,h.__v=u,h.__P=n,x=l.__r,E=0,"prototype"in I&&I.prototype.render){for(h.state=h.__s,h.__d=!1,x&&x(u),a=h.render(h.props,h.state,h.context),$=0;$<h._sb.length;$++)h.__h.push(h._sb[$]);h._sb=[]}else do{h.__d=!1,x&&x(u),a=h.render(h.props,h.state,h.context),h.state=h.__s}while(h.__d&&++E<25);h.state=h.__s,null!=h.getChildContext&&(i=s(s({},i),h.getChildContext())),v||null==h.getSnapshotBeforeUpdate||(m=h.getSnapshotBeforeUpdate(y,_)),H=null!=a&&a.type===d&&null==a.key?a.props.children:a,g(n,Array.isArray(H)?H:[H],u,t,i,o,r,e,f,c),h.base=u.__e,u.__h=null,h.__h.length&&e.push(h),k&&(h.__E=h.__=null),h.__e=!1}else null==r&&u.__v===t.__v?(u.__k=t.__k,u.__e=t.__e):u.__e=j(t.__e,u,t,i,o,r,e,c);(a=l.diffed)&&a(u)}catch(n){u.__v=null,(c||null!=r)&&(u.__e=f,u.__h=!!c,r[r.indexOf(f)]=null),l.__e(n,u,t)}}function T(n,u){l.__c&&l.__c(u,n),n.some(function(u){try{n=u.__h,u.__h=[],n.some(function(n){n.call(u)})}catch(n){l.__e(n,u.__v)}})}function j(l,u,t,i,o,r,e,c){var a,s,v,y=t.props,d=u.props,p=u.type,m=0;if("svg"===p&&(o=!0),null!=r)for(;m<r.length;m++)if((a=r[m])&&"setAttribute"in a==!!p&&(p?a.localName===p:3===a.nodeType)){l=a,r[m]=null;break}if(null==l){if(null===p)return document.createTextNode(d);l=o?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,d.is&&d),r=null,c=!1}if(null===p)y===d||c&&l.data===d||(l.data=d);else{if(r=r&&n.call(l.childNodes),s=(y=t.props||f).dangerouslySetInnerHTML,v=d.dangerouslySetInnerHTML,!c){if(null!=r)for(y={},m=0;m<l.attributes.length;m++)y[l.attributes[m].name]=l.attributes[m].value;(v||s)&&(v&&(s&&v.__html==s.__html||v.__html===l.innerHTML)||(l.innerHTML=v&&v.__html||""))}if(C(l,d,y,o,c),v)u.__k=[];else if(m=u.props.children,g(l,Array.isArray(m)?m:[m],u,t,i,o&&"foreignObject"!==p,r,e,r?r[0]:t.__k&&_(t,0),c),null!=r)for(m=r.length;m--;)null!=r[m]&&h(r[m]);c||("value"in d&&void 0!==(m=d.value)&&(m!==l.value||"progress"===p&&!m||"option"===p&&m!==y.value)&&E(l,"value",m,y.value,!1),"checked"in d&&void 0!==(m=d.checked)&&m!==l.checked&&E(l,"checked",m,y.checked,!1))}return l}function z(n,u,t){try{"function"==typeof n?n(u):n.current=u}catch(n){l.__e(n,t)}}function F(n,u,t){var i,o;if(l.unmount&&l.unmount(n),(i=n.ref)&&(i.current&&i.current!==n.__e||z(i,null,u)),null!=(i=n.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(n){l.__e(n,u)}i.base=i.__P=null,n.__c=void 0}if(i=n.__k)for(o=0;o<i.length;o++)i[o]&&F(i[o],u,t||"function"!=typeof n.type);t||null==n.__e||h(n.__e),n.__=n.__e=n.__d=void 0}function L(n,l,u){return this.constructor(n,u)}function M(u,t,i){var o,r,e;l.__&&l.__(u,t),r=(o="function"==typeof i)?null:i&&i.__k||t.__k,e=[],I(t,u=(!o&&i||t).__k=v(d,null,[u]),r||f,f,void 0!==t.ownerSVGElement,!o&&i?[i]:r?null:t.firstChild?n.call(t.childNodes):null,e,!o&&i?i:r?r.__e:t.firstChild,o),T(e,u)}n=c.slice,l={__e:function(n,l,u,t){for(var i,o,r;l=l.__;)if((i=l.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(n)),r=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(n,t||{}),r=i.__d),r)return i.__E=i}catch(l){n=l}throw n}},u=0,t=function(n){return null!=n&&void 0===n.constructor},p.prototype.setState=function(n,l){var u;u=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=s({},this.state),"function"==typeof n&&(n=n(s({},u),this.props)),n&&s(u,n),null!=n&&this.__v&&(l&&this._sb.push(l),k(this))},p.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),k(this))},p.prototype.render=d,i=[],b.__r=0,r=0,e={__proto__:null,render:M,hydrate:function n(l,u){M(l,u,n)},createElement:v,h:v,Fragment:d,createRef:function(){return{current:null}},isValidElement:t,Component:p,cloneElement:function(l,u,t){var i,o,r,e=s({},l.props);for(r in u)"key"==r?i=u[r]:"ref"==r?o=u[r]:e[r]=u[r];return arguments.length>2&&(e.children=arguments.length>3?n.call(arguments,2):t),y(l.type,e,i||l.key,o||l.ref,null)},createContext:function(n,l){var u={__c:l="__cC"+r++,__:n,Consumer:function(n,l){return n.children(l)},Provider:function(n){var u,t;return this.getChildContext||(u=[],(t={})[l]=this,this.getChildContext=function(){return t},this.shouldComponentUpdate=function(n){this.props.value!==n.value&&u.some(k)},this.sub=function(n){u.push(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){u.splice(u.indexOf(n),1),l&&l.call(n)}}),n.children}};return u.Provider.__=u.Consumer.contextType=u},toChildArray:function n(l,u){return u=u||[],null==l||"boolean"==typeof l||(Array.isArray(l)?l.some(function(l){n(l,u)}):u.push(l)),u},options:l},typeof module<"u"?module.exports=e:self.preact=e});
//# sourceMappingURL=preact.min.umd.js.map
