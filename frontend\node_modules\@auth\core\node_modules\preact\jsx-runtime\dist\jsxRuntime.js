var r=require("preact"),e=0;function _(_,n,o,t,u){var l,f,i={};for(f in n)"ref"==f?l=n[f]:i[f]=n[f];var c={type:_,props:i,key:o,ref:l,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--e,__source:u,__self:t};if("function"==typeof _&&(l=_.defaultProps))for(f in l)void 0===i[f]&&(i[f]=l[f]);return r.options.vnode&&r.options.vnode(c),c}Object.defineProperty(exports,"Fragment",{enumerable:!0,get:function(){return r.Fragment}}),exports.jsx=_,exports.jsxDEV=_,exports.jsxs=_;
//# sourceMappingURL=jsxRuntime.js.map
