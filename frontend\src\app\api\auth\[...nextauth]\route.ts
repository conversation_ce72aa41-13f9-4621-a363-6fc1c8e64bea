// NextAuth configuration for the AI Tutor Platform

import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { User, UserRole } from '@/types';

// TODO: Replace with actual backend authentication
const authenticateUser = async (email: string, password: string): Promise<User | null> => {
  // Mock authentication - replace with actual API call
  const mockUsers: User[] = [
    {
      id: '1',
      name: 'John <PERSON>',
      email: '<EMAIL>',
      role: 'student',
      orgId: 'org1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'teacher',
      orgId: 'org1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '3',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'hod',
      orgId: 'org1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '4',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'admin',
      orgId: 'org1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  // Simple mock authentication
  const user = mockUsers.find(u => u.email === email);
  if (user && password === 'password') {
    return user;
  }

  return null;
};

const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const user = await authenticateUser(credentials.email, credentials.password);
          
          if (user) {
            return {
              id: user.id,
              name: user.name,
              email: user.email,
              role: user.role,
              orgId: user.orgId,
            };
          }
          
          return null;
        } catch (error) {
          console.error('Authentication error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.orgId = user.orgId;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as UserRole;
        session.user.orgId = token.orgId as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/sign-in',
  },
  session: {
    strategy: 'jwt' as const,
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key',
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
