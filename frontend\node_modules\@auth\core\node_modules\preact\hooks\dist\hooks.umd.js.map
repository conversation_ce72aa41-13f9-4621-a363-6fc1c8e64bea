{"version": 3, "file": "hooks.umd.js", "sources": ["../src/index.js"], "sourcesContent": ["import { options } from 'preact';\n\n/** @type {number} */\nlet currentIndex;\n\n/** @type {import('./internal').Component} */\nlet currentComponent;\n\n/** @type {import('./internal').Component} */\nlet previousComponent;\n\n/** @type {number} */\nlet currentHook = 0;\n\n/** @type {Array<import('./internal').Component>} */\nlet afterPaintEffects = [];\n\nlet EMPTY = [];\n\nlet oldBeforeDiff = options._diff;\nlet oldBeforeRender = options._render;\nlet oldAfterDiff = options.diffed;\nlet oldCommit = options._commit;\nlet oldBeforeUnmount = options.unmount;\n\nconst RAF_TIMEOUT = 100;\nlet prevRaf;\n\noptions._diff = vnode => {\n\tcurrentComponent = null;\n\tif (oldBeforeDiff) oldBeforeDiff(vnode);\n};\n\noptions._render = vnode => {\n\tif (oldBeforeRender) oldBeforeRender(vnode);\n\n\tcurrentComponent = vnode._component;\n\tcurrentIndex = 0;\n\n\tconst hooks = currentComponent.__hooks;\n\tif (hooks) {\n\t\tif (previousComponent === currentComponent) {\n\t\t\thooks._pendingEffects = [];\n\t\t\tcurrentComponent._renderCallbacks = [];\n\t\t\thooks._list.forEach(hookItem => {\n\t\t\t\tif (hookItem._nextValue) {\n\t\t\t\t\thookItem._value = hookItem._nextValue;\n\t\t\t\t}\n\t\t\t\thookItem._pendingValue = EMPTY;\n\t\t\t\thookItem._nextValue = hookItem._pendingArgs = undefined;\n\t\t\t});\n\t\t} else {\n\t\t\thooks._pendingEffects.forEach(invokeCleanup);\n\t\t\thooks._pendingEffects.forEach(invokeEffect);\n\t\t\thooks._pendingEffects = [];\n\t\t}\n\t}\n\tpreviousComponent = currentComponent;\n};\n\noptions.diffed = vnode => {\n\tif (oldAfterDiff) oldAfterDiff(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks) {\n\t\tif (c.__hooks._pendingEffects.length) afterPaint(afterPaintEffects.push(c));\n\t\tc.__hooks._list.forEach(hookItem => {\n\t\t\tif (hookItem._pendingArgs) {\n\t\t\t\thookItem._args = hookItem._pendingArgs;\n\t\t\t}\n\t\t\tif (hookItem._pendingValue !== EMPTY) {\n\t\t\t\thookItem._value = hookItem._pendingValue;\n\t\t\t}\n\t\t\thookItem._pendingArgs = undefined;\n\t\t\thookItem._pendingValue = EMPTY;\n\t\t});\n\t}\n\tpreviousComponent = currentComponent = null;\n};\n\noptions._commit = (vnode, commitQueue) => {\n\tcommitQueue.some(component => {\n\t\ttry {\n\t\t\tcomponent._renderCallbacks.forEach(invokeCleanup);\n\t\t\tcomponent._renderCallbacks = component._renderCallbacks.filter(cb =>\n\t\t\t\tcb._value ? invokeEffect(cb) : true\n\t\t\t);\n\t\t} catch (e) {\n\t\t\tcommitQueue.some(c => {\n\t\t\t\tif (c._renderCallbacks) c._renderCallbacks = [];\n\t\t\t});\n\t\t\tcommitQueue = [];\n\t\t\toptions._catchError(e, component._vnode);\n\t\t}\n\t});\n\n\tif (oldCommit) oldCommit(vnode, commitQueue);\n};\n\noptions.unmount = vnode => {\n\tif (oldBeforeUnmount) oldBeforeUnmount(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks) {\n\t\tlet hasErrored;\n\t\tc.__hooks._list.forEach(s => {\n\t\t\ttry {\n\t\t\t\tinvokeCleanup(s);\n\t\t\t} catch (e) {\n\t\t\t\thasErrored = e;\n\t\t\t}\n\t\t});\n\t\tc.__hooks = undefined;\n\t\tif (hasErrored) options._catchError(hasErrored, c._vnode);\n\t}\n};\n\n/**\n * Get a hook's state from the currentComponent\n * @param {number} index The index of the hook to get\n * @param {number} type The index of the hook to get\n * @returns {any}\n */\nfunction getHookState(index, type) {\n\tif (options._hook) {\n\t\toptions._hook(currentComponent, index, currentHook || type);\n\t}\n\tcurrentHook = 0;\n\n\t// Largely inspired by:\n\t// * https://github.com/michael-klein/funcy.js/blob/f6be73468e6ec46b0ff5aa3cc4c9baf72a29025a/src/hooks/core_hooks.mjs\n\t// * https://github.com/michael-klein/funcy.js/blob/650beaa58c43c33a74820a3c98b3c7079cf2e333/src/renderer.mjs\n\t// Other implementations to look at:\n\t// * https://codesandbox.io/s/mnox05qp8\n\tconst hooks =\n\t\tcurrentComponent.__hooks ||\n\t\t(currentComponent.__hooks = {\n\t\t\t_list: [],\n\t\t\t_pendingEffects: []\n\t\t});\n\n\tif (index >= hooks._list.length) {\n\t\thooks._list.push({ _pendingValue: EMPTY });\n\t}\n\treturn hooks._list[index];\n}\n\n/**\n * @param {import('./index').StateUpdater<any>} [initialState]\n */\nexport function useState(initialState) {\n\tcurrentHook = 1;\n\treturn useReducer(invokeOrReturn, initialState);\n}\n\n/**\n * @param {import('./index').Reducer<any, any>} reducer\n * @param {import('./index').StateUpdater<any>} initialState\n * @param {(initialState: any) => void} [init]\n * @returns {[ any, (state: any) => void ]}\n */\nexport function useReducer(reducer, initialState, init) {\n\t/** @type {import('./internal').ReducerHookState} */\n\tconst hookState = getHookState(currentIndex++, 2);\n\thookState._reducer = reducer;\n\tif (!hookState._component) {\n\t\thookState._value = [\n\t\t\t!init ? invokeOrReturn(undefined, initialState) : init(initialState),\n\n\t\t\taction => {\n\t\t\t\tconst currentValue = hookState._nextValue\n\t\t\t\t\t? hookState._nextValue[0]\n\t\t\t\t\t: hookState._value[0];\n\t\t\t\tconst nextValue = hookState._reducer(currentValue, action);\n\n\t\t\t\tif (currentValue !== nextValue) {\n\t\t\t\t\thookState._nextValue = [nextValue, hookState._value[1]];\n\t\t\t\t\thookState._component.setState({});\n\t\t\t\t}\n\t\t\t}\n\t\t];\n\n\t\thookState._component = currentComponent;\n\n\t\tif (!currentComponent._hasScuFromHooks) {\n\t\t\tcurrentComponent._hasScuFromHooks = true;\n\t\t\tconst prevScu = currentComponent.shouldComponentUpdate;\n\n\t\t\t// This SCU has the purpose of bailing out after repeated updates\n\t\t\t// to stateful hooks.\n\t\t\t// we store the next value in _nextValue[0] and keep doing that for all\n\t\t\t// state setters, if we have next states and\n\t\t\t// all next states within a component end up being equal to their original state\n\t\t\t// we are safe to bail out for this specific component.\n\t\t\tcurrentComponent.shouldComponentUpdate = function(p, s, c) {\n\t\t\t\tif (!hookState._component.__hooks) return true;\n\n\t\t\t\tconst stateHooks = hookState._component.__hooks._list.filter(\n\t\t\t\t\tx => x._component\n\t\t\t\t);\n\t\t\t\tconst allHooksEmpty = stateHooks.every(x => !x._nextValue);\n\t\t\t\t// When we have no updated hooks in the component we invoke the previous SCU or\n\t\t\t\t// traverse the VDOM tree further.\n\t\t\t\tif (allHooksEmpty) {\n\t\t\t\t\treturn prevScu ? prevScu.call(this, p, s, c) : true;\n\t\t\t\t}\n\n\t\t\t\t// We check whether we have components with a nextValue set that\n\t\t\t\t// have values that aren't equal to one another this pushes\n\t\t\t\t// us to update further down the tree\n\t\t\t\tlet shouldUpdate = false;\n\t\t\t\tstateHooks.forEach(hookItem => {\n\t\t\t\t\tif (hookItem._nextValue) {\n\t\t\t\t\t\tconst currentValue = hookItem._value[0];\n\t\t\t\t\t\thookItem._value = hookItem._nextValue;\n\t\t\t\t\t\thookItem._nextValue = undefined;\n\t\t\t\t\t\tif (currentValue !== hookItem._value[0]) shouldUpdate = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn shouldUpdate || hookState._component.props !== p\n\t\t\t\t\t? prevScu\n\t\t\t\t\t\t? prevScu.call(this, p, s, c)\n\t\t\t\t\t\t: true\n\t\t\t\t\t: false;\n\t\t\t};\n\t\t}\n\t}\n\n\treturn hookState._nextValue || hookState._value;\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {any[]} args\n */\nexport function useEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 3);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._pendingArgs = args;\n\n\t\tcurrentComponent.__hooks._pendingEffects.push(state);\n\t}\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {any[]} args\n */\nexport function useLayoutEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 4);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._pendingArgs = args;\n\n\t\tcurrentComponent._renderCallbacks.push(state);\n\t}\n}\n\nexport function useRef(initialValue) {\n\tcurrentHook = 5;\n\treturn useMemo(() => ({ current: initialValue }), []);\n}\n\n/**\n * @param {object} ref\n * @param {() => object} createHandle\n * @param {any[]} args\n */\nexport function useImperativeHandle(ref, createHandle, args) {\n\tcurrentHook = 6;\n\tuseLayoutEffect(\n\t\t() => {\n\t\t\tif (typeof ref == 'function') {\n\t\t\t\tref(createHandle());\n\t\t\t\treturn () => ref(null);\n\t\t\t} else if (ref) {\n\t\t\t\tref.current = createHandle();\n\t\t\t\treturn () => (ref.current = null);\n\t\t\t}\n\t\t},\n\t\targs == null ? args : args.concat(ref)\n\t);\n}\n\n/**\n * @param {() => any} factory\n * @param {any[]} args\n */\nexport function useMemo(factory, args) {\n\t/** @type {import('./internal').MemoHookState} */\n\tconst state = getHookState(currentIndex++, 7);\n\tif (argsChanged(state._args, args)) {\n\t\tstate._pendingValue = factory();\n\t\tstate._pendingArgs = args;\n\t\tstate._factory = factory;\n\t\treturn state._pendingValue;\n\t}\n\n\treturn state._value;\n}\n\n/**\n * @param {() => void} callback\n * @param {any[]} args\n */\nexport function useCallback(callback, args) {\n\tcurrentHook = 8;\n\treturn useMemo(() => callback, args);\n}\n\n/**\n * @param {import('./internal').PreactContext} context\n */\nexport function useContext(context) {\n\tconst provider = currentComponent.context[context._id];\n\t// We could skip this call here, but than we'd not call\n\t// `options._hook`. We need to do that in order to make\n\t// the devtools aware of this hook.\n\t/** @type {import('./internal').ContextHookState} */\n\tconst state = getHookState(currentIndex++, 9);\n\t// The devtools needs access to the context object to\n\t// be able to pull of the default value when no provider\n\t// is present in the tree.\n\tstate._context = context;\n\tif (!provider) return context._defaultValue;\n\t// This is probably not safe to convert to \"!\"\n\tif (state._value == null) {\n\t\tstate._value = true;\n\t\tprovider.sub(currentComponent);\n\t}\n\treturn provider.props.value;\n}\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, cb?: (value: T) => string | number) => void}\n */\nexport function useDebugValue(value, formatter) {\n\tif (options.useDebugValue) {\n\t\toptions.useDebugValue(formatter ? formatter(value) : value);\n\t}\n}\n\n/**\n * @param {(error: any, errorInfo: import('preact').ErrorInfo) => void} cb\n */\nexport function useErrorBoundary(cb) {\n\t/** @type {import('./internal').ErrorBoundaryHookState} */\n\tconst state = getHookState(currentIndex++, 10);\n\tconst errState = useState();\n\tstate._value = cb;\n\tif (!currentComponent.componentDidCatch) {\n\t\tcurrentComponent.componentDidCatch = (err, errorInfo) => {\n\t\t\tif (state._value) state._value(err, errorInfo);\n\t\t\terrState[1](err);\n\t\t};\n\t}\n\treturn [\n\t\terrState[0],\n\t\t() => {\n\t\t\terrState[1](undefined);\n\t\t}\n\t];\n}\n\nexport function useId() {\n\tconst state = getHookState(currentIndex++, 11);\n\tif (!state._value) {\n\t\t// Grab either the root node or the nearest async boundary node.\n\t\t/** @type {import('./internal.d').VNode} */\n\t\tlet root = currentComponent._vnode;\n\t\twhile (root !== null && !root._mask && root._parent !== null) {\n\t\t\troot = root._parent;\n\t\t}\n\n\t\tlet mask = root._mask || (root._mask = [0, 0]);\n\t\tstate._value = 'P' + mask[0] + '-' + mask[1]++;\n\t}\n\n\treturn state._value;\n}\n/**\n * After paint effects consumer.\n */\nfunction flushAfterPaintEffects() {\n\tlet component;\n\twhile ((component = afterPaintEffects.shift())) {\n\t\tif (!component._parentDom || !component.__hooks) continue;\n\t\ttry {\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeCleanup);\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeEffect);\n\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t} catch (e) {\n\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t\toptions._catchError(e, component._vnode);\n\t\t}\n\t}\n}\n\nlet HAS_RAF = typeof requestAnimationFrame == 'function';\n\n/**\n * Schedule a callback to be invoked after the browser has a chance to paint a new frame.\n * Do this by combining requestAnimationFrame (rAF) + setTimeout to invoke a callback after\n * the next browser frame.\n *\n * Also, schedule a timeout in parallel to the the rAF to ensure the callback is invoked\n * even if RAF doesn't fire (for example if the browser tab is not visible)\n *\n * @param {() => void} callback\n */\nfunction afterNextFrame(callback) {\n\tconst done = () => {\n\t\tclearTimeout(timeout);\n\t\tif (HAS_RAF) cancelAnimationFrame(raf);\n\t\tsetTimeout(callback);\n\t};\n\tconst timeout = setTimeout(done, RAF_TIMEOUT);\n\n\tlet raf;\n\tif (HAS_RAF) {\n\t\traf = requestAnimationFrame(done);\n\t}\n}\n\n// Note: if someone used options.debounceRendering = requestAnimationFrame,\n// then effects will ALWAYS run on the NEXT frame instead of the current one, incurring a ~16ms delay.\n// Perhaps this is not such a big deal.\n/**\n * Schedule afterPaintEffects flush after the browser paints\n * @param {number} newQueueLength\n */\nfunction afterPaint(newQueueLength) {\n\tif (newQueueLength === 1 || prevRaf !== options.requestAnimationFrame) {\n\t\tprevRaf = options.requestAnimationFrame;\n\t\t(prevRaf || afterNextFrame)(flushAfterPaintEffects);\n\t}\n}\n\n/**\n * @param {import('./internal').EffectHookState} hook\n */\nfunction invokeCleanup(hook) {\n\t// A hook cleanup can introduce a call to render which creates a new root, this will call options.vnode\n\t// and move the currentComponent away.\n\tconst comp = currentComponent;\n\tlet cleanup = hook._cleanup;\n\tif (typeof cleanup == 'function') {\n\t\thook._cleanup = undefined;\n\t\tcleanup();\n\t}\n\n\tcurrentComponent = comp;\n}\n\n/**\n * Invoke a Hook's effect\n * @param {import('./internal').EffectHookState} hook\n */\nfunction invokeEffect(hook) {\n\t// A hook call can introduce a call to render which creates a new root, this will call options.vnode\n\t// and move the currentComponent away.\n\tconst comp = currentComponent;\n\thook._cleanup = hook._value();\n\tcurrentComponent = comp;\n}\n\n/**\n * @param {any[]} oldArgs\n * @param {any[]} newArgs\n */\nfunction argsChanged(oldArgs, newArgs) {\n\treturn (\n\t\t!oldArgs ||\n\t\toldArgs.length !== newArgs.length ||\n\t\tnewArgs.some((arg, index) => arg !== oldArgs[index])\n\t);\n}\n\nfunction invokeOrReturn(arg, f) {\n\treturn typeof f == 'function' ? f(arg) : f;\n}\n"], "names": ["currentIndex", "currentComponent", "previousComponent", "prevRaf", "currentHook", "afterPaintEffects", "EMPTY", "oldBeforeDiff", "options", "oldBeforeRender", "oldAfterDiff", "diffed", "old<PERSON><PERSON><PERSON>", "__c", "oldBeforeUnmount", "unmount", "getHookState", "index", "type", "__h", "hooks", "__H", "__", "length", "push", "__V", "useState", "initialState", "useReducer", "invokeOrReturn", "reducer", "init", "hookState", "_reducer", "undefined", "action", "currentValue", "__N", "nextValue", "setState", "_hasScuFromHooks", "prevScu", "shouldComponentUpdate", "p", "s", "c", "stateHooks", "filter", "x", "every", "call", "this", "shouldUpdate", "for<PERSON>ach", "hookItem", "props", "useLayoutEffect", "callback", "args", "state", "__s", "args<PERSON><PERSON><PERSON>", "_pendingArgs", "useMemo", "factory", "flushAfterPaintEffects", "component", "shift", "invokeCleanup", "invokeEffect", "e", "__e", "__b", "vnode", "requestAnimationFrame", "afterNextFrame", "commitQueue", "some", "cb", "__v", "hasErrored", "HAS_RAF", "raf", "done", "clearTimeout", "timeout", "cancelAnimationFrame", "setTimeout", "hook", "comp", "cleanup", "oldArgs", "newArgs", "arg", "f", "useCallback", "useContext", "context", "provider", "sub", "value", "formatter", "useDebugValue", "useEffect", "useErrorBoundary", "errState", "componentDidCatch", "err", "errorInfo", "root", "__m", "mask", "useImperativeHandle", "ref", "createHandle", "current", "concat", "initialValue"], "mappings": "2QAGA,IAAIA,EAGAC,EAGAC,EAiBAC,EAdAC,EAAc,EAGdC,EAAoB,GAEpBC,EAAQ,GAERC,EAAgBC,EAAAA,YAChBC,EAAkBD,EAAAA,YAClBE,EAAeF,EAAAA,QAAQG,OACvBC,EAAYJ,EAAAA,QAAhBK,IACIC,EAAmBN,EAAOA,QAACO,QAoG/B,SAASC,EAAaC,EAAOC,GACxBV,UAAeW,KAClBX,EAAOA,QAAAW,IAAOlB,EAAkBgB,EAAOb,GAAec,GAEvDd,EAAc,EAOd,IAAMgB,EACLnB,EAAAoB,MACCpB,EAAgBoB,IAAW,CAC3BC,GAAO,GACPH,IAAiB,KAMnB,OAHIF,GAASG,KAAYG,QACxBH,EAAKE,GAAOE,KAAK,CAAEC,IAAenB,IAE5Bc,EAAAE,GAAYL,EACnB,CAKM,SAASS,EAASC,GAExB,OADAvB,EAAc,EACPwB,EAAWC,EAAgBF,EAClC,CAQM,SAASC,EAAWE,EAASH,EAAcI,GAEjD,IAAMC,EAAYhB,EAAahB,IAAgB,GAE/C,GADAgC,EAAUC,EAAWH,GAChBE,EAALnB,MACCmB,EAAAV,GAAmB,CACjBS,EAAiDA,EAAKJ,GAA/CE,OAAeK,EAAWP,GAElC,SAAAQ,GACC,IAAMC,EAAeJ,EAASK,IAC3BL,MAAqB,GACrBA,EAASV,GAAQ,GACdgB,EAAYN,EAAUC,EAASG,EAAcD,GAE/CC,IAAiBE,IACpBN,EAASK,IAAc,CAACC,EAAWN,EAAAV,GAAiB,IACpDU,EAAAnB,IAAqB0B,SAAS,CAA9B,GAED,GAGFP,MAAuB/B,GAElBA,EAAiBuC,GAAkB,CACvCvC,EAAiBuC,GAAmB,EACpC,IAAMC,EAAUxC,EAAiByC,sBAQjCzC,EAAiByC,sBAAwB,SAASC,EAAGC,EAAGC,GACvD,IAAKb,EAALnB,IAAAQ,IAAmC,OAAA,EAEnC,IAAMyB,EAAad,EAAAnB,IAAAQ,IAAAC,GAAmCyB,OACrD,SAAAC,UAAKA,EADanC,GAAA,GAMnB,GAHsBiC,EAAWG,MAAM,SAAAD,GAAC,OAAKA,EAALX,GAAA,GAIvC,OAAOI,GAAUA,EAAQS,KAAKC,KAAMR,EAAGC,EAAGC,GAM3C,IAAIO,GAAe,EAUnB,OATAN,EAAWO,QAAQ,SAAAC,GAClB,GAAIA,EAAJjB,IAAyB,CACxB,IAAMD,EAAekB,EAAQhC,GAAQ,GACrCgC,EAAAhC,GAAkBgC,EAClBA,IAAAA,EAAAjB,SAAsBH,EAClBE,IAAiBkB,EAAQhC,GAAQ,KAAI8B,GAAe,EACxD,CACD,MAEMA,GAAgBpB,EAASnB,IAAY0C,QAAUZ,MACnDF,GACCA,EAAQS,KAAKC,KAAMR,EAAGC,EAAGC,GAG7B,CACD,CAGF,OAAOb,EAASK,KAAeL,EAC/BV,EAAA,CAqBM,SAASkC,EAAgBC,EAAUC,GAEzC,IAAMC,EAAQ3C,EAAahB,IAAgB,IACtCQ,EAAAA,QAADoD,KAAyBC,EAAYF,EAADtC,IAAcqC,KACrDC,EAAArC,GAAemC,EACfE,EAAMG,EAAeJ,EAErBzD,EAAgBkB,IAAkBK,KAAKmC,GAExC,CAgCeI,SAAAA,EAAQC,EAASN,GAEhC,IAAMC,EAAQ3C,EAAahB,IAAgB,GAC3C,OAAI6D,EAAYF,EAAaD,IAAAA,IAC5BC,EAAKlC,IAAiBuC,IACtBL,EAAMG,EAAeJ,EACrBC,EAAKxC,IAAY6C,EACVL,EAAPlC,KAGMkC,IACP,CAqFD,SAASM,IAER,IADA,IAAIC,EACIA,EAAY7D,EAAkB8D,SACrC,GAAKD,OAAyBA,EAA9B7C,IACA,IACC6C,EAAS7C,IAAyBgC,IAAAA,QAAQe,GAC1CF,EAAS7C,IAAyBgC,IAAAA,QAAQgB,GAC1CH,EAAS7C,IAA2BF,IAAA,EAIpC,CAHC,MAAOmD,GACRJ,EAAS7C,IAA2BF,IAAA,GACpCX,EAAAA,QAAO+D,IAAaD,EAAGJ,MACvB,CAEF,CArXD1D,EAAOA,QAAPgE,IAAgB,SAAAC,GACfxE,EAAmB,KACfM,GAAeA,EAAckE,EACjC,EAEDjE,EAAAA,YAAkB,SAAAiE,GACbhE,GAAiBA,EAAgBgE,GAGrCzE,EAAe,EAEf,IAAMoB,GAHNnB,EAAmBwE,EAAnB5D,SAIIO,IACClB,IAAsBD,GACzBmB,EAAKD,IAAmB,GACxBlB,EAAgBkB,IAAoB,GACpCC,EAAKE,GAAO+B,QAAQ,SAAAC,GACfA,EAAqBjB,MACxBiB,EAAAhC,GAAkBgC,EAAlBjB,KAEDiB,MAAyBhD,EACzBgD,EAAAjB,IAAsBiB,EAASQ,OAAe5B,CAC9C,KAEDd,EAAKD,IAAiBkC,QAAQe,GAC9BhD,EAAKD,IAAiBkC,QAAQgB,GAC9BjD,EAAKD,IAAmB,KAG1BjB,EAAoBD,CACpB,EAEDO,EAAOA,QAACG,OAAS,SAAA8D,GACZ/D,GAAcA,EAAa+D,GAE/B,IAAM5B,EAAI4B,EAAV5D,IACIgC,GAAKA,QACJA,EAAAxB,IAAAF,IAA0BI,SAoXR,IApX2BlB,EAAkBmB,KAAKqB,IAoX7C1C,IAAYK,EAAOA,QAACkE,yBAC/CvE,EAAUK,EAAOA,QAACkE,wBACNC,GAAgBV,IArX5BpB,EAACxB,OAAegC,QAAQ,SAAAC,GACnBA,EAASQ,IACZR,MAAiBA,EAASQ,GAEvBR,EAAA7B,MAA2BnB,IAC9BgD,KAAkBA,EAClB7B,KACD6B,EAASQ,OAAe5B,EACxBoB,EAAA7B,IAAyBnB,CACzB,IAEFJ,EAAoBD,EAAmB,IACvC,EAEDO,EAAOA,QAAPK,IAAkB,SAAC4D,EAAOG,GACzBA,EAAYC,KAAK,SAAAX,GAChB,IACCA,EAAA/C,IAA2BkC,QAAQe,GACnCF,EAAA/C,IAA6B+C,EAAS/C,IAAkB4B,OAAO,SAAA+B,GAC9DA,OAAAA,EAAAxD,IAAY+C,EAAaS,EADuC,EASjE,CANC,MAAOR,GACRM,EAAYC,KAAK,SAAAhC,GACZA,EAAoBA,MAAAA,MAAqB,GAC7C,GACD+B,EAAc,GACdpE,EAAAA,QAAA+D,IAAoBD,EAAGJ,EACvBa,IAAA,CACD,GAEGnE,GAAWA,EAAU6D,EAAOG,EAChC,EAEDpE,EAAOA,QAACO,QAAU,SAAA0D,GACb3D,GAAkBA,EAAiB2D,GAEvC,IAEKO,EAFCnC,EAAI4B,EAAH5D,IACHgC,GAAKA,EAATxB,MAECwB,EAACxB,OAAegC,QAAQ,SAAAT,GACvB,IACCwB,EAAcxB,EAGd,CAFC,MAAO0B,GACRU,EAAaV,CACb,CACD,GACDzB,EAAAxB,SAAYa,EACR8C,GAAYxE,EAAOA,QAAA+D,IAAaS,EAAYnC,OAEjD,EAgSD,IAAIoC,EAA0C,mBAAzBP,sBAYrB,SAASC,EAAelB,GACvB,IAOIyB,EAPEC,EAAO,WACZC,aAAaC,GACTJ,GAASK,qBAAqBJ,GAClCK,WAAW9B,EACX,EACK4B,EAAUE,WAAWJ,EA5YR,KA+YfF,IACHC,EAAMR,sBAAsBS,GAE7B,CAmBD,SAASf,EAAcoB,GAGtB,IAAMC,EAAOxF,EACTyF,EAAUF,EAAd3E,IACsB,mBAAX6E,IACVF,WAAgBtD,EAChBwD,KAGDzF,EAAmBwF,CACnB,CAMD,SAASpB,EAAamB,GAGrB,IAAMC,EAAOxF,EACbuF,EAAI3E,IAAY2E,OAChBvF,EAAmBwF,CACnB,CAMD,SAAS5B,EAAY8B,EAASC,GAC7B,OACED,GACDA,EAAQpE,SAAWqE,EAAQrE,QAC3BqE,EAAQf,KAAK,SAACgB,EAAK5E,GAAU4E,OAAAA,IAAQF,EAAQ1E,EAAhC,EAEd,CAED,SAASY,EAAegE,EAAKC,GAC5B,MAAmB,mBAALA,EAAkBA,EAAED,GAAOC,CACzC,eAhLeC,SAAYtC,EAAUC,GAErC,OADAtD,EAAc,EACP2D,EAAQ,WAAA,OAAMN,CAAN,EAAgBC,EAC/B,eAKesC,SAAWC,GAC1B,IAAMC,EAAWjG,EAAiBgG,QAAQA,EAA1CpF,KAKM8C,EAAQ3C,EAAahB,IAAgB,GAK3C,OADA2D,EAAKd,EAAYoD,EACZC,GAEe,MAAhBvC,EAAKrC,KACRqC,EAAArC,IAAe,EACf4E,EAASC,IAAIlG,IAEPiG,EAAS3C,MAAM6C,OANAH,EAEtB3E,EAKA,kBAMM,SAAuB8E,EAAOC,GAChC7F,EAAOA,QAAC8F,eACX9F,EAAAA,QAAQ8F,cAAcD,EAAYA,EAAUD,GAASA,EAEtD,cA7GeG,SAAU9C,EAAUC,GAEnC,IAAMC,EAAQ3C,EAAahB,IAAgB,IACtCQ,EAADA,QAAAoD,KAAyBC,EAAYF,EAAaD,IAAAA,KACrDC,KAAeF,EACfE,EAAMG,EAAeJ,EAErBzD,EAAgBoB,IAAyBG,IAAAA,KAAKmC,GAE/C,qBAyGe6C,SAAiB1B,GAEhC,IAAMnB,EAAQ3C,EAAahB,IAAgB,IACrCyG,EAAW/E,IAQjB,OAPAiC,EAAKrC,GAAUwD,EACV7E,EAAiByG,oBACrBzG,EAAiByG,kBAAoB,SAACC,EAAKC,GACtCjD,MAAcA,EAAKrC,GAAQqF,EAAKC,GACpCH,EAAS,GAAGE,EACZ,GAEK,CACNF,EAAS,GACT,WACCA,EAAS,QAAGvE,EACZ,EAEF,UAEM,WACN,IAAMyB,EAAQ3C,EAAahB,IAAgB,IAC3C,IAAK2D,EAALrC,GAAmB,CAIlB,IADA,IAAIuF,EAAO5G,EAAH8E,IACQ,OAAT8B,IAAkBA,EAADC,KAAgC,OAAjBD,MACtCA,EAAOA,EACPvF,GAED,IAAIyF,EAAOF,EAAAC,MAAeD,EAAIC,IAAS,CAAC,EAAG,IAC3CnD,KAAe,IAAMoD,EAAK,GAAK,IAAMA,EAAK,IAC1C,CAED,OAAOpD,EACPrC,EAAA,wBAhHe0F,SAAoBC,EAAKC,EAAcxD,GACtDtD,EAAc,EACdoD,EACC,WACC,MAAkB,mBAAPyD,GACVA,EAAIC,KACG,WAAA,OAAMD,EAAI,KAAV,GACGA,GACVA,EAAIE,QAAUD,IACP,WAAA,OAAOD,EAAIE,QAAU,IAArB,QAFD,CAIP,EACO,MAARzD,EAAeA,EAAOA,EAAK0D,OAAOH,GAEnC,0DAxBM,SAAgBI,GAEtB,OADAjH,EAAc,EACP2D,EAAQ,WAAO,MAAA,CAAEoD,QAASE,EAAlB,EAAmC,GAClD"}