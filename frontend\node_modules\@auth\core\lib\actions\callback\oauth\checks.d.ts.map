{"version": 3, "file": "checks.d.ts", "sourceRoot": "", "sources": ["../../../../src/lib/actions/callback/oauth/checks.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,cAAc,EACd,eAAe,EACf,eAAe,EACf,IAAI,EACL,MAAM,sBAAsB,CAAA;AAC7B,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,0BAA0B,CAAA;AACtD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAA;AACzE,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAA;AAM7E,+BAA+B;AAC/B,wBAAsB,UAAU,CAC9B,IAAI,EAAE,MAAM,cAAc,EAC1B,KAAK,EAAE,MAAM,EACb,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,eAAe,CAAC,OAAO,GAAG,MAAM,GAAG,oBAAoB,CAAC,EACjE,IAAI,CAAC,EAAE,GAAG,GACT,OAAO,CAAC,MAAM,CAAC,CAejB;AAGD,eAAO,MAAM,IAAI;oBACO,gBAAgB,OAAO,CAAC;;;;IAY9C;;;;;;OAMG;iBAEQ,eAAe,CAAC,SAAS,CAAC,cACvB,MAAM,EAAE,WACX,gBAAgB,OAAO,CAAC,GAChC,QAAQ,MAAM,GAAG,SAAS,CAAC;CA4B/B,CAAA;AAGD,wBAAgB,WAAW,CAAC,KAAK,EAAE,MAAM,GACrC;IACE,2GAA2G;IAC3G,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,uCAAuC;IACvC,MAAM,EAAE,MAAM,CAAA;CACf,GACD,SAAS,CAKZ;AAED,eAAO,MAAM,KAAK;oBACM,gBAAgB,OAAO,CAAC,SAAS,MAAM;;;;IAyB7D;;;;;;OAMG;iBAEQ,eAAe,CAAC,SAAS,CAAC,cACvB,MAAM,EAAE,WACX,gBAAgB,OAAO,CAAC,gBACnB,MAAM,GACnB,QAAQ,MAAM,GAAG,SAAS,CAAC;CAqC/B,CAAA;AAGD,eAAO,MAAM,KAAK;oBACM,gBAAgB,MAAM,CAAC;;;;IAO7C;;;;;;OAMG;iBAEQ,eAAe,CAAC,SAAS,CAAC,cACvB,MAAM,EAAE,WACX,gBAAgB,MAAM,CAAC,GAC/B,QAAQ,MAAM,GAAG,SAAS,CAAC;CAyB/B,CAAA;AAED;;;GAGG;AACH,wBAAgB,WAAW,CACzB,KAAK,EAAE,eAAe,CAAC,OAAO,CAAC,EAC/B,QAAQ,EAAE,mBAAmB,CAAC,GAAG,CAAC,EAClC,iBAAiB,EAAE,eAAe,CAAC,mBAAmB,CAAC;;;;;;EAoBxD;AAGD,KAAK,uBAAuB,GAAG;IAAE,SAAS,EAAE,MAAM,CAAC;IAAC,YAAY,CAAC,EAAE,IAAI,CAAA;CAAE,CAAA;AACzE,eAAO,MAAM,iBAAiB;oBAEjB,gBAAgB,oBAAoB,CAAC,aACnC,MAAM,iBACF,IAAI;;;IAarB;;OAEG;iBAEQ,gBAAgB,oBAAoB,CAAC,WACrC,eAAe,CAAC,SAAS,CAAC,cACvB,MAAM,EAAE,GACnB,QAAQ,uBAAuB,CAAC;CAwBpC,CAAA"}