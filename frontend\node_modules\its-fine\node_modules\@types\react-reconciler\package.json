{"name": "@types/react-reconciler", "version": "0.28.9", "description": "TypeScript definitions for react-reconciler", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-reconciler", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}, {"name": "<PERSON>", "githubUsername": "zhanghaocong", "url": "https://github.com/zhanghaocong"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mathieudutour"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-reconciler"}, "scripts": {}, "dependencies": {}, "peerDependencies": {"@types/react": "*"}, "typesPublisherContentHash": "08ff2948d78f7616f151a26a7f4a9f847bd190e3ea194516a4dc517cea40cb6e", "typeScriptVersion": "5.0"}