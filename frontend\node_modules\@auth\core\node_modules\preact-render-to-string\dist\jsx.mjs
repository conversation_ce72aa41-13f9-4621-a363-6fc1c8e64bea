import{Fragment as t,options as e}from"preact";if("function"!=typeof Symbol){var n=0;Symbol=function(t){return"@@"+t+ ++n},Symbol.for=function(t){return"@@"+t}}var r=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,o=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,i=/[\s\n\\/='"\0<>]/,a=/^xlink:?./,l=/["&<]/;function c(t){if(!1===l.test(t+=""))return t;for(var e=0,n=0,r="",o="";n<t.length;n++){switch(t.charCodeAt(n)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 60:o="&lt;";break;default:continue}n!==e&&(r+=t.slice(e,n)),r+=o,e=n+1}return n!==e&&(r+=t.slice(e,n)),r}var s=function(t,e){return String(t).replace(/(\n+)/g,"$1"+(e||"\t"))},f=function(t,e,n){return String(t).length>(e||40)||!n&&-1!==String(t).indexOf("\n")||-1!==String(t).indexOf("<")},u={},p=/([A-Z])/g;function d(t){var e="";for(var n in t){var o=t[n];null!=o&&""!==o&&(e&&(e+=" "),e+="-"==n[0]?n:u[n]||(u[n]=n.replace(p,"-$1").toLowerCase()),e="number"==typeof o&&!1===r.test(n)?e+": "+o+"px;":e+": "+o+";")}return e||void 0}function y(t,e){return Array.isArray(e)?e.reduce(y,t):null!=e&&!1!==e&&t.push(e),t}function _(){this.__d=!0}function g(t,e){return{__v:t,context:e,props:t.props,setState:_,forceUpdate:_,__d:!0,__h:[]}}function v(t,e){var n=t.contextType,r=n&&e[n.__c];return null!=n?r?r.props.value:n.__:e}var b=[];function m(n,r,l,u,p,_){if(null==n||"boolean"==typeof n)return"";if("object"!=typeof n)return c(n);var h=l.pretty,j=h&&"string"==typeof h?h:"\t";if(Array.isArray(n)){for(var x="",S=0;S<n.length;S++)h&&S>0&&(x+="\n"),x+=m(n[S],r,l,u,p,_);return x}var k,A=n.type,O=n.props,w=!1;if("function"==typeof A){if(w=!0,!l.shallow||!u&&!1!==l.renderRootComponent){if(A===t){var F=[];return y(F,n.props.children),m(F,r,l,!1!==l.shallowHighOrder,p,_)}var C,E=n.__c=g(n,r);e.__b&&e.__b(n);var M=e.__r;if(A.prototype&&"function"==typeof A.prototype.render){var H=v(A,r);(E=n.__c=new A(O,H)).__v=n,E._dirty=E.__d=!0,E.props=O,null==E.state&&(E.state={}),null==E._nextState&&null==E.__s&&(E._nextState=E.__s=E.state),E.context=H,A.getDerivedStateFromProps?E.state=Object.assign({},E.state,A.getDerivedStateFromProps(E.props,E.state)):E.componentWillMount&&(E.componentWillMount(),E.state=E._nextState!==E.state?E._nextState:E.__s!==E.state?E.__s:E.state),M&&M(n),C=E.render(E.props,E.state,E.context)}else for(var N=v(A,r),D=0;E.__d&&D++<25;)E.__d=!1,M&&M(n),C=A.call(n.__c,O,N);return E.getChildContext&&(r=Object.assign({},r,E.getChildContext())),e.diffed&&e.diffed(n),m(C,r,l,!1!==l.shallowHighOrder,p,_)}A=(k=A).displayName||k!==Function&&k.name||function(t){var e=(Function.prototype.toString.call(t).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!e){for(var n=-1,r=b.length;r--;)if(b[r]===t){n=r;break}n<0&&(n=b.push(t)-1),e="UnnamedComponent"+n}return e}(k)}var I,L,W="<"+A;if(O){var $=Object.keys(O);l&&!0===l.sortAttributes&&$.sort();for(var P=0;P<$.length;P++){var T=$[P],U=O[T];if("children"!==T){if(!i.test(T)&&(l&&l.allAttributes||"key"!==T&&"ref"!==T&&"__self"!==T&&"__source"!==T)){if("defaultValue"===T)T="value";else if("defaultChecked"===T)T="checked";else if("defaultSelected"===T)T="selected";else if("className"===T){if(void 0!==O.class)continue;T="class"}else p&&a.test(T)&&(T=T.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===T){if(O.for)continue;T="for"}"style"===T&&U&&"object"==typeof U&&(U=d(U)),"a"===T[0]&&"r"===T[1]&&"boolean"==typeof U&&(U=String(U));var R=l.attributeHook&&l.attributeHook(T,U,r,l,w);if(R||""===R)W+=R;else if("dangerouslySetInnerHTML"===T)L=U&&U.__html;else if("textarea"===A&&"value"===T)I=U;else if((U||0===U||""===U)&&"function"!=typeof U){if(!(!0!==U&&""!==U||(U=T,l&&l.xml))){W=W+" "+T;continue}if("value"===T){if("select"===A){_=U;continue}"option"===A&&_==U&&void 0===O.selected&&(W+=" selected")}W=W+" "+T+'="'+c(U)+'"'}}}else I=U}}if(h){var J=W.replace(/\n\s*/," ");J===W||~J.indexOf("\n")?h&&~W.indexOf("\n")&&(W+="\n"):W=J}if(W+=">",i.test(A))throw new Error(A+" is not a valid HTML tag name in "+W);var V,q=o.test(A)||l.voidElements&&l.voidElements.test(A),z=[];if(L)h&&f(L)&&(L="\n"+j+s(L,j)),W+=L;else if(null!=I&&y(V=[],I).length){for(var B=h&&~W.indexOf("\n"),G=!1,Z=0;Z<V.length;Z++){var K=V[Z];if(null!=K&&!1!==K){var Q=m(K,r,l,!0,"svg"===A||"foreignObject"!==A&&p,_);if(h&&!B&&f(Q)&&(B=!0),Q)if(h){var X=Q.length>0&&"<"!=Q[0];G&&X?z[z.length-1]+=Q:z.push(Q),G=X}else z.push(Q)}}if(h&&B)for(var Y=z.length;Y--;)z[Y]="\n"+j+s(z[Y],j)}if(z.length||L)W+=z.join("");else if(l&&l.xml)return W.substring(0,W.length-1)+" />";return!q||V||L?(h&&~W.indexOf("\n")&&(W+="\n"),W=W+"</"+A+">"):W=W.replace(/>$/," />"),W}var h={shallow:!0};x.render=x;var j=[];function x(t,n,r){n=n||{};var o,i=e.__s;return e.__s=!0,o=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?m(t,n,r):w(t,n,!1,void 0),e.__c&&e.__c(t,j),e.__s=i,j.length=0,o}function S(t,e){return"className"===t?"class":"htmlFor"===t?"for":"defaultValue"===t?"value":"defaultChecked"===t?"checked":"defaultSelected"===t?"selected":e&&a.test(t)?t.toLowerCase().replace(/^xlink:?/,"xlink:"):t}function k(t,e){return"style"===t&&null!=e&&"object"==typeof e?d(e):"a"===t[0]&&"r"===t[1]&&"boolean"==typeof e?String(e):e}var A=Array.isArray,O=Object.assign;function w(n,r,a,l){if(null==n||!0===n||!1===n||""===n)return"";if("object"!=typeof n)return c(n);if(A(n)){for(var s="",f=0;f<n.length;f++)s+=w(n[f],r,a,l);return s}e.__b&&e.__b(n);var u=n.type,p=n.props;if("function"==typeof u){if(u===t)return w(n.props.children,r,a,l);var d;d=u.prototype&&"function"==typeof u.prototype.render?function(t,n){var r=t.type,o=v(r,n),i=new r(t.props,o);t.__c=i,i.__v=t,i.__d=!0,i.props=t.props,null==i.state&&(i.state={}),null==i.__s&&(i.__s=i.state),i.context=o,r.getDerivedStateFromProps?i.state=O({},i.state,r.getDerivedStateFromProps(i.props,i.state)):i.componentWillMount&&(i.componentWillMount(),i.state=i.__s!==i.state?i.__s:i.state);var a=e.__r;return a&&a(t),i.render(i.props,i.state,i.context)}(n,r):function(t,n){var r,o=g(t,n),i=v(t.type,n);t.__c=o;for(var a=e.__r,l=0;o.__d&&l++<25;)o.__d=!1,a&&a(t),r=t.type.call(o,t.props,i);return r}(n,r);var y=n.__c;y.getChildContext&&(r=O({},r,y.getChildContext()));var _=w(d,r,a,l);return e.diffed&&e.diffed(n),_}var b,m,h="<";if(h+=u,p)for(var j in b=p.children,p){var x=p[j];if(!("key"===j||"ref"===j||"__self"===j||"__source"===j||"children"===j||"className"===j&&"class"in p||"htmlFor"===j&&"for"in p||i.test(j)))if(x=k(j=S(j,a),x),"dangerouslySetInnerHTML"===j)m=x&&x.__html;else if("textarea"===u&&"value"===j)b=x;else if((x||0===x||""===x)&&"function"!=typeof x){if(!0===x||""===x){x=j,h=h+" "+j;continue}if("value"===j){if("select"===u){l=x;continue}"option"!==u||l!=x||"selected"in p||(h+=" selected")}h=h+" "+j+'="'+c(x)+'"'}}var F=h;if(h+=">",i.test(u))throw new Error(u+" is not a valid HTML tag name in "+h);var C="",E=!1;if(m)C+=m,E=!0;else if("string"==typeof b)C+=c(b),E=!0;else if(A(b))for(var M=0;M<b.length;M++){var H=b[M];if(null!=H&&!1!==H){var N=w(H,r,"svg"===u||"foreignObject"!==u&&a,l);N&&(C+=N,E=!0)}}else if(null!=b&&!1!==b&&!0!==b){var D=w(b,r,"svg"===u||"foreignObject"!==u&&a,l);D&&(C+=D,E=!0)}if(e.diffed&&e.diffed(n),E)h+=C;else if(o.test(u))return F+" />";return h+"</"+u+">"}x.shallowRender=function(t,e){return x(t,e,h)};const F=/(\\|\"|\')/g,C=Object.prototype.toString,E=Date.prototype.toISOString,M=Error.prototype.toString,H=RegExp.prototype.toString,N=Symbol.prototype.toString,D=/^Symbol\((.*)\)(.*)$/,I=/\n/gi,L=Object.getOwnPropertySymbols||(t=>[]);function W(t){return"[object Array]"===t||"[object ArrayBuffer]"===t||"[object DataView]"===t||"[object Float32Array]"===t||"[object Float64Array]"===t||"[object Int8Array]"===t||"[object Int16Array]"===t||"[object Int32Array]"===t||"[object Uint8Array]"===t||"[object Uint8ClampedArray]"===t||"[object Uint16Array]"===t||"[object Uint32Array]"===t}function $(t){return""===t.name?"[Function anonymous]":"[Function "+t.name+"]"}function P(t){return N.call(t).replace(D,"Symbol($1)")}function T(t){return"["+M.call(t)+"]"}function U(t){if(!0===t||!1===t)return""+t;if(void 0===t)return"undefined";if(null===t)return"null";const e=typeof t;if("number"===e)return function(t){return t!=+t?"NaN":0===t&&1/t<0?"-0":""+t}(t);if("string"===e)return'"'+function(t){return t.replace(F,"\\$1")}(t)+'"';if("function"===e)return $(t);if("symbol"===e)return P(t);const n=C.call(t);return"[object WeakMap]"===n?"WeakMap {}":"[object WeakSet]"===n?"WeakSet {}":"[object Function]"===n||"[object GeneratorFunction]"===n?$(t,min):"[object Symbol]"===n?P(t):"[object Date]"===n?E.call(t):"[object Error]"===n?T(t):"[object RegExp]"===n?H.call(t):"[object Arguments]"===n&&0===t.length?"Arguments []":W(n)&&0===t.length?t.constructor.name+" []":t instanceof Error&&T(t)}function R(t,e,n,r,o,i,a,l,c,s){let f="";if(t.length){f+=o;const u=n+e;for(let n=0;n<t.length;n++)f+=u+q(t[n],e,u,r,o,i,a,l,c,s),n<t.length-1&&(f+=","+r);f+=o+n}return"["+f+"]"}function J(t,e,n,r,o,i,a,l,c,s){if((i=i.slice()).indexOf(t)>-1)return"[Circular]";i.push(t);const f=++l>a;if(!f&&t.toJSON&&"function"==typeof t.toJSON)return q(t.toJSON(),e,n,r,o,i,a,l,c,s);const u=C.call(t);return"[object Arguments]"===u?f?"[Arguments]":function(t,e,n,r,o,i,a,l,c,s){return(s?"":"Arguments ")+R(t,e,n,r,o,i,a,l,c,s)}(t,e,n,r,o,i,a,l,c,s):W(u)?f?"[Array]":function(t,e,n,r,o,i,a,l,c,s){return(s?"":t.constructor.name+" ")+R(t,e,n,r,o,i,a,l,c,s)}(t,e,n,r,o,i,a,l,c,s):"[object Map]"===u?f?"[Map]":function(t,e,n,r,o,i,a,l,c,s){let f="Map {";const u=t.entries();let p=u.next();if(!p.done){f+=o;const t=n+e;for(;!p.done;)f+=t+q(p.value[0],e,t,r,o,i,a,l,c,s)+" => "+q(p.value[1],e,t,r,o,i,a,l,c,s),p=u.next(),p.done||(f+=","+r);f+=o+n}return f+"}"}(t,e,n,r,o,i,a,l,c,s):"[object Set]"===u?f?"[Set]":function(t,e,n,r,o,i,a,l,c,s){let f="Set {";const u=t.entries();let p=u.next();if(!p.done){f+=o;const t=n+e;for(;!p.done;)f+=t+q(p.value[1],e,t,r,o,i,a,l,c,s),p=u.next(),p.done||(f+=","+r);f+=o+n}return f+"}"}(t,e,n,r,o,i,a,l,c,s):"object"==typeof t?f?"[Object]":function(t,e,n,r,o,i,a,l,c,s){let f=(s?"":t.constructor?t.constructor.name+" ":"Object ")+"{",u=Object.keys(t).sort();const p=L(t);if(p.length&&(u=u.filter(t=>!("symbol"==typeof t||"[object Symbol]"===C.call(t))).concat(p)),u.length){f+=o;const p=n+e;for(let n=0;n<u.length;n++){const d=u[n];f+=p+q(d,e,p,r,o,i,a,l,c,s)+": "+q(t[d],e,p,r,o,i,a,l,c,s),n<u.length-1&&(f+=","+r)}f+=o+n}return f+"}"}(t,e,n,r,o,i,a,l,c,s):void 0}function V(t,e,n,r,o,i,a,l,c,s){let f,u=!1;for(let e=0;e<c.length;e++)if(f=c[e],f.test(t)){u=!0;break}return!!u&&f.print(t,function(t){return q(t,e,n,r,o,i,a,l,c,s)},function(t){const r=n+e;return r+t.replace(I,"\n"+r)},{edgeSpacing:o,spacing:r})}function q(t,e,n,r,o,i,a,l,c,s){return U(t)||V(t,e,n,r,o,i,a,l,c,s)||J(t,e,n,r,o,i,a,l,c,s)}const z={indent:2,min:!1,maxDepth:Infinity,plugins:[]};function B(t){return new Array(t+1).join(" ")}var G={test:function(t){return t&&"object"==typeof t&&"type"in t&&"props"in t&&"key"in t},print:function(t,e,n){return x(t,G.context,G.opts)}},Z={plugins:[G]},K={attributeHook:function(t,e,n,r,o){var i=typeof e;if("dangerouslySetInnerHTML"===t)return!1;if(null==e||"function"===i&&!r.functions)return"";if(r.skipFalseAttributes&&!o&&(!1===e||("class"===t||"style"===t)&&""===e))return"";var a="string"==typeof r.pretty?r.pretty:"\t";return"string"!==i?("function"!==i||r.functionNames?(G.context=n,G.opts=r,~(e=function(t,e){let n,r;e?(function(t){if(Object.keys(t).forEach(t=>{if(!z.hasOwnProperty(t))throw new Error("prettyFormat: Invalid option: "+t)}),t.min&&void 0!==t.indent&&0!==t.indent)throw new Error("prettyFormat: Cannot run with min option and indent")}(e),e=function(t){const e={};return Object.keys(z).forEach(n=>e[n]=t.hasOwnProperty(n)?t[n]:z[n]),e.min&&(e.indent=0),e}(e)):e=z;const o=e.min?" ":"\n",i=e.min?"":"\n";if(e&&e.plugins.length){n=B(e.indent),r=[];var a=V(t,n,"",o,i,r,e.maxDepth,0,e.plugins,e.min);if(a)return a}return U(t)||(n||(n=B(e.indent)),r||(r=[]),J(t,n,"",o,i,r,e.maxDepth,0,e.plugins,e.min))}(e,Z)).indexOf("\n")&&(e=s("\n"+e,a)+"\n")):e="Function",s("\n"+t+"={"+e+"}",a)):"\n"+a+t+'="'+c(e)+'"'},jsx:!0,xml:!1,functions:!0,functionNames:!0,skipFalseAttributes:!0,pretty:"  "};function Q(t,e,n,r){return x(t,e,n=Object.assign({},K,n||{}))}export default Q;export{Q as render};
//# sourceMappingURL=jsx.module.js.map
