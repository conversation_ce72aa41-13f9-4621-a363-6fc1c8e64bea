{"version": 3, "file": "nodemailer.d.ts", "sourceRoot": "", "sources": ["../src/providers/nodemailer.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAA;AAC7D,OAAO,KAAK,aAAa,MAAM,wCAAwC,CAAA;AACvE,OAAO,KAAK,iBAAiB,MAAM,4CAA4C,CAAA;AAC/E,OAAO,KAAK,YAAY,MAAM,uCAAuC,CAAA;AACrE,OAAO,KAAK,aAAa,MAAM,wCAAwC,CAAA;AACvE,OAAO,KAAK,QAAQ,MAAM,mCAAmC,CAAA;AAC7D,OAAO,KAAK,eAAe,MAAM,0CAA0C,CAAA;AAC3E,OAAO,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,aAAa,CAAA;AACnD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,YAAY,CAAA;AAE7C,KAAK,mBAAmB,GACpB,MAAM,GACN,aAAa,GACb,aAAa,CAAC,OAAO,GACrB,QAAQ,GACR,QAAQ,CAAC,OAAO,GAChB,iBAAiB,GACjB,iBAAiB,CAAC,OAAO,GACzB,eAAe,GACf,eAAe,CAAC,OAAO,GACvB,aAAa,GACb,aAAa,CAAC,OAAO,GACrB,YAAY,GACZ,YAAY,CAAC,OAAO,GACpB,SAAS,CAAC,GAAG,CAAC,GACd,gBAAgB,CAAA;AAEpB,MAAM,WAAW,gBAAiB,SAAQ,WAAW;IACnD,MAAM,CAAC,EAAE,mBAAmB,CAAA;IAC5B,uBAAuB,EAAE,CAAC,MAAM,EAAE;QAChC,UAAU,EAAE,MAAM,CAAA;QAClB,GAAG,EAAE,MAAM,CAAA;QACX,OAAO,EAAE,IAAI,CAAA;QACb,QAAQ,EAAE,gBAAgB,CAAA;QAC1B,KAAK,EAAE,MAAM,CAAA;QACb,KAAK,EAAE,KAAK,CAAA;QACZ,OAAO,EAAE,OAAO,CAAA;KACjB,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;IACrB,OAAO,EAAE,oBAAoB,CAAA;CAC9B;AAED,MAAM,MAAM,oBAAoB,GAAG,IAAI,CACrC,OAAO,CAAC,gBAAgB,CAAC,EACzB,SAAS,GAAG,MAAM,CACnB,CAAA;AAED,MAAM,CAAC,OAAO,UAAU,UAAU,CAChC,MAAM,EAAE,oBAAoB,GAC3B,gBAAgB,CA6BlB"}