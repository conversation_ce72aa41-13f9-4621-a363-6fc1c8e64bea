{"version": 3, "file": "testUtils.umd.js", "sources": ["../src/index.js"], "sourcesContent": ["import { options } from 'preact';\n\n/**\n * Setup a rerender function that will drain the queue of pending renders\n * @returns {() => void}\n */\nexport function setupRerender() {\n\toptions.__test__previousDebounce = options.debounceRendering;\n\toptions.debounceRendering = cb => (options.__test__drainQueue = cb);\n\treturn () => options.__test__drainQueue && options.__test__drainQueue();\n}\n\nconst isThenable = value => value != null && typeof value.then == 'function';\n\n/** Depth of nested calls to `act`. */\nlet actDepth = 0;\n\n/**\n * Run a test function, and flush all effects and rerenders after invoking it.\n *\n * Returns a Promise which resolves \"immediately\" if the callback is\n * synchronous or when the callback's result resolves if it is asynchronous.\n *\n * @param {() => void|Promise<void>} cb The function under test. This may be sync or async.\n * @return {Promise<void>}\n */\nexport function act(cb) {\n\tif (++actDepth > 1) {\n\t\t// If calls to `act` are nested, a flush happens only when the\n\t\t// outermost call returns. In the inner call, we just execute the\n\t\t// callback and return since the infrastructure for flushing has already\n\t\t// been set up.\n\t\t//\n\t\t// If an exception occurs, the outermost `act` will handle cleanup.\n\t\tconst result = cb();\n\t\tif (isThenable(result)) {\n\t\t\treturn result.then(() => {\n\t\t\t\t--actDepth;\n\t\t\t});\n\t\t}\n\t\t--actDepth;\n\t\treturn Promise.resolve();\n\t}\n\n\tconst previousRequestAnimationFrame = options.requestAnimationFrame;\n\tconst rerender = setupRerender();\n\n\t/** @type {() => void} */\n\tlet flush, toFlush;\n\n\t// Override requestAnimationFrame so we can flush pending hooks.\n\toptions.requestAnimationFrame = fc => (flush = fc);\n\n\tconst finish = () => {\n\t\ttry {\n\t\t\trerender();\n\t\t\twhile (flush) {\n\t\t\t\ttoFlush = flush;\n\t\t\t\tflush = null;\n\n\t\t\t\ttoFlush();\n\t\t\t\trerender();\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tif (!err) {\n\t\t\t\terr = e;\n\t\t\t}\n\t\t} finally {\n\t\t\tteardown();\n\t\t}\n\n\t\toptions.requestAnimationFrame = previousRequestAnimationFrame;\n\t\t--actDepth;\n\t};\n\n\tlet err;\n\tlet result;\n\n\ttry {\n\t\tresult = cb();\n\t} catch (e) {\n\t\terr = e;\n\t}\n\n\tif (isThenable(result)) {\n\t\treturn result.then(finish, err => {\n\t\t\tfinish();\n\t\t\tthrow err;\n\t\t});\n\t}\n\n\t// nb. If the callback is synchronous, effects must be flushed before\n\t// `act` returns, so that the caller does not have to await the result,\n\t// even though React recommends this.\n\tfinish();\n\tif (err) {\n\t\tthrow err;\n\t}\n\treturn Promise.resolve();\n}\n\n/**\n * Teardown test environment and reset preact's internal state\n */\nexport function teardown() {\n\tif (options.__test__drainQueue) {\n\t\t// Flush any pending updates leftover by test\n\t\toptions.__test__drainQueue();\n\t\tdelete options.__test__drainQueue;\n\t}\n\n\tif (typeof options.__test__previousDebounce != 'undefined') {\n\t\toptions.debounceRendering = options.__test__previousDebounce;\n\t\tdelete options.__test__previousDebounce;\n\t} else {\n\t\toptions.debounceRendering = undefined;\n\t}\n}\n"], "names": ["setup<PERSON><PERSON>nder", "options", "__test__previousDebounce", "debounceRendering", "cb", "__test__drainQueue", "isThenable", "value", "then", "actDepth", "teardown", "undefined", "result", "Promise", "resolve", "flush", "toFlush", "previousRequestAnimationFrame", "requestAnimationFrame", "rerender", "fc", "err", "finish", "e"], "mappings": "wRAMgBA,IAGf,OAFAC,EAAOA,QAACC,EAA2BD,EAAAA,QAAQE,kBAC3CF,EAAOA,QAACE,kBAAoB,SAAAC,UAAOH,UAAQI,EAAqBD,CAAlC,EACvB,WAAA,OAAMH,EAAOA,QAACI,GAAsBJ,UAAQI,GAA5C,CACP,CAED,IAAMC,EAAa,SAAAC,GAAK,OAAa,MAATA,GAAsC,mBAAdA,EAAMC,IAAlC,EAGpBC,EAAW,EAyFR,SAASC,IACXT,EAAOA,QAACI,IAEXJ,EAAOA,QAACI,WACDJ,EAAAA,QAAQI,QAG+B,IAApCJ,UAAQC,GAClBD,EAAAA,QAAQE,kBAAoBF,EAAOA,QAACC,SAC7BD,EAAOA,QAACC,GAEfD,EAAAA,QAAQE,uBAAoBQ,CAE7B,OA3FM,SAAaP,GACnB,KAAMK,EAAW,EAAG,CAOnB,IAAMG,EAASR,IACf,OAAIE,EAAWM,GACPA,EAAOJ,KAAK,aAChBC,CACF,MAEAA,EACKI,QAAQC,UACf,CAED,IAIIC,EAAOC,EAJLC,EAAgChB,EAAOA,QAACiB,sBACxCC,EAAWnB,IAMjBC,EAAOA,QAACiB,sBAAwB,SAAAE,GAAOL,OAAAA,EAAQK,CAAb,EAElC,IAsBIC,EACAT,EAvBEU,EAAS,WACd,IAEC,IADAH,IACOJ,GACNC,EAAUD,EACVA,EAAQ,KAERC,IACAG,GAQD,CANC,MAAOI,GACHF,IACJA,EAAME,EAEP,CAbD,QAcCb,GACA,CAEDT,EAAOA,QAACiB,sBAAwBD,IAC9BR,CACF,EAKD,IACCG,EAASR,GAGT,CAFC,MAAOmB,GACRF,EAAME,CACN,CAED,GAAIjB,EAAWM,GACd,OAAOA,EAAOJ,KAAKc,EAAQ,SAAAD,GAE1B,MADAC,IACMD,CACN,GAOF,GADAC,IACID,EACH,MAAMA,EAEP,OAAOR,QAAQC,SACf"}