export type OAuthProviderType = "42-school" | "apple" | "asgardeo" | "auth0" | "authentik" | "azure-ad-b2c" | "azure-ad" | "azure-devops" | "bankid-no" | "battlenet" | "beyondidentity" | "box" | "boxyhq-saml" | "bungie" | "click-up" | "cognito" | "coinbase" | "descope" | "discord" | "dribbble" | "dropbox" | "duende-identity-server6" | "eveonline" | "facebook" | "faceit" | "foursquare" | "freshbooks" | "fusionauth" | "github" | "gitlab" | "google" | "hubspot" | "identity-server4" | "instagram" | "kakao" | "keycloak" | "line" | "linkedin" | "mailchimp" | "mailru" | "mastodon" | "mattermost" | "medium" | "microsoft-entra-id" | "naver" | "netlify" | "netsuite" | "nodemailer" | "notion" | "okta" | "onelogin" | "ory-hydra" | "osso" | "osu" | "passage" | "passkey" | "patreon" | "pinterest" | "pipedrive" | "postmark" | "reddit" | "resend" | "salesforce" | "sendgrid" | "simplelogin" | "slack" | "spotify" | "strava" | "threads" | "tiktok" | "todoist" | "trakt" | "twitch" | "twitter" | "united-effects" | "vk" | "webauthn" | "webex" | "wikimedia" | "wordpress" | "workos" | "yandex" | "zitadel" | "zoho" | "zoom";
//# sourceMappingURL=oauth-types.d.ts.map