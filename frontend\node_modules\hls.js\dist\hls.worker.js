!function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t,r={exports:{}};var a=(t||(t=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function a(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function n(e,t,a,n,s){if("function"!=typeof a)throw new TypeError("The listener must be a function");var o=new i(a,n||e,s),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],o]:e._events[u].push(o):(e._events[u]=o,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new a:delete e._events[t]}function o(){this._events=new a,this._eventsCount=0}Object.create&&(a.prototype=Object.create(null),(new a).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,a,i=[];if(0===this._eventsCount)return i;for(a in e=this._events)t.call(e,a)&&i.push(r?a.slice(1):a);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,a=this._events[t];if(!a)return[];if(a.fn)return[a.fn];for(var i=0,n=a.length,s=new Array(n);i<n;i++)s[i]=a[i].fn;return s},o.prototype.listenerCount=function(e){var t=r?r+e:e,a=this._events[t];return a?a.fn?1:a.length:0},o.prototype.emit=function(e,t,a,i,n,s){var o=r?r+e:e;if(!this._events[o])return!1;var u,d,l=this._events[o],c=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),c){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,a),!0;case 4:return l.fn.call(l.context,t,a,i),!0;case 5:return l.fn.call(l.context,t,a,i,n),!0;case 6:return l.fn.call(l.context,t,a,i,n,s),!0}for(d=1,u=new Array(c-1);d<c;d++)u[d-1]=arguments[d];l.fn.apply(l.context,u)}else{var h,f=l.length;for(d=0;d<f;d++)switch(l[d].once&&this.removeListener(e,l[d].fn,void 0,!0),c){case 1:l[d].fn.call(l[d].context);break;case 2:l[d].fn.call(l[d].context,t);break;case 3:l[d].fn.call(l[d].context,t,a);break;case 4:l[d].fn.call(l[d].context,t,a,i);break;default:if(!u)for(h=1,u=new Array(c-1);h<c;h++)u[h-1]=arguments[h];l[d].fn.apply(l[d].context,u)}}return!0},o.prototype.on=function(e,t,r){return n(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return n(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,a,i){var n=r?r+e:e;if(!this._events[n])return this;if(!t)return s(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||i&&!o.once||a&&o.context!==a||s(this,n);else{for(var u=0,d=[],l=o.length;u<l;u++)(o[u].fn!==t||i&&!o[u].once||a&&o[u].context!==a)&&d.push(o[u]);d.length?this._events[n]=1===d.length?d[0]:d:s(this,n)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&s(this,t)):(this._events=new a,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o}(r)),r.exports),i=e(a);function n(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t);if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},s.apply(null,arguments)}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,l(e,t)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}function c(e,t){return t+10<=e.length&&51===e[t]&&68===e[t+1]&&73===e[t+2]&&e[t+3]<255&&e[t+4]<255&&e[t+6]<128&&e[t+7]<128&&e[t+8]<128&&e[t+9]<128}function h(e,t){return t+10<=e.length&&73===e[t]&&68===e[t+1]&&51===e[t+2]&&e[t+3]<255&&e[t+4]<255&&e[t+6]<128&&e[t+7]<128&&e[t+8]<128&&e[t+9]<128}function f(e,t){var r=0;return r=(127&e[t])<<21,r|=(127&e[t+1])<<14,r|=(127&e[t+2])<<7,r|=127&e[t+3]}function p(e,t){for(var r=t,a=0;h(e,t);){a+=10,a+=f(e,t+6),c(e,t+10)&&(a+=10),t+=a}if(a>0)return e.subarray(r,r+a)}var m=function(e){return e.NETWORK_ERROR="networkError",e.MEDIA_ERROR="mediaError",e.KEY_SYSTEM_ERROR="keySystemError",e.MUX_ERROR="muxError",e.OTHER_ERROR="otherError",e}({}),v=function(e){return e.KEY_SYSTEM_NO_KEYS="keySystemNoKeys",e.KEY_SYSTEM_NO_ACCESS="keySystemNoAccess",e.KEY_SYSTEM_NO_SESSION="keySystemNoSession",e.KEY_SYSTEM_NO_CONFIGURED_LICENSE="keySystemNoConfiguredLicense",e.KEY_SYSTEM_LICENSE_REQUEST_FAILED="keySystemLicenseRequestFailed",e.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED="keySystemServerCertificateRequestFailed",e.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED="keySystemServerCertificateUpdateFailed",e.KEY_SYSTEM_SESSION_UPDATE_FAILED="keySystemSessionUpdateFailed",e.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED="keySystemStatusOutputRestricted",e.KEY_SYSTEM_STATUS_INTERNAL_ERROR="keySystemStatusInternalError",e.KEY_SYSTEM_DESTROY_MEDIA_KEYS_ERROR="keySystemDestroyMediaKeysError",e.KEY_SYSTEM_DESTROY_CLOSE_SESSION_ERROR="keySystemDestroyCloseSessionError",e.KEY_SYSTEM_DESTROY_REMOVE_SESSION_ERROR="keySystemDestroyRemoveSessionError",e.MANIFEST_LOAD_ERROR="manifestLoadError",e.MANIFEST_LOAD_TIMEOUT="manifestLoadTimeOut",e.MANIFEST_PARSING_ERROR="manifestParsingError",e.MANIFEST_INCOMPATIBLE_CODECS_ERROR="manifestIncompatibleCodecsError",e.LEVEL_EMPTY_ERROR="levelEmptyError",e.LEVEL_LOAD_ERROR="levelLoadError",e.LEVEL_LOAD_TIMEOUT="levelLoadTimeOut",e.LEVEL_PARSING_ERROR="levelParsingError",e.LEVEL_SWITCH_ERROR="levelSwitchError",e.AUDIO_TRACK_LOAD_ERROR="audioTrackLoadError",e.AUDIO_TRACK_LOAD_TIMEOUT="audioTrackLoadTimeOut",e.SUBTITLE_LOAD_ERROR="subtitleTrackLoadError",e.SUBTITLE_TRACK_LOAD_TIMEOUT="subtitleTrackLoadTimeOut",e.FRAG_LOAD_ERROR="fragLoadError",e.FRAG_LOAD_TIMEOUT="fragLoadTimeOut",e.FRAG_DECRYPT_ERROR="fragDecryptError",e.FRAG_PARSING_ERROR="fragParsingError",e.FRAG_GAP="fragGap",e.REMUX_ALLOC_ERROR="remuxAllocError",e.KEY_LOAD_ERROR="keyLoadError",e.KEY_LOAD_TIMEOUT="keyLoadTimeOut",e.BUFFER_ADD_CODEC_ERROR="bufferAddCodecError",e.BUFFER_INCOMPATIBLE_CODECS_ERROR="bufferIncompatibleCodecsError",e.BUFFER_APPEND_ERROR="bufferAppendError",e.BUFFER_APPENDING_ERROR="bufferAppendingError",e.BUFFER_STALLED_ERROR="bufferStalledError",e.BUFFER_FULL_ERROR="bufferFullError",e.BUFFER_SEEK_OVER_HOLE="bufferSeekOverHole",e.BUFFER_NUDGE_ON_STALL="bufferNudgeOnStall",e.ASSET_LIST_LOAD_ERROR="assetListLoadError",e.ASSET_LIST_LOAD_TIMEOUT="assetListLoadTimeout",e.ASSET_LIST_PARSING_ERROR="assetListParsingError",e.INTERSTITIAL_ASSET_ITEM_ERROR="interstitialAssetItemError",e.INTERNAL_EXCEPTION="internalException",e.INTERNAL_ABORTED="aborted",e.ATTACH_MEDIA_ERROR="attachMediaError",e.UNKNOWN="unknown",e}({}),y=function(e){return e.MEDIA_ATTACHING="hlsMediaAttaching",e.MEDIA_ATTACHED="hlsMediaAttached",e.MEDIA_DETACHING="hlsMediaDetaching",e.MEDIA_DETACHED="hlsMediaDetached",e.MEDIA_ENDED="hlsMediaEnded",e.STALL_RESOLVED="hlsStallResolved",e.BUFFER_RESET="hlsBufferReset",e.BUFFER_CODECS="hlsBufferCodecs",e.BUFFER_CREATED="hlsBufferCreated",e.BUFFER_APPENDING="hlsBufferAppending",e.BUFFER_APPENDED="hlsBufferAppended",e.BUFFER_EOS="hlsBufferEos",e.BUFFERED_TO_END="hlsBufferedToEnd",e.BUFFER_FLUSHING="hlsBufferFlushing",e.BUFFER_FLUSHED="hlsBufferFlushed",e.MANIFEST_LOADING="hlsManifestLoading",e.MANIFEST_LOADED="hlsManifestLoaded",e.MANIFEST_PARSED="hlsManifestParsed",e.LEVEL_SWITCHING="hlsLevelSwitching",e.LEVEL_SWITCHED="hlsLevelSwitched",e.LEVEL_LOADING="hlsLevelLoading",e.LEVEL_LOADED="hlsLevelLoaded",e.LEVEL_UPDATED="hlsLevelUpdated",e.LEVEL_PTS_UPDATED="hlsLevelPtsUpdated",e.LEVELS_UPDATED="hlsLevelsUpdated",e.AUDIO_TRACKS_UPDATED="hlsAudioTracksUpdated",e.AUDIO_TRACK_SWITCHING="hlsAudioTrackSwitching",e.AUDIO_TRACK_SWITCHED="hlsAudioTrackSwitched",e.AUDIO_TRACK_LOADING="hlsAudioTrackLoading",e.AUDIO_TRACK_LOADED="hlsAudioTrackLoaded",e.AUDIO_TRACK_UPDATED="hlsAudioTrackUpdated",e.SUBTITLE_TRACKS_UPDATED="hlsSubtitleTracksUpdated",e.SUBTITLE_TRACKS_CLEARED="hlsSubtitleTracksCleared",e.SUBTITLE_TRACK_SWITCH="hlsSubtitleTrackSwitch",e.SUBTITLE_TRACK_LOADING="hlsSubtitleTrackLoading",e.SUBTITLE_TRACK_LOADED="hlsSubtitleTrackLoaded",e.SUBTITLE_TRACK_UPDATED="hlsSubtitleTrackUpdated",e.SUBTITLE_FRAG_PROCESSED="hlsSubtitleFragProcessed",e.CUES_PARSED="hlsCuesParsed",e.NON_NATIVE_TEXT_TRACKS_FOUND="hlsNonNativeTextTracksFound",e.INIT_PTS_FOUND="hlsInitPtsFound",e.FRAG_LOADING="hlsFragLoading",e.FRAG_LOAD_EMERGENCY_ABORTED="hlsFragLoadEmergencyAborted",e.FRAG_LOADED="hlsFragLoaded",e.FRAG_DECRYPTED="hlsFragDecrypted",e.FRAG_PARSING_INIT_SEGMENT="hlsFragParsingInitSegment",e.FRAG_PARSING_USERDATA="hlsFragParsingUserdata",e.FRAG_PARSING_METADATA="hlsFragParsingMetadata",e.FRAG_PARSED="hlsFragParsed",e.FRAG_BUFFERED="hlsFragBuffered",e.FRAG_CHANGED="hlsFragChanged",e.FPS_DROP="hlsFpsDrop",e.FPS_DROP_LEVEL_CAPPING="hlsFpsDropLevelCapping",e.MAX_AUTO_LEVEL_UPDATED="hlsMaxAutoLevelUpdated",e.ERROR="hlsError",e.DESTROYING="hlsDestroying",e.KEY_LOADING="hlsKeyLoading",e.KEY_LOADED="hlsKeyLoaded",e.LIVE_BACK_BUFFER_REACHED="hlsLiveBackBufferReached",e.BACK_BUFFER_REACHED="hlsBackBufferReached",e.STEERING_MANIFEST_LOADED="hlsSteeringManifestLoaded",e.ASSET_LIST_LOADING="hlsAssetListLoading",e.ASSET_LIST_LOADED="hlsAssetListLoaded",e.INTERSTITIALS_UPDATED="hlsInterstitialsUpdated",e.INTERSTITIALS_BUFFERED_TO_BOUNDARY="hlsInterstitialsBufferedToBoundary",e.INTERSTITIAL_ASSET_PLAYER_CREATED="hlsInterstitialAssetPlayerCreated",e.INTERSTITIAL_STARTED="hlsInterstitialStarted",e.INTERSTITIAL_ASSET_STARTED="hlsInterstitialAssetStarted",e.INTERSTITIAL_ASSET_ENDED="hlsInterstitialAssetEnded",e.INTERSTITIAL_ASSET_ERROR="hlsInterstitialAssetError",e.INTERSTITIAL_ENDED="hlsInterstitialEnded",e.INTERSTITIALS_PRIMARY_RESUMED="hlsInterstitialsPrimaryResumed",e.PLAYOUT_LIMIT_REACHED="hlsPlayoutLimitReached",e.EVENT_CUE_ENTER="hlsEventCueEnter",e}({}),g=function(e,t){this.trace=void 0,this.debug=void 0,this.log=void 0,this.warn=void 0,this.info=void 0,this.error=void 0;var r="["+e+"]:";this.trace=S,this.debug=t.debug.bind(null,r),this.log=t.log.bind(null,r),this.warn=t.warn.bind(null,r),this.info=t.info.bind(null,r),this.error=t.error.bind(null,r)},S=function(){},T={trace:S,debug:S,log:S,warn:S,info:S,error:S};function E(){return s({},T)}function b(e,t,r){return t[e]?t[e].bind(t):(a=e,(i=self.console[a])?i.bind(self.console,"["+a+"] >"):S);var a,i}var A=E();var _=A;function R(e,t){return 255===e[t]&&240==(246&e[t+1])}function k(e,t){return 1&e[t+1]?7:9}function w(e,t){return(3&e[t+3])<<11|e[t+4]<<3|(224&e[t+5])>>>5}function I(e,t){return t+1<e.length&&R(e,t)}function U(e,t){if(I(e,t)){var r=k(e,t);if(t+r>=e.length)return!1;var a=w(e,t);if(a<=r)return!1;var i=t+a;return i===e.length||I(e,i)}return!1}function x(e,t,r,a,i){if(!e.samplerate){var n=function(e,t,r,a){var i=t[r+2],n=i>>2&15;if(!(n>12)){var s=1+(i>>6&3),o=t[r+3]>>6&3|(1&i)<<2,u="mp4a.40."+s,d=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350][n],l=n;5!==s&&29!==s||(l-=3);var c=[s<<3|(14&l)>>1,(1&l)<<7|o<<3];return _.log("manifest codec:"+a+", parsed codec:"+u+", channels:"+o+", rate:"+d+" (ADTS object type:"+s+" sampling index:"+n+")"),{config:c,samplerate:d,channelCount:o,codec:u,parsedCodec:u,manifestCodec:a}}var h=new Error("invalid ADTS sampling index:"+n);e.emit(y.ERROR,y.ERROR,{type:m.MEDIA_ERROR,details:v.FRAG_PARSING_ERROR,fatal:!0,error:h,reason:h.message})}(t,r,a,i);if(!n)return;s(e,n)}}function P(e){return 9216e4/e}function D(e,t,r,a,i){var n,s=a+i*P(e.samplerate),o=function(e,t){var r=k(e,t);if(t+r<=e.length){var a=w(e,t)-r;if(a>0)return{headerLength:r,frameLength:a}}}(t,r);if(o){var u=o.frameLength,d=o.headerLength,l=d+u,c=Math.max(0,r+l-t.length);c?(n=new Uint8Array(l-d)).set(t.subarray(r+d,t.length),0):n=t.subarray(r+d,r+l);var h={unit:n,pts:s};return c||e.samples.push(h),{sample:h,length:l,missing:c}}var f=t.length-r;return(n=new Uint8Array(f)).set(t.subarray(r,t.length),0),{sample:{unit:n,pts:s},length:f,missing:-1}}var C=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},L=Number.isSafeInteger||function(e){return"number"==typeof e&&Math.abs(e)<=O},O=Number.MAX_SAFE_INTEGER||9007199254740991;function B(e,t){return h(e,t)&&f(e,t+6)+10<=e.length-t}function M(e,t){if(void 0===t&&(t=!1),"undefined"!=typeof TextDecoder){var r=new TextDecoder("utf-8").decode(e);if(t){var a=r.indexOf("\0");return-1!==a?r.substring(0,a):r}return r.replace(/\0/g,"")}for(var i,n,s,o=e.length,u="",d=0;d<o;){if(0===(i=e[d++])&&t)return u;if(0!==i&&3!==i)switch(i>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:u+=String.fromCharCode(i);break;case 12:case 13:n=e[d++],u+=String.fromCharCode((31&i)<<6|63&n);break;case 14:n=e[d++],s=e[d++],u+=String.fromCharCode((15&i)<<12|(63&n)<<6|(63&s)<<0)}}return u}function N(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=1/0),function(e,t,r,a){var i=function(e){return e instanceof ArrayBuffer?e:e.buffer}(e),n=1;"BYTES_PER_ELEMENT"in a&&(n=a.BYTES_PER_ELEMENT);var s=(c=e,c&&c.buffer instanceof ArrayBuffer&&void 0!==c.byteLength&&void 0!==c.byteOffset?e.byteOffset:0),o=(s+e.byteLength)/n,u=(s+t)/n,d=Math.floor(Math.max(0,Math.min(u,o))),l=Math.floor(Math.min(d+Math.max(r,0),o));var c;return new a(i,d,l-d)}(e,t,r,Uint8Array)}function F(e){var t={key:e.type,description:"",data:"",mimeType:null,pictureType:null};if(!(e.size<2))if(3===e.data[0]){var r=e.data.subarray(1).indexOf(0);if(-1!==r){var a=M(N(e.data,1,r)),i=e.data[2+r],n=e.data.subarray(3+r).indexOf(0);if(-1!==n){var s,o=M(N(e.data,3+r,n));return s="--\x3e"===a?M(N(e.data,4+r+n)):function(e){return e instanceof ArrayBuffer?e:0==e.byteOffset&&e.byteLength==e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer}(e.data.subarray(4+r+n)),t.mimeType=a,t.pictureType=i,t.description=o,t.data=s,t}}}else console.log("Ignore frame with unrecognized character encoding")}function G(e){return"PRIV"===e.type?function(e){if(!(e.size<2)){var t=M(e.data,!0),r=new Uint8Array(e.data.subarray(t.length+1));return{key:e.type,info:t,data:r.buffer}}}(e):"W"===e.type[0]?function(e){if("WXXX"===e.type){if(e.size<2)return;var t=1,r=M(e.data.subarray(t),!0);t+=r.length+1;var a=M(e.data.subarray(t));return{key:e.type,info:r,data:a}}var i=M(e.data);return{key:e.type,info:"",data:i}}(e):"APIC"===e.type?F(e):function(e){if(!(e.size<2)){if("TXXX"===e.type){var t=1,r=M(e.data.subarray(t),!0);t+=r.length+1;var a=M(e.data.subarray(t));return{key:e.type,info:r,data:a}}var i=M(e.data.subarray(1));return{key:e.type,info:"",data:i}}}(e)}function V(e){var t=String.fromCharCode(e[0],e[1],e[2],e[3]),r=f(e,4);return{type:t,size:r,data:e.subarray(10,10+r)}}var K=10,Y=10;function j(e){return e&&"PRIV"===e.key&&"com.apple.streaming.transportStreamTimestamp"===e.info}function z(e){if(8===e.data.byteLength){var t=new Uint8Array(e.data),r=1&t[3],a=(t[4]<<23)+(t[5]<<15)+(t[6]<<7)+t[7];return a/=45,r&&(a+=47721858.84),Math.round(a)}}function H(e){for(var t=function(e){for(var t=0,r=[];h(e,t);){var a=f(e,t+6);e[t+5]>>6&1&&(t+=K);for(var i=(t+=K)+a;t+Y<i;){var n=V(e.subarray(t)),s=G(n);s&&r.push(s),t+=n.size+K}c(e,t)&&(t+=K)}return r}(e),r=0;r<t.length;r++){var a=t[r];if(j(a))return z(a)}}var W,q,X,Z,J,Q,$=function(e){return e.audioId3="org.id3",e.dateRange="com.apple.quicktime.HLS",e.emsg="https://aomedia.org/emsg/ID3",e.misbklv="urn:misb:KLV:bin:1910.1",e}({}),ee=function(e){for(var t="",r=0;r<e.length;r++){var a=e[r].toString(16);a.length<2&&(a="0"+a),t+=a}return t},te={exports:{}};W||(W=1,q=/^(?=((?:[a-zA-Z0-9+\-.]+:)?))\1(?=((?:\/\/[^\/?#]*)?))\2(?=((?:(?:[^?#\/]*\/)*[^;?#\/]*)?))\3((?:;[^?#]*)?)(\?[^#]*)?(#[^]*)?$/,X=/^(?=([^\/?#]*))\1([^]*)$/,Z=/(?:\/|^)\.(?=\/)/g,J=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,te.exports=Q={buildAbsoluteURL:function(e,t,r){if(r=r||{},e=e.trim(),!(t=t.trim())){if(!r.alwaysNormalize)return e;var a=Q.parseURL(e);if(!a)throw new Error("Error trying to parse base URL.");return a.path=Q.normalizePath(a.path),Q.buildURLFromParts(a)}var i=Q.parseURL(t);if(!i)throw new Error("Error trying to parse relative URL.");if(i.scheme)return r.alwaysNormalize?(i.path=Q.normalizePath(i.path),Q.buildURLFromParts(i)):t;var n=Q.parseURL(e);if(!n)throw new Error("Error trying to parse base URL.");if(!n.netLoc&&n.path&&"/"!==n.path[0]){var s=X.exec(n.path);n.netLoc=s[1],n.path=s[2]}n.netLoc&&!n.path&&(n.path="/");var o={scheme:n.scheme,netLoc:i.netLoc,path:null,params:i.params,query:i.query,fragment:i.fragment};if(!i.netLoc&&(o.netLoc=n.netLoc,"/"!==i.path[0]))if(i.path){var u=n.path,d=u.substring(0,u.lastIndexOf("/")+1)+i.path;o.path=Q.normalizePath(d)}else o.path=n.path,i.params||(o.params=n.params,i.query||(o.query=n.query));return null===o.path&&(o.path=r.alwaysNormalize?Q.normalizePath(i.path):i.path),Q.buildURLFromParts(o)},parseURL:function(e){var t=q.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(Z,"");e.length!==(e=e.replace(J,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}});var re="audio",ae="video",ie=Math.pow(2,32)-1,ne=[].push,se={video:1,audio:2,id3:3,text:4};function oe(e){return String.fromCharCode.apply(null,e)}function ue(e,t){var r=e[t]<<8|e[t+1];return r<0?65536+r:r}function de(e,t){var r=ce(e,t);return r<0?4294967296+r:r}function le(e,t){var r=de(e,t);return r*=Math.pow(2,32),r+=de(e,t+4)}function ce(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]}function he(e,t){var r=[];if(!t.length)return r;for(var a=e.byteLength,i=0;i<a;){var n=de(e,i),s=n>1?i+n:a;if(oe(e.subarray(i+4,i+8))===t[0])if(1===t.length)r.push(e.subarray(i+8,s));else{var o=he(e.subarray(i+8,s),t.slice(1));o.length&&ne.apply(r,o)}i=s}return r}function fe(e){var t=[],r=e[0],a=8,i=de(e,a);a+=4;var n=0,s=0;0===r?(n=de(e,a),s=de(e,a+4),a+=8):(n=le(e,a),s=le(e,a+8),a+=16),a+=2;var o=e.length+s,u=ue(e,a);a+=2;for(var d=0;d<u;d++){var l=a,c=de(e,l);l+=4;var h=2147483647&c;if(1===(2147483648&c)>>>31)return _.warn("SIDX has hierarchical references (not supported)"),null;var f=de(e,l);l+=4,t.push({referenceSize:h,subsegmentDuration:f,info:{duration:f/i,start:o,end:o+h-1}}),o+=h,a=l+=4}return{earliestPresentationTime:n,timescale:i,version:r,referencesCount:u,references:t}}function pe(e){for(var t=[],r=he(e,["moov","trak"]),a=0;a<r.length;a++){var i=r[a],n=he(i,["tkhd"])[0];if(n){var s=n[0],o=de(n,0===s?12:20),u=he(i,["mdia","mdhd"])[0];if(u){var l=de(u,0===(s=u[0])?12:20),c=he(i,["mdia","hdlr"])[0];if(c){var h=oe(c.subarray(8,12)),f={soun:re,vide:ae}[h],p=me(he(i,["mdia","minf","stbl","stsd"])[0]);f?(t[o]={timescale:l,type:f,stsd:p},t[f]=d({timescale:l,id:o},p)):t[o]={timescale:l,type:h,stsd:p}}}}}return he(e,["moov","mvex","trex"]).forEach((function(e){var r=de(e,4),a=t[r];a&&(a.default={duration:de(e,12),flags:de(e,20)})})),t}function me(e){var t,r=e.subarray(8),a=r.subarray(86),i=oe(r.subarray(4,8)),n=i,s="enca"===i||"encv"===i;if(s){var o=he(r,[i])[0];he(o.subarray("enca"===i?28:78),["sinf"]).forEach((function(e){var t=he(e,["schm"])[0];if(t){var r=oe(t.subarray(4,8));if("cbcs"===r||"cenc"===r){var a=he(e,["frma"])[0];a&&(n=oe(a))}}}))}var u=n;switch(n){case"avc1":case"avc2":case"avc3":case"avc4":var d=he(a,["avcC"])[0];d&&d.length>3&&(n+="."+ge(d[1])+ge(d[2])+ge(d[3]),t=ve("avc1"===u?"dva1":"dvav",a));break;case"mp4a":var l=he(r,[i])[0],c=he(l.subarray(28),["esds"])[0];if(c&&c.length>7){var h=4;if(3!==c[h++])break;h=ye(c,h),h+=2;var f=c[h++];if(128&f&&(h+=2),64&f&&(h+=c[h++]),4!==c[h++])break;h=ye(c,h);var p=c[h++];if(64!==p)break;if(n+="."+ge(p),h+=12,5!==c[h++])break;h=ye(c,h);var m=c[h++],v=(248&m)>>3;31===v&&(v+=1+((7&m)<<3)+((224&c[h])>>5)),n+="."+v}break;case"hvc1":case"hev1":var y=he(a,["hvcC"])[0];if(y&&y.length>12){var g=y[1],S=["","A","B","C"][g>>6],T=31&g,E=de(y,2),b=(32&g)>>5?"H":"L",A=y[12],_=y.subarray(6,12);n+="."+S+T,n+="."+function(e){for(var t=0,r=0;r<32;r++)t|=(e>>r&1)<<31-r;return t>>>0}(E).toString(16).toUpperCase(),n+="."+b+A;for(var R="",k=_.length;k--;){var w=_[k];if(w||R)R="."+w.toString(16).toUpperCase()+R}n+=R}t=ve("hev1"==u?"dvhe":"dvh1",a);break;case"dvh1":case"dvhe":case"dvav":case"dva1":case"dav1":n=ve(n,a)||n;break;case"vp09":var I=he(a,["vpcC"])[0];if(I&&I.length>6){var U=I[4],x=I[5],P=I[6]>>4&15;n+="."+Se(U)+"."+Se(x)+"."+Se(P)}break;case"av01":var D=he(a,["av1C"])[0];if(D&&D.length>2){var C=D[1]>>>5,L=31&D[1],O=D[2]>>>7?"H":"M",B=(64&D[2])>>6,M=(32&D[2])>>5,N=2===C&&B?M?12:10:B?10:8,F=(16&D[2])>>4,G=(8&D[2])>>3,V=(4&D[2])>>2,K=3&D[2];n+="."+C+"."+Se(L)+O+"."+Se(N)+"."+F+"."+G+V+K+"."+Se(1)+"."+Se(1)+"."+Se(1)+".0",t=ve("dav1",a)}}return{codec:n,encrypted:s,supplemental:t}}function ve(e,t){var r=he(t,["dvvC"]),a=r.length?r[0]:he(t,["dvcC"])[0];if(a){var i=a[2]>>1&127,n=a[2]<<5&32|a[3]>>3&31;return e+"."+Se(i)+"."+Se(n)}}function ye(e,t){for(var r=t+5;128&e[t++]&&t<r;);return t}function ge(e){return("0"+e.toString(16).toUpperCase()).slice(-2)}function Se(e){return(e<10?"0":"")+e}function Te(e,t){if(e&&t){var r=t.keyId;if(r&&t.isCommonEncryption)he(e,["moov","trak"]).forEach((function(e){var t=he(e,["mdia","minf","stbl","stsd"])[0].subarray(8),a=he(t,["enca"]),i=a.length>0;i||(a=he(t,["encv"])),a.forEach((function(e){he(i?e.subarray(28):e.subarray(78),["sinf"]).forEach((function(e){var t=function(e){var t=he(e,["schm"])[0];if(t){var r=oe(t.subarray(4,8));if("cbcs"===r||"cenc"===r)return he(e,["schi","tenc"])[0]}return null}(e);if(t){var a=t.subarray(8,24);a.some((function(e){return 0!==e}))||(_.log("[eme] Patching keyId in 'enc"+(i?"a":"v")+">sinf>>tenc' box: "+ee(a)+" -> "+ee(r)),t.set(r,8))}}))}))}))}}function Ee(e,t){var r=new Uint8Array(e.length+t.length);return r.set(e),r.set(t,e.length),r}function be(e,t){var r=[],a=t.samples,i=t.timescale,n=t.id,s=!1;return he(a,["moof"]).map((function(o){var u=o.byteOffset-8;he(o,["traf"]).map((function(o){var d=he(o,["tfdt"]).map((function(e){var t=e[0],r=de(e,4);return 1===t&&(r*=Math.pow(2,32),r+=de(e,8)),r/i}))[0];return void 0!==d&&(e=d),he(o,["tfhd"]).map((function(d){var l=de(d,4),c=16777215&de(d,0),h=0,f=0!=(16&c),p=0,m=0!=(32&c),v=8;l===n&&(0!=(1&c)&&(v+=8),0!=(2&c)&&(v+=4),0!=(8&c)&&(h=de(d,v),v+=4),f&&(p=de(d,v),v+=4),m&&(v+=4),"video"===t.type&&(s=function(e){if(!e)return!1;var t=e.substring(0,4);return"hvc1"===t||"hev1"===t||"dvh1"===t||"dvhe"===t}(t.codec)),he(o,["trun"]).map((function(n){var o=n[0],d=16777215&de(n,0),l=0!=(1&d),c=0,f=0!=(4&d),m=0!=(256&d),v=0,y=0!=(512&d),g=0,S=0!=(1024&d),T=0!=(2048&d),E=0,b=de(n,4),A=8;l&&(c=de(n,A),A+=4),f&&(A+=4);for(var _=c+u,R=0;R<b;R++){if(m?(v=de(n,A),A+=4):v=h,y?(g=de(n,A),A+=4):g=p,S&&(A+=4),T&&(E=0===o?de(n,A):ce(n,A),A+=4),t.type===ae)for(var k=0;k<g;){var w=de(a,_);if(Ae(s,a[_+=4]))_e(a.subarray(_,_+w),s?2:1,e+E/i,r);_+=w,k+=w+4}e+=v/i}})))}))}))})),r}function Ae(e,t){if(e){var r=t>>1&63;return 39===r||40===r}return 6===(31&t)}function _e(e,t,r,a){var i=Re(e),n=0;n+=t;for(var s=0,o=0,u=0;n<i.length;){s=0;do{if(n>=i.length)break;s+=u=i[n++]}while(255===u);o=0;do{if(n>=i.length)break;o+=u=i[n++]}while(255===u);var d=i.length-n,l=n;if(o<d)n+=o;else if(o>d){_.error("Malformed SEI payload. "+o+" is too small, only "+d+" bytes left to parse.");break}if(4===s){if(181===i[l++]){var c=ue(i,l);if(l+=2,49===c){var h=de(i,l);if(l+=4,1195456820===h){var f=i[l++];if(3===f){var p=i[l++],m=64&p,v=m?2+3*(31&p):0,y=new Uint8Array(v);if(m){y[0]=p;for(var g=1;g<v;g++)y[g]=i[l++]}a.push({type:f,payloadType:s,pts:r,bytes:y})}}}}}else if(5===s&&o>16){for(var S=[],T=0;T<16;T++){var E=i[l++].toString(16);S.push(1==E.length?"0"+E:E),3!==T&&5!==T&&7!==T&&9!==T||S.push("-")}for(var b=o-16,A=new Uint8Array(b),R=0;R<b;R++)A[R]=i[l++];a.push({payloadType:s,pts:r,uuid:S.join(""),userData:M(A),userDataBytes:A})}}}function Re(e){for(var t=e.byteLength,r=[],a=1;a<t-2;)0===e[a]&&0===e[a+1]&&3===e[a+2]?(r.push(a+2),a+=2):a++;if(0===r.length)return e;var i=t-r.length,n=new Uint8Array(i),s=0;for(a=0;a<i;s++,a++)s===r[0]&&(s++,r.shift()),n[a]=e[s];return n}function ke(e,t){return void 0===e&&(e=""),void 0===t&&(t=9e4),{type:e,id:-1,pid:-1,inputTimeScale:t,sequenceNumber:-1,samples:[],dropped:0}}var we=function(){function e(){this._audioTrack=void 0,this._id3Track=void 0,this.frameIndex=0,this.cachedData=null,this.basePTS=null,this.initPTS=null,this.lastPTS=null}var t=e.prototype;return t.resetInitSegment=function(e,t,r,a){this._id3Track={type:"id3",id:3,pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0}},t.resetTimeStamp=function(e){this.initPTS=e,this.resetContiguity()},t.resetContiguity=function(){this.basePTS=null,this.lastPTS=null,this.frameIndex=0},t.canParse=function(e,t){return!1},t.appendFrame=function(e,t,r){},t.demux=function(e,t){this.cachedData&&(e=Ee(this.cachedData,e),this.cachedData=null);var r,a=p(e,0),i=a?a.length:0,n=this._audioTrack,s=this._id3Track,o=a?H(a):void 0,u=e.length;for((null===this.basePTS||0===this.frameIndex&&C(o))&&(this.basePTS=Ie(o,t,this.initPTS),this.lastPTS=this.basePTS),null===this.lastPTS&&(this.lastPTS=this.basePTS),a&&a.length>0&&s.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:a,type:$.audioId3,duration:Number.POSITIVE_INFINITY});i<u;){if(this.canParse(e,i)){var d=this.appendFrame(n,e,i);d?(this.frameIndex++,this.lastPTS=d.sample.pts,r=i+=d.length):i=u}else B(e,i)?(a=p(e,i),s.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:a,type:$.audioId3,duration:Number.POSITIVE_INFINITY}),r=i+=a.length):i++;if(i===u&&r!==u){var l=e.slice(r);this.cachedData?this.cachedData=Ee(this.cachedData,l):this.cachedData=l}}return{audioTrack:n,videoTrack:ke(),id3Track:s,textTrack:ke()}},t.demuxSampleAes=function(e,t,r){return Promise.reject(new Error("["+this+"] This demuxer does not support Sample-AES decryption"))},t.flush=function(e){var t=this.cachedData;return t&&(this.cachedData=null,this.demux(t,0)),{audioTrack:this._audioTrack,videoTrack:ke(),id3Track:this._id3Track,textTrack:ke()}},t.destroy=function(){this.cachedData=null,this._audioTrack=this._id3Track=void 0},e}(),Ie=function(e,t,r){return C(e)?90*e:9e4*t+(r?9e4*r.baseTime/r.timescale:0)},Ue=null,xe=[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],Pe=[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],De=[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],Ce=[0,1,1,4];function Le(e,t,r,a,i){if(!(r+24>t.length)){var n=Oe(t,r);if(n&&r+n.frameLength<=t.length){var s=a+i*(9e4*n.samplesPerFrame/n.sampleRate),o={unit:t.subarray(r,r+n.frameLength),pts:s,dts:s};return e.config=[],e.channelCount=n.channelCount,e.samplerate=n.sampleRate,e.samples.push(o),{sample:o,length:n.frameLength,missing:0}}}}function Oe(e,t){var r=e[t+1]>>3&3,a=e[t+1]>>1&3,i=e[t+2]>>4&15,n=e[t+2]>>2&3;if(1!==r&&0!==i&&15!==i&&3!==n){var s=e[t+2]>>1&1,o=e[t+3]>>6,u=1e3*xe[14*(3===r?3-a:3===a?3:4)+i-1],d=Pe[3*(3===r?0:2===r?1:2)+n],l=3===o?1:2,c=De[r][a],h=Ce[a],f=8*c*h,p=Math.floor(c*u/d+s)*h;if(null===Ue){var m=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);Ue=m?parseInt(m[1]):0}return!!Ue&&Ue<=87&&2===a&&u>=224e3&&0===o&&(e[t+3]=128|e[t+3]),{sampleRate:d,channelCount:l,frameLength:p,samplesPerFrame:f}}}function Be(e,t){return 255===e[t]&&224==(224&e[t+1])&&0!=(6&e[t+1])}function Me(e,t){return t+1<e.length&&Be(e,t)}function Ne(e,t){if(t+1<e.length&&Be(e,t)){var r=Oe(e,t),a=4;null!=r&&r.frameLength&&(a=r.frameLength);var i=t+a;return i===e.length||Me(e,i)}return!1}var Fe=function(e){function t(t,r){var a;return(a=e.call(this)||this).observer=void 0,a.config=void 0,a.observer=t,a.config=r,a}o(t,e);var r=t.prototype;return r.resetInitSegment=function(t,r,a,i){e.prototype.resetInitSegment.call(this,t,r,a,i),this._audioTrack={container:"audio/adts",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"aac",samples:[],manifestCodec:r,duration:i,inputTimeScale:9e4,dropped:0}},t.probe=function(e,t){if(!e)return!1;var r=p(e,0),a=(null==r?void 0:r.length)||0;if(Ne(e,a))return!1;for(var i=e.length;a<i;a++)if(U(e,a))return t.log("ADTS sync word found !"),!0;return!1},r.canParse=function(e,t){return function(e,t){return function(e,t){return t+5<e.length}(e,t)&&R(e,t)&&w(e,t)<=e.length-t}(e,t)},r.appendFrame=function(e,t,r){x(e,this.observer,t,r,e.manifestCodec);var a=D(e,t,r,this.basePTS,this.frameIndex);if(a&&0===a.missing)return a},t}(we),Ge=function(e,t){var r=0,a=5;t+=a;for(var i=new Uint32Array(1),n=new Uint32Array(1),s=new Uint8Array(1);a>0;){s[0]=e[t];var o=Math.min(a,8),u=8-o;n[0]=4278190080>>>24+u<<u,i[0]=(s[0]&n[0])>>u,r=r?r<<o|i[0]:i[0],t+=1,a-=o}return r},Ve=function(e){function t(t){var r;return(r=e.call(this)||this).observer=void 0,r.observer=t,r}o(t,e);var r=t.prototype;return r.resetInitSegment=function(t,r,a,i){e.prototype.resetInitSegment.call(this,t,r,a,i),this._audioTrack={container:"audio/ac-3",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"ac3",samples:[],manifestCodec:r,duration:i,inputTimeScale:9e4,dropped:0}},r.canParse=function(e,t){return t+64<e.length},r.appendFrame=function(e,t,r){var a=Ke(e,t,r,this.basePTS,this.frameIndex);if(-1!==a)return{sample:e.samples[e.samples.length-1],length:a,missing:0}},t.probe=function(e){if(!e)return!1;var t=p(e,0);if(!t)return!1;var r=t.length;return 11===e[r]&&119===e[r+1]&&void 0!==H(t)&&Ge(e,r)<16},t}(we);function Ke(e,t,r,a,i){if(r+8>t.length)return-1;if(11!==t[r]||119!==t[r+1])return-1;var n=t[r+4]>>6;if(n>=3)return-1;var s=[48e3,44100,32e3][n],o=63&t[r+4],u=2*[64,69,96,64,70,96,80,87,120,80,88,120,96,104,144,96,105,144,112,121,168,112,122,168,128,139,192,128,140,192,160,174,240,160,175,240,192,208,288,192,209,288,224,243,336,224,244,336,256,278,384,256,279,384,320,348,480,320,349,480,384,417,576,384,418,576,448,487,672,448,488,672,512,557,768,512,558,768,640,696,960,640,697,960,768,835,1152,768,836,1152,896,975,1344,896,976,1344,1024,1114,1536,1024,1115,1536,1152,1253,1728,1152,1254,1728,1280,1393,1920,1280,1394,1920][3*o+n];if(r+u>t.length)return-1;var d=t[r+6]>>5,l=0;2===d?l+=2:(1&d&&1!==d&&(l+=2),4&d&&(l+=2));var c=(t[r+6]<<8|t[r+7])>>12-l&1,h=[2,1,2,3,3,4,4,5][d]+c,f=t[r+5]>>3,p=7&t[r+5],m=new Uint8Array([n<<6|f<<1|p>>2,(3&p)<<6|d<<3|c<<2|o>>4,o<<4&224]),v=a+i*(1536/s*9e4),y=t.subarray(r,r+u);return e.config=m,e.channelCount=h,e.samplerate=s,e.samples.push({unit:y,pts:v}),u}var Ye=function(e){function t(){return e.apply(this,arguments)||this}o(t,e);var r=t.prototype;return r.resetInitSegment=function(t,r,a,i){e.prototype.resetInitSegment.call(this,t,r,a,i),this._audioTrack={container:"audio/mpeg",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"mp3",samples:[],manifestCodec:r,duration:i,inputTimeScale:9e4,dropped:0}},t.probe=function(e){if(!e)return!1;var t=p(e,0),r=(null==t?void 0:t.length)||0;if(t&&11===e[r]&&119===e[r+1]&&void 0!==H(t)&&Ge(e,r)<=16)return!1;for(var a=e.length;r<a;r++)if(Ne(e,r))return _.log("MPEG Audio sync word found !"),!0;return!1},r.canParse=function(e,t){return function(e,t){return Be(e,t)&&4<=e.length-t}(e,t)},r.appendFrame=function(e,t,r){if(null!==this.basePTS)return Le(e,t,r,this.basePTS,this.frameIndex)},t}(we),je=0,ze=1,He=function(){function e(e,t,r){this.subtle=void 0,this.aesIV=void 0,this.aesMode=void 0,this.subtle=e,this.aesIV=t,this.aesMode=r}return e.prototype.decrypt=function(e,t){switch(this.aesMode){case je:return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},t,e);case ze:return this.subtle.decrypt({name:"AES-CTR",counter:this.aesIV,length:64},t,e);default:throw new Error("[AESCrypto] invalid aes mode "+this.aesMode)}},e}();var We=function(){function e(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.ksRows=0,this.keySize=0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.initTable()}var t=e.prototype;return t.uint8ArrayToUint32Array_=function(e){for(var t=new DataView(e),r=new Uint32Array(4),a=0;a<4;a++)r[a]=t.getUint32(4*a);return r},t.initTable=function(){var e=this.sBox,t=this.invSBox,r=this.subMix,a=r[0],i=r[1],n=r[2],s=r[3],o=this.invSubMix,u=o[0],d=o[1],l=o[2],c=o[3],h=new Uint32Array(256),f=0,p=0,m=0;for(m=0;m<256;m++)h[m]=m<128?m<<1:m<<1^283;for(m=0;m<256;m++){var v=p^p<<1^p<<2^p<<3^p<<4;v=v>>>8^255&v^99,e[f]=v,t[v]=f;var y=h[f],g=h[y],S=h[g],T=257*h[v]^16843008*v;a[f]=T<<24|T>>>8,i[f]=T<<16|T>>>16,n[f]=T<<8|T>>>24,s[f]=T,T=16843009*S^65537*g^257*y^16843008*f,u[v]=T<<24|T>>>8,d[v]=T<<16|T>>>16,l[v]=T<<8|T>>>24,c[v]=T,f?(f=y^h[h[h[S^y]]],p^=h[h[p]]):f=p=1}},t.expandKey=function(e){for(var t=this.uint8ArrayToUint32Array_(e),r=!0,a=0;a<t.length&&r;)r=t[a]===this.key[a],a++;if(!r){this.key=t;var i=this.keySize=t.length;if(4!==i&&6!==i&&8!==i)throw new Error("Invalid aes key size="+i);var n,s,o,u,d=this.ksRows=4*(i+6+1),l=this.keySchedule=new Uint32Array(d),c=this.invKeySchedule=new Uint32Array(d),h=this.sBox,f=this.rcon,p=this.invSubMix,m=p[0],v=p[1],y=p[2],g=p[3];for(n=0;n<d;n++)n<i?o=l[n]=t[n]:(u=o,n%i==0?(u=h[(u=u<<8|u>>>24)>>>24]<<24|h[u>>>16&255]<<16|h[u>>>8&255]<<8|h[255&u],u^=f[n/i|0]<<24):i>6&&n%i==4&&(u=h[u>>>24]<<24|h[u>>>16&255]<<16|h[u>>>8&255]<<8|h[255&u]),l[n]=o=(l[n-i]^u)>>>0);for(s=0;s<d;s++)n=d-s,u=3&s?l[n]:l[n-4],c[s]=s<4||n<=4?u:m[h[u>>>24]]^v[h[u>>>16&255]]^y[h[u>>>8&255]]^g[h[255&u]],c[s]=c[s]>>>0}},t.networkToHostOrderSwap=function(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24},t.decrypt=function(e,t,r){for(var a,i,n,s,o,u,d,l,c,h,f,p,m,v,y=this.keySize+6,g=this.invKeySchedule,S=this.invSBox,T=this.invSubMix,E=T[0],b=T[1],A=T[2],_=T[3],R=this.uint8ArrayToUint32Array_(r),k=R[0],w=R[1],I=R[2],U=R[3],x=new Int32Array(e),P=new Int32Array(x.length),D=this.networkToHostOrderSwap;t<x.length;){for(c=D(x[t]),h=D(x[t+1]),f=D(x[t+2]),p=D(x[t+3]),o=c^g[0],u=p^g[1],d=f^g[2],l=h^g[3],m=4,v=1;v<y;v++)a=E[o>>>24]^b[u>>16&255]^A[d>>8&255]^_[255&l]^g[m],i=E[u>>>24]^b[d>>16&255]^A[l>>8&255]^_[255&o]^g[m+1],n=E[d>>>24]^b[l>>16&255]^A[o>>8&255]^_[255&u]^g[m+2],s=E[l>>>24]^b[o>>16&255]^A[u>>8&255]^_[255&d]^g[m+3],o=a,u=i,d=n,l=s,m+=4;a=S[o>>>24]<<24^S[u>>16&255]<<16^S[d>>8&255]<<8^S[255&l]^g[m],i=S[u>>>24]<<24^S[d>>16&255]<<16^S[l>>8&255]<<8^S[255&o]^g[m+1],n=S[d>>>24]<<24^S[l>>16&255]<<16^S[o>>8&255]<<8^S[255&u]^g[m+2],s=S[l>>>24]<<24^S[o>>16&255]<<16^S[u>>8&255]<<8^S[255&d]^g[m+3],P[t]=D(a^k),P[t+1]=D(s^w),P[t+2]=D(n^I),P[t+3]=D(i^U),k=c,w=h,I=f,U=p,t+=4}return P.buffer},e}(),qe=function(){function e(e,t,r){this.subtle=void 0,this.key=void 0,this.aesMode=void 0,this.subtle=e,this.key=t,this.aesMode=r}return e.prototype.expandKey=function(){var e=function(e){switch(e){case je:return"AES-CBC";case ze:return"AES-CTR";default:throw new Error("[FastAESKey] invalid aes mode "+e)}}(this.aesMode);return this.subtle.importKey("raw",this.key,{name:e},!1,["encrypt","decrypt"])},e}();var Xe=function(){function e(e,t){var r=(void 0===t?{}:t).removePKCS7Padding,a=void 0===r||r;if(this.logEnabled=!0,this.removePKCS7Padding=void 0,this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null,this.useSoftware=void 0,this.enableSoftwareAES=void 0,this.enableSoftwareAES=e.enableSoftwareAES,this.removePKCS7Padding=a,a)try{var i=self.crypto;i&&(this.subtle=i.subtle||i.webkitSubtle)}catch(e){}this.useSoftware=!this.subtle}var t=e.prototype;return t.destroy=function(){this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null},t.isSync=function(){return this.useSoftware},t.flush=function(){var e=this.currentResult,t=this.remainderData;if(!e||t)return this.reset(),null;var r,a,i,n=new Uint8Array(e);return this.reset(),this.removePKCS7Padding?(a=(r=n).byteLength,(i=a&&new DataView(r.buffer).getUint8(a-1))?r.slice(0,a-i):r):n},t.reset=function(){this.currentResult=null,this.currentIV=null,this.remainderData=null,this.softwareDecrypter&&(this.softwareDecrypter=null)},t.decrypt=function(e,t,r,a){var i=this;return this.useSoftware?new Promise((function(n,s){var o=ArrayBuffer.isView(e)?e:new Uint8Array(e);i.softwareDecrypt(o,t,r,a);var u=i.flush();u?n(u.buffer):s(new Error("[softwareDecrypt] Failed to decrypt data"))})):this.webCryptoDecrypt(new Uint8Array(e),t,r,a)},t.softwareDecrypt=function(e,t,r,a){var i=this.currentIV,n=this.currentResult,s=this.remainderData;if(a!==je||16!==t.byteLength)return _.warn("SoftwareDecrypt: can only handle AES-128-CBC"),null;this.logOnce("JS AES decrypt"),s&&(e=Ee(s,e),this.remainderData=null);var o=this.getValidChunk(e);if(!o.length)return null;i&&(r=i);var u=this.softwareDecrypter;u||(u=this.softwareDecrypter=new We),u.expandKey(t);var d=n;return this.currentResult=u.decrypt(o.buffer,0,r),this.currentIV=o.slice(-16).buffer,d||null},t.webCryptoDecrypt=function(e,t,r,a){var i=this;if(this.key!==t||!this.fastAesKey){if(!this.subtle)return Promise.resolve(this.onWebCryptoError(e,t,r,a));this.key=t,this.fastAesKey=new qe(this.subtle,t,a)}return this.fastAesKey.expandKey().then((function(t){return i.subtle?(i.logOnce("WebCrypto AES decrypt"),new He(i.subtle,new Uint8Array(r),a).decrypt(e.buffer,t)):Promise.reject(new Error("web crypto not initialized"))})).catch((function(n){return _.warn("[decrypter]: WebCrypto Error, disable WebCrypto API, "+n.name+": "+n.message),i.onWebCryptoError(e,t,r,a)}))},t.onWebCryptoError=function(e,t,r,a){var i=this.enableSoftwareAES;if(i){this.useSoftware=!0,this.logEnabled=!0,this.softwareDecrypt(e,t,r,a);var n=this.flush();if(n)return n.buffer}throw new Error("WebCrypto"+(i?" and softwareDecrypt":"")+": failed to decrypt data")},t.getValidChunk=function(e){var t=e,r=e.length-e.length%16;return r!==e.length&&(t=e.slice(0,r),this.remainderData=e.slice(r)),t},t.logOnce=function(e){this.logEnabled&&(_.log("[decrypter]: "+e),this.logEnabled=!1)},e}(),Ze=/\/emsg[-/]ID3/i,Je=function(){function e(e,t){this.remainderData=null,this.timeOffset=0,this.config=void 0,this.videoTrack=void 0,this.audioTrack=void 0,this.id3Track=void 0,this.txtTrack=void 0,this.config=t}var t=e.prototype;return t.resetTimeStamp=function(){},t.resetInitSegment=function(e,t,r,a){var i=this.videoTrack=ke("video",1),n=this.audioTrack=ke("audio",1),s=this.txtTrack=ke("text",1);if(this.id3Track=ke("id3",1),this.timeOffset=0,null!=e&&e.byteLength){var o=pe(e);if(o.video){var u=o.video,d=u.id,l=u.timescale,c=u.codec,h=u.supplemental;i.id=d,i.timescale=s.timescale=l,i.codec=c,i.supplemental=h}if(o.audio){var f=o.audio,p=f.id,m=f.timescale,v=f.codec;n.id=p,n.timescale=m,n.codec=v}s.id=se.text,i.sampleDuration=0,i.duration=n.duration=a}},t.resetContiguity=function(){this.remainderData=null},e.probe=function(e){return function(e){for(var t=e.byteLength,r=0;r<t;){var a=de(e,r);if(a>8&&109===e[r+4]&&111===e[r+5]&&111===e[r+6]&&102===e[r+7])return!0;r=a>1?r+a:t}return!1}(e)},t.demux=function(e,t){this.timeOffset=t;var r=e,a=this.videoTrack,i=this.txtTrack;if(this.config.progressive){this.remainderData&&(r=Ee(this.remainderData,e));var n=function(e){var t={valid:null,remainder:null},r=he(e,["moof"]);if(r.length<2)return t.remainder=e,t;var a=r[r.length-1];return t.valid=e.slice(0,a.byteOffset-8),t.remainder=e.slice(a.byteOffset-8),t}(r);this.remainderData=n.remainder,a.samples=n.valid||new Uint8Array}else a.samples=r;var s=this.extractID3Track(a,t);return i.samples=be(t,a),{videoTrack:a,audioTrack:this.audioTrack,id3Track:s,textTrack:this.txtTrack}},t.flush=function(){var e=this.timeOffset,t=this.videoTrack,r=this.txtTrack;t.samples=this.remainderData||new Uint8Array,this.remainderData=null;var a=this.extractID3Track(t,this.timeOffset);return r.samples=be(e,t),{videoTrack:t,audioTrack:ke(),id3Track:a,textTrack:ke()}},t.extractID3Track=function(e,t){var r=this,a=this.id3Track;if(e.samples.length){var i=he(e.samples,["emsg"]);i&&i.forEach((function(e){var i=function(e){var t=e[0],r="",a="",i=0,n=0,s=0,o=0,u=0,d=0;if(0===t){for(;"\0"!==oe(e.subarray(d,d+1));)r+=oe(e.subarray(d,d+1)),d+=1;for(r+=oe(e.subarray(d,d+1)),d+=1;"\0"!==oe(e.subarray(d,d+1));)a+=oe(e.subarray(d,d+1)),d+=1;a+=oe(e.subarray(d,d+1)),d+=1,i=de(e,12),n=de(e,16),o=de(e,20),u=de(e,24),d=28}else if(1===t){i=de(e,d+=4);var l=de(e,d+=4),c=de(e,d+=4);for(d+=4,s=Math.pow(2,32)*l+c,L(s)||(s=Number.MAX_SAFE_INTEGER,_.warn("Presentation time exceeds safe integer limit and wrapped to max safe integer in parsing emsg box")),o=de(e,d),u=de(e,d+=4),d+=4;"\0"!==oe(e.subarray(d,d+1));)r+=oe(e.subarray(d,d+1)),d+=1;for(r+=oe(e.subarray(d,d+1)),d+=1;"\0"!==oe(e.subarray(d,d+1));)a+=oe(e.subarray(d,d+1)),d+=1;a+=oe(e.subarray(d,d+1)),d+=1}return{schemeIdUri:r,value:a,timeScale:i,presentationTime:s,presentationTimeDelta:n,eventDuration:o,id:u,payload:e.subarray(d,e.byteLength)}}(e);if(Ze.test(i.schemeIdUri)){var n=Qe(i,t),s=4294967295===i.eventDuration?Number.POSITIVE_INFINITY:i.eventDuration/i.timeScale;s<=.001&&(s=Number.POSITIVE_INFINITY);var o=i.payload;a.samples.push({data:o,len:o.byteLength,dts:n,pts:n,type:$.emsg,duration:s})}else if(r.config.enableEmsgKLVMetadata&&i.schemeIdUri.startsWith("urn:misb:KLV:bin:1910.1")){var u=Qe(i,t);a.samples.push({data:i.payload,len:i.payload.byteLength,dts:u,pts:u,type:$.misbklv,duration:Number.POSITIVE_INFINITY})}}))}return a},t.demuxSampleAes=function(e,t,r){return Promise.reject(new Error("The MP4 demuxer does not support SAMPLE-AES decryption"))},t.destroy=function(){this.config=null,this.remainderData=null,this.videoTrack=this.audioTrack=this.id3Track=this.txtTrack=void 0},e}();function Qe(e,t){return C(e.presentationTime)?e.presentationTime/e.timeScale:t+e.presentationTimeDelta/e.timeScale}var $e=function(){function e(e,t,r){this.keyData=void 0,this.decrypter=void 0,this.keyData=r,this.decrypter=new Xe(t,{removePKCS7Padding:!1})}var t=e.prototype;return t.decryptBuffer=function(e){return this.decrypter.decrypt(e,this.keyData.key.buffer,this.keyData.iv.buffer,je)},t.decryptAacSample=function(e,t,r){var a=this,i=e[t].unit;if(!(i.length<=16)){var n=i.subarray(16,i.length-i.length%16),s=n.buffer.slice(n.byteOffset,n.byteOffset+n.length);this.decryptBuffer(s).then((function(n){var s=new Uint8Array(n);i.set(s,16),a.decrypter.isSync()||a.decryptAacSamples(e,t+1,r)}))}},t.decryptAacSamples=function(e,t,r){for(;;t++){if(t>=e.length)return void r();if(!(e[t].unit.length<32)&&(this.decryptAacSample(e,t,r),!this.decrypter.isSync()))return}},t.getAvcEncryptedData=function(e){for(var t=16*Math.floor((e.length-48)/160)+16,r=new Int8Array(t),a=0,i=32;i<e.length-16;i+=160,a+=16)r.set(e.subarray(i,i+16),a);return r},t.getAvcDecryptedUnit=function(e,t){for(var r=new Uint8Array(t),a=0,i=32;i<e.length-16;i+=160,a+=16)e.set(r.subarray(a,a+16),i);return e},t.decryptAvcSample=function(e,t,r,a,i){var n=this,s=Re(i.data),o=this.getAvcEncryptedData(s);this.decryptBuffer(o.buffer).then((function(o){i.data=n.getAvcDecryptedUnit(s,o),n.decrypter.isSync()||n.decryptAvcSamples(e,t,r+1,a)}))},t.decryptAvcSamples=function(e,t,r,a){if(e instanceof Uint8Array)throw new Error("Cannot decrypt samples of type Uint8Array");for(;;t++,r=0){if(t>=e.length)return void a();for(var i=e[t].units;!(r>=i.length);r++){var n=i[r];if(!(n.data.length<=48||1!==n.type&&5!==n.type||(this.decryptAvcSample(e,t,r,a,n),this.decrypter.isSync())))return}}},e}(),et=function(){function e(){this.VideoSample=null}var t=e.prototype;return t.createVideoSample=function(e,t,r){return{key:e,frame:!1,pts:t,dts:r,units:[],length:0}},t.getLastNalUnit=function(e){var t,r,a=this.VideoSample;if(a&&0!==a.units.length||(a=e[e.length-1]),null!=(t=a)&&t.units){var i=a.units;r=i[i.length-1]}return r},t.pushAccessUnit=function(e,t){if(e.units.length&&e.frame){if(void 0===e.pts){var r=t.samples,a=r.length;if(!a)return void t.dropped++;var i=r[a-1];e.pts=i.pts,e.dts=i.dts}t.samples.push(e)}},t.parseNALu=function(e,t,r){var a,i,n=t.byteLength,s=e.naluState||0,o=s,u=[],d=0,l=-1,c=0;for(-1===s&&(l=0,c=this.getNALuType(t,0),s=0,d=1);d<n;)if(a=t[d++],s)if(1!==s)if(a)if(1===a){if(i=d-s-1,l>=0){var h={data:t.subarray(l,i),type:c};u.push(h)}else{var f=this.getLastNalUnit(e.samples);f&&(o&&d<=4-o&&f.state&&(f.data=f.data.subarray(0,f.data.byteLength-o)),i>0&&(f.data=Ee(f.data,t.subarray(0,i)),f.state=0))}d<n?(l=d,c=this.getNALuType(t,d),s=0):s=-1}else s=0;else s=3;else s=a?0:2;else s=a?0:1;if(l>=0&&s>=0){var p={data:t.subarray(l,n),type:c,state:s};u.push(p)}if(0===u.length){var m=this.getLastNalUnit(e.samples);m&&(m.data=Ee(m.data,t))}return e.naluState=s,u},e}(),tt=function(){function e(e){this.data=void 0,this.bytesAvailable=void 0,this.word=void 0,this.bitsAvailable=void 0,this.data=e,this.bytesAvailable=e.byteLength,this.word=0,this.bitsAvailable=0}var t=e.prototype;return t.loadWord=function(){var e=this.data,t=this.bytesAvailable,r=e.byteLength-t,a=new Uint8Array(4),i=Math.min(4,t);if(0===i)throw new Error("no bytes available");a.set(e.subarray(r,r+i)),this.word=new DataView(a.buffer).getUint32(0),this.bitsAvailable=8*i,this.bytesAvailable-=i},t.skipBits=function(e){var t;e=Math.min(e,8*this.bytesAvailable+this.bitsAvailable),this.bitsAvailable>e?(this.word<<=e,this.bitsAvailable-=e):(e-=this.bitsAvailable,e-=(t=e>>3)<<3,this.bytesAvailable-=t,this.loadWord(),this.word<<=e,this.bitsAvailable-=e)},t.readBits=function(e){var t=Math.min(this.bitsAvailable,e),r=this.word>>>32-t;if(e>32&&_.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=t,this.bitsAvailable>0)this.word<<=t;else{if(!(this.bytesAvailable>0))throw new Error("no bits available");this.loadWord()}return(t=e-t)>0&&this.bitsAvailable?r<<t|this.readBits(t):r},t.skipLZ=function(){var e;for(e=0;e<this.bitsAvailable;++e)if(0!=(this.word&2147483648>>>e))return this.word<<=e,this.bitsAvailable-=e,e;return this.loadWord(),e+this.skipLZ()},t.skipUEG=function(){this.skipBits(1+this.skipLZ())},t.skipEG=function(){this.skipBits(1+this.skipLZ())},t.readUEG=function(){var e=this.skipLZ();return this.readBits(e+1)-1},t.readEG=function(){var e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)},t.readBoolean=function(){return 1===this.readBits(1)},t.readUByte=function(){return this.readBits(8)},t.readUShort=function(){return this.readBits(16)},t.readUInt=function(){return this.readBits(32)},e}(),rt=function(e){function t(){return e.apply(this,arguments)||this}o(t,e);var r=t.prototype;return r.parsePES=function(e,t,r,a){var i,n=this,s=this.parseNALu(e,r.data,a),o=this.VideoSample,u=!1;r.data=null,o&&s.length&&!e.audFound&&(this.pushAccessUnit(o,e),o=this.VideoSample=this.createVideoSample(!1,r.pts,r.dts)),s.forEach((function(a){var s,d;switch(a.type){case 1:var l=!1;i=!0;var c,h=a.data;if(u&&h.length>4){var f=n.readSliceType(h);2!==f&&4!==f&&7!==f&&9!==f||(l=!0)}if(l)null!=(c=o)&&c.frame&&!o.key&&(n.pushAccessUnit(o,e),o=n.VideoSample=null);o||(o=n.VideoSample=n.createVideoSample(!0,r.pts,r.dts)),o.frame=!0,o.key=l;break;case 5:i=!0,null!=(s=o)&&s.frame&&!o.key&&(n.pushAccessUnit(o,e),o=n.VideoSample=null),o||(o=n.VideoSample=n.createVideoSample(!0,r.pts,r.dts)),o.key=!0,o.frame=!0;break;case 6:i=!0,_e(a.data,1,r.pts,t.samples);break;case 7:var p,m;i=!0,u=!0;var v=a.data,y=n.readSPS(v);if(!e.sps||e.width!==y.width||e.height!==y.height||(null==(p=e.pixelRatio)?void 0:p[0])!==y.pixelRatio[0]||(null==(m=e.pixelRatio)?void 0:m[1])!==y.pixelRatio[1]){e.width=y.width,e.height=y.height,e.pixelRatio=y.pixelRatio,e.sps=[v];for(var g=v.subarray(1,4),S="avc1.",T=0;T<3;T++){var E=g[T].toString(16);E.length<2&&(E="0"+E),S+=E}e.codec=S}break;case 8:i=!0,e.pps=[a.data];break;case 9:i=!0,e.audFound=!0,null!=(d=o)&&d.frame&&(n.pushAccessUnit(o,e),o=null),o||(o=n.VideoSample=n.createVideoSample(!1,r.pts,r.dts));break;case 12:i=!0;break;default:i=!1}o&&i&&o.units.push(a)})),a&&o&&(this.pushAccessUnit(o,e),this.VideoSample=null)},r.getNALuType=function(e,t){return 31&e[t]},r.readSliceType=function(e){var t=new tt(e);return t.readUByte(),t.readUEG(),t.readUEG()},r.skipScalingList=function(e,t){for(var r=8,a=8,i=0;i<e;i++)0!==a&&(a=(r+t.readEG()+256)%256),r=0===a?r:a},r.readSPS=function(e){var t,r,a,i=new tt(e),n=0,s=0,o=0,u=0,d=i.readUByte.bind(i),l=i.readBits.bind(i),c=i.readUEG.bind(i),h=i.readBoolean.bind(i),f=i.skipBits.bind(i),p=i.skipEG.bind(i),m=i.skipUEG.bind(i),v=this.skipScalingList.bind(this);d();var y=d();if(l(5),f(3),d(),m(),100===y||110===y||122===y||244===y||44===y||83===y||86===y||118===y||128===y){var g=c();if(3===g&&f(1),m(),m(),f(1),h())for(r=3!==g?8:12,a=0;a<r;a++)h()&&v(a<6?16:64,i)}m();var S=c();if(0===S)c();else if(1===S)for(f(1),p(),p(),t=c(),a=0;a<t;a++)p();m(),f(1);var T=c(),E=c(),b=l(1);0===b&&f(1),f(1),h()&&(n=c(),s=c(),o=c(),u=c());var A=[1,1];if(h()&&h())switch(d()){case 1:A=[1,1];break;case 2:A=[12,11];break;case 3:A=[10,11];break;case 4:A=[16,11];break;case 5:A=[40,33];break;case 6:A=[24,11];break;case 7:A=[20,11];break;case 8:A=[32,11];break;case 9:A=[80,33];break;case 10:A=[18,11];break;case 11:A=[15,11];break;case 12:A=[64,33];break;case 13:A=[160,99];break;case 14:A=[4,3];break;case 15:A=[3,2];break;case 16:A=[2,1];break;case 255:A=[d()<<8|d(),d()<<8|d()]}return{width:Math.ceil(16*(T+1)-2*n-2*s),height:(2-b)*(E+1)*16-(b?2:4)*(o+u),pixelRatio:A}},t}(et),at=function(e){function t(){for(var t,r=arguments.length,a=new Array(r),i=0;i<r;i++)a[i]=arguments[i];return(t=e.call.apply(e,[this].concat(a))||this).initVPS=null,t}o(t,e);var r=t.prototype;return r.parsePES=function(e,t,r,a){var i,n=this,o=this.parseNALu(e,r.data,a),u=this.VideoSample,d=!1;r.data=null,u&&o.length&&!e.audFound&&(this.pushAccessUnit(u,e),u=this.VideoSample=this.createVideoSample(!1,r.pts,r.dts)),o.forEach((function(a){var o,l;switch(a.type){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:u||(u=n.VideoSample=n.createVideoSample(!1,r.pts,r.dts)),u.frame=!0,i=!0;break;case 16:case 17:case 18:case 21:var c;if(i=!0,d)null!=(c=u)&&c.frame&&!u.key&&(n.pushAccessUnit(u,e),u=n.VideoSample=null);u||(u=n.VideoSample=n.createVideoSample(!0,r.pts,r.dts)),u.key=!0,u.frame=!0;break;case 19:case 20:i=!0,null!=(o=u)&&o.frame&&!u.key&&(n.pushAccessUnit(u,e),u=n.VideoSample=null),u||(u=n.VideoSample=n.createVideoSample(!0,r.pts,r.dts)),u.key=!0,u.frame=!0;break;case 39:i=!0,_e(a.data,2,r.pts,t.samples);break;case 32:i=!0,e.vps||("object"!=typeof e.params&&(e.params={}),e.params=s(e.params,n.readVPS(a.data)),n.initVPS=a.data),e.vps=[a.data];break;case 33:if(i=!0,d=!0,void 0===e.vps||e.vps[0]===n.initVPS||void 0===e.sps||n.matchSPS(e.sps[0],a.data)||(n.initVPS=e.vps[0],e.sps=e.pps=void 0),!e.sps){var h=n.readSPS(a.data);for(var f in e.width=h.width,e.height=h.height,e.pixelRatio=h.pixelRatio,e.codec=h.codecString,e.sps=[],"object"!=typeof e.params&&(e.params={}),h.params)e.params[f]=h.params[f]}n.pushParameterSet(e.sps,a.data,e.vps),u||(u=n.VideoSample=n.createVideoSample(!0,r.pts,r.dts)),u.key=!0;break;case 34:if(i=!0,"object"==typeof e.params){if(!e.pps){e.pps=[];var p=n.readPPS(a.data);for(var m in p)e.params[m]=p[m]}n.pushParameterSet(e.pps,a.data,e.vps)}break;case 35:i=!0,e.audFound=!0,null!=(l=u)&&l.frame&&(n.pushAccessUnit(u,e),u=null),u||(u=n.VideoSample=n.createVideoSample(!1,r.pts,r.dts));break;default:i=!1}u&&i&&u.units.push(a)})),a&&u&&(this.pushAccessUnit(u,e),this.VideoSample=null)},r.pushParameterSet=function(e,t,r){(r&&r[0]===this.initVPS||!r&&!e.length)&&e.push(t)},r.getNALuType=function(e,t){return(126&e[t])>>>1},r.ebsp2rbsp=function(e){for(var t=new Uint8Array(e.byteLength),r=0,a=0;a<e.byteLength;a++)a>=2&&3===e[a]&&0===e[a-1]&&0===e[a-2]||(t[r]=e[a],r++);return new Uint8Array(t.buffer,0,r)},r.pushAccessUnit=function(t,r){e.prototype.pushAccessUnit.call(this,t,r),this.initVPS&&(this.initVPS=null)},r.readVPS=function(e){var t=new tt(e);return t.readUByte(),t.readUByte(),t.readBits(4),t.skipBits(2),t.readBits(6),{numTemporalLayers:t.readBits(3)+1,temporalIdNested:t.readBoolean()}},r.readSPS=function(e){var t=new tt(this.ebsp2rbsp(e));t.readUByte(),t.readUByte(),t.readBits(4);var r=t.readBits(3);t.readBoolean();for(var a=t.readBits(2),i=t.readBoolean(),n=t.readBits(5),s=t.readUByte(),o=t.readUByte(),u=t.readUByte(),d=t.readUByte(),l=t.readUByte(),c=t.readUByte(),h=t.readUByte(),f=t.readUByte(),p=t.readUByte(),m=t.readUByte(),v=t.readUByte(),y=[],g=[],S=0;S<r;S++)y.push(t.readBoolean()),g.push(t.readBoolean());if(r>0)for(var T=r;T<8;T++)t.readBits(2);for(var E=0;E<r;E++)y[E]&&(t.readUByte(),t.readUByte(),t.readUByte(),t.readUByte(),t.readUByte(),t.readUByte(),t.readUByte(),t.readUByte(),t.readUByte(),t.readUByte(),t.readUByte()),g[E]&&t.readUByte();t.readUEG();var b=t.readUEG();3==b&&t.skipBits(1);var A=t.readUEG(),_=t.readUEG(),R=t.readBoolean(),k=0,w=0,I=0,U=0;R&&(k+=t.readUEG(),w+=t.readUEG(),I+=t.readUEG(),U+=t.readUEG());for(var x=t.readUEG(),P=t.readUEG(),D=t.readUEG(),C=t.readBoolean()?0:r;C<=r;C++)t.skipUEG(),t.skipUEG(),t.skipUEG();if((t.skipUEG(),t.skipUEG(),t.skipUEG(),t.skipUEG(),t.skipUEG(),t.skipUEG(),t.readBoolean())&&t.readBoolean())for(var L=0;L<4;L++)for(var O=0;O<(3===L?2:6);O++){if(t.readBoolean()){var B=Math.min(64,1<<4+(L<<1));L>1&&t.readEG();for(var M=0;M<B;M++)t.readEG()}else t.readUEG()}t.readBoolean(),t.readBoolean(),t.readBoolean()&&(t.readUByte(),t.skipUEG(),t.skipUEG(),t.readBoolean());for(var N=t.readUEG(),F=0,G=0;G<N;G++){var V=!1;if(0!==G&&(V=t.readBoolean()),V){G===N&&t.readUEG(),t.readBoolean(),t.readUEG();for(var K=0,Y=0;Y<=F;Y++){var j=t.readBoolean(),z=!1;j||(z=t.readBoolean()),(j||z)&&K++}F=K}else{var H=t.readUEG(),W=t.readUEG();F=H+W;for(var q=0;q<H;q++)t.readUEG(),t.readBoolean();for(var X=0;X<W;X++)t.readUEG(),t.readBoolean()}}if(t.readBoolean())for(var Z=t.readUEG(),J=0;J<Z;J++){for(var Q=0;Q<D+4;Q++)t.readBits(1);t.readBits(1)}var $=0,ee=1,te=1,re=!0,ae=1,ie=0;t.readBoolean(),t.readBoolean();if(t.readBoolean()){if(t.readBoolean()){var ne=t.readUByte();ne>0&&ne<16?(ee=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][ne-1],te=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][ne-1]):255===ne&&(ee=t.readBits(16),te=t.readBits(16))}if(t.readBoolean()&&t.readBoolean(),t.readBoolean())t.readBits(3),t.readBoolean(),t.readBoolean()&&(t.readUByte(),t.readUByte(),t.readUByte());if(t.readBoolean()&&(t.readUEG(),t.readUEG()),t.readBoolean(),t.readBoolean(),t.readBoolean(),t.readBoolean()&&(t.skipUEG(),t.skipUEG(),t.skipUEG(),t.skipUEG()),t.readBoolean())if(ae=t.readBits(32),ie=t.readBits(32),t.readBoolean()&&t.readUEG(),t.readBoolean()){var se=t.readBoolean(),oe=t.readBoolean(),ue=!1;(se||oe)&&((ue=t.readBoolean())&&(t.readUByte(),t.readBits(5),t.readBoolean(),t.readBits(5)),t.readBits(4),t.readBits(4),ue&&t.readBits(4),t.readBits(5),t.readBits(5),t.readBits(5));for(var de=0;de<=r;de++){var le=!1;(re=t.readBoolean())||t.readBoolean()?t.readEG():le=t.readBoolean();var ce=le?1:t.readUEG()+1;if(se)for(var he=0;he<ce;he++)t.readUEG(),t.readUEG(),ue&&(t.readUEG(),t.readUEG()),t.skipBits(1);if(oe)for(var fe=0;fe<ce;fe++)t.readUEG(),t.readUEG(),ue&&(t.readUEG(),t.readUEG()),t.skipBits(1)}}t.readBoolean()&&(t.readBoolean(),t.readBoolean(),t.readBoolean(),$=t.readUEG())}var pe=A,me=_;if(R){var ve=1,ye=1;1===b?ve=ye=2:2==b&&(ve=2),pe=A-ve*w-ve*k,me=_-ye*U-ye*I}for(var ge=a?["A","B","C"][a]:"",Se=s<<24|o<<16|u<<8|d,Te=0,Ee=0;Ee<32;Ee++)Te=(Te|(Se>>Ee&1)<<31-Ee)>>>0;var be=Te.toString(16);return 1===n&&"2"===be&&(be="6"),{codecString:"hvc1."+ge+n+"."+be+"."+(i?"H":"L")+v+".B0",params:{general_tier_flag:i,general_profile_idc:n,general_profile_space:a,general_profile_compatibility_flags:[s,o,u,d],general_constraint_indicator_flags:[l,c,h,f,p,m],general_level_idc:v,bit_depth:x+8,bit_depth_luma_minus8:x,bit_depth_chroma_minus8:P,min_spatial_segmentation_idc:$,chroma_format_idc:b,frame_rate:{fixed:re,fps:ie/ae}},width:pe,height:me,pixelRatio:[ee,te]}},r.readPPS=function(e){var t=new tt(this.ebsp2rbsp(e));t.readUByte(),t.readUByte(),t.skipUEG(),t.skipUEG(),t.skipBits(2),t.skipBits(3),t.skipBits(2),t.skipUEG(),t.skipUEG(),t.skipEG(),t.skipBits(2),t.readBoolean()&&t.skipUEG(),t.skipEG(),t.skipEG(),t.skipBits(4);var r=t.readBoolean(),a=t.readBoolean(),i=1;return a&&r?i=0:a?i=3:r&&(i=2),{parallelismType:i}},r.matchSPS=function(e,t){return String.fromCharCode.apply(null,e).substr(3)===String.fromCharCode.apply(null,t).substr(3)},t}(et),it=188,nt=function(){function e(e,t,r,a){this.logger=void 0,this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.sampleAes=null,this.pmtParsed=!1,this.audioCodec=void 0,this.videoCodec=void 0,this._pmtId=-1,this._videoTrack=void 0,this._audioTrack=void 0,this._id3Track=void 0,this._txtTrack=void 0,this.aacOverFlow=null,this.remainderData=null,this.videoParser=void 0,this.observer=e,this.config=t,this.typeSupported=r,this.logger=a,this.videoParser=null}e.probe=function(t,r){var a=e.syncOffset(t);return a>0&&r.warn("MPEG2-TS detected but first sync word found @ offset "+a),-1!==a},e.syncOffset=function(e){for(var t=e.length,r=Math.min(940,t-it)+1,a=0;a<r;){for(var i=!1,n=-1,s=0,o=a;o<t;o+=it){if(71!==e[o]||t-o!==it&&71!==e[o+it]){if(s)return-1;break}if(s++,-1===n&&0!==(n=o)&&(r=Math.min(n+18612,e.length-it)+1),i||(i=0===st(e,o)),i&&s>1&&(0===n&&s>2||o+it>r))return n}a++}return-1},e.createTrack=function(e,t){return{container:"video"===e||"audio"===e?"video/mp2t":void 0,type:e,id:se[e],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0,duration:"audio"===e?t:void 0}};var t=e.prototype;return t.resetInitSegment=function(t,r,a,i){this.pmtParsed=!1,this._pmtId=-1,this._videoTrack=e.createTrack("video"),this._videoTrack.duration=i,this._audioTrack=e.createTrack("audio",i),this._id3Track=e.createTrack("id3"),this._txtTrack=e.createTrack("text"),this._audioTrack.segmentCodec="aac",this.videoParser=null,this.aacOverFlow=null,this.remainderData=null,this.audioCodec=r,this.videoCodec=a},t.resetTimeStamp=function(){},t.resetContiguity=function(){var e=this._audioTrack,t=this._videoTrack,r=this._id3Track;e&&(e.pesData=null),t&&(t.pesData=null),r&&(r.pesData=null),this.aacOverFlow=null,this.remainderData=null},t.demux=function(t,r,a,i){var n;void 0===a&&(a=!1),void 0===i&&(i=!1),a||(this.sampleAes=null);var s=this._videoTrack,o=this._audioTrack,u=this._id3Track,d=this._txtTrack,l=s.pid,c=s.pesData,h=o.pid,f=u.pid,p=o.pesData,m=u.pesData,v=null,y=this.pmtParsed,g=this._pmtId,S=t.length;if(this.remainderData&&(S=(t=Ee(this.remainderData,t)).length,this.remainderData=null),S<it&&!i)return this.remainderData=t,{audioTrack:o,videoTrack:s,id3Track:u,textTrack:d};var T=Math.max(0,e.syncOffset(t));(S-=(S-T)%it)<t.byteLength&&!i&&(this.remainderData=new Uint8Array(t.buffer,S,t.buffer.byteLength-S));for(var E=0,b=T;b<S;b+=it)if(71===t[b]){var A=!!(64&t[b+1]),_=st(t,b),R=void 0;if((48&t[b+3])>>4>1){if((R=b+5+t[b+4])===b+it)continue}else R=b+4;switch(_){case l:A&&(c&&(n=ct(c,this.logger))&&(this.readyVideoParser(s.segmentCodec),null!==this.videoParser&&this.videoParser.parsePES(s,d,n,!1)),c={data:[],size:0}),c&&(c.data.push(t.subarray(R,b+it)),c.size+=b+it-R);break;case h:if(A){if(p&&(n=ct(p,this.logger)))switch(o.segmentCodec){case"aac":this.parseAACPES(o,n);break;case"mp3":this.parseMPEGPES(o,n);break;case"ac3":this.parseAC3PES(o,n)}p={data:[],size:0}}p&&(p.data.push(t.subarray(R,b+it)),p.size+=b+it-R);break;case f:A&&(m&&(n=ct(m,this.logger))&&this.parseID3PES(u,n),m={data:[],size:0}),m&&(m.data.push(t.subarray(R,b+it)),m.size+=b+it-R);break;case 0:A&&(R+=t[R]+1),g=this._pmtId=ot(t,R);break;case g:A&&(R+=t[R]+1);var k=ut(t,R,this.typeSupported,a,this.observer,this.logger);(l=k.videoPid)>0&&(s.pid=l,s.segmentCodec=k.segmentVideoCodec),(h=k.audioPid)>0&&(o.pid=h,o.segmentCodec=k.segmentAudioCodec),(f=k.id3Pid)>0&&(u.pid=f),null===v||y||(this.logger.warn("MPEG-TS PMT found at "+b+" after unknown PID '"+v+"'. Backtracking to sync byte @"+T+" to parse all TS packets."),v=null,b=T-188),y=this.pmtParsed=!0;break;case 17:case 8191:break;default:v=_}}else E++;E>0&&dt(this.observer,new Error("Found "+E+" TS packet/s that do not start with 0x47"),void 0,this.logger),s.pesData=c,o.pesData=p,u.pesData=m;var w={audioTrack:o,videoTrack:s,id3Track:u,textTrack:d};return i&&this.extractRemainingSamples(w),w},t.flush=function(){var e,t=this.remainderData;return this.remainderData=null,e=t?this.demux(t,-1,!1,!0):{videoTrack:this._videoTrack,audioTrack:this._audioTrack,id3Track:this._id3Track,textTrack:this._txtTrack},this.extractRemainingSamples(e),this.sampleAes?this.decrypt(e,this.sampleAes):e},t.extractRemainingSamples=function(e){var t,r=e.audioTrack,a=e.videoTrack,i=e.id3Track,n=e.textTrack,s=a.pesData,o=r.pesData,u=i.pesData;if(s&&(t=ct(s,this.logger))?(this.readyVideoParser(a.segmentCodec),null!==this.videoParser&&(this.videoParser.parsePES(a,n,t,!0),a.pesData=null)):a.pesData=s,o&&(t=ct(o,this.logger))){switch(r.segmentCodec){case"aac":this.parseAACPES(r,t);break;case"mp3":this.parseMPEGPES(r,t);break;case"ac3":this.parseAC3PES(r,t)}r.pesData=null}else null!=o&&o.size&&this.logger.log("last AAC PES packet truncated,might overlap between fragments"),r.pesData=o;u&&(t=ct(u,this.logger))?(this.parseID3PES(i,t),i.pesData=null):i.pesData=u},t.demuxSampleAes=function(e,t,r){var a=this.demux(e,r,!0,!this.config.progressive),i=this.sampleAes=new $e(this.observer,this.config,t);return this.decrypt(a,i)},t.readyVideoParser=function(e){null===this.videoParser&&("avc"===e?this.videoParser=new rt:"hevc"===e&&(this.videoParser=new at))},t.decrypt=function(e,t){return new Promise((function(r){var a=e.audioTrack,i=e.videoTrack;a.samples&&"aac"===a.segmentCodec?t.decryptAacSamples(a.samples,0,(function(){i.samples?t.decryptAvcSamples(i.samples,0,0,(function(){r(e)})):r(e)})):i.samples&&t.decryptAvcSamples(i.samples,0,0,(function(){r(e)}))}))},t.destroy=function(){this.observer&&this.observer.removeAllListeners(),this.config=this.logger=this.observer=null,this.aacOverFlow=this.videoParser=this.remainderData=this.sampleAes=null,this._videoTrack=this._audioTrack=this._id3Track=this._txtTrack=void 0},t.parseAACPES=function(e,t){var r,a,i,n=0,s=this.aacOverFlow,o=t.data;if(s){this.aacOverFlow=null;var u=s.missing,d=s.sample.unit.byteLength;if(-1===u)o=Ee(s.sample.unit,o);else{var l=d-u;s.sample.unit.set(o.subarray(0,u),l),e.samples.push(s.sample),n=s.missing}}for(r=n,a=o.length;r<a-1&&!I(o,r);r++);if(r!==n){var c,h=r<a-1;if(c=h?"AAC PES did not start with ADTS header,offset:"+r:"No ADTS header found in AAC PES",dt(this.observer,new Error(c),h,this.logger),!h)return}if(x(e,this.observer,o,r,this.audioCodec),void 0!==t.pts)i=t.pts;else{if(!s)return void this.logger.warn("[tsdemuxer]: AAC PES unknown PTS");var f=P(e.samplerate);i=s.sample.pts+f}for(var p,m=0;r<a;){if(r+=(p=D(e,o,r,i,m)).length,p.missing){this.aacOverFlow=p;break}for(m++;r<a-1&&!I(o,r);r++);}},t.parseMPEGPES=function(e,t){var r=t.data,a=r.length,i=0,n=0,s=t.pts;if(void 0!==s)for(;n<a;)if(Me(r,n)){var o=Le(e,r,n,s,i);if(!o)break;n+=o.length,i++}else n++;else this.logger.warn("[tsdemuxer]: MPEG PES unknown PTS")},t.parseAC3PES=function(e,t){var r=t.data,a=t.pts;if(void 0!==a)for(var i,n=r.length,s=0,o=0;o<n&&(i=Ke(e,r,o,a,s++))>0;)o+=i;else this.logger.warn("[tsdemuxer]: AC3 PES unknown PTS")},t.parseID3PES=function(e,t){if(void 0!==t.pts){var r=s({},t,{type:this._videoTrack?$.emsg:$.audioId3,duration:Number.POSITIVE_INFINITY});e.samples.push(r)}else this.logger.warn("[tsdemuxer]: ID3 PES unknown PTS")},e}();function st(e,t){return((31&e[t+1])<<8)+e[t+2]}function ot(e,t){return(31&e[t+10])<<8|e[t+11]}function ut(e,t,r,a,i,n){var s={audioPid:-1,videoPid:-1,id3Pid:-1,segmentVideoCodec:"avc",segmentAudioCodec:"aac"},o=t+3+((15&e[t+1])<<8|e[t+2])-4;for(t+=12+((15&e[t+10])<<8|e[t+11]);t<o;){var u=st(e,t),d=(15&e[t+3])<<8|e[t+4];switch(e[t]){case 207:if(!a){lt("ADTS AAC",n);break}case 15:-1===s.audioPid&&(s.audioPid=u);break;case 21:-1===s.id3Pid&&(s.id3Pid=u);break;case 219:if(!a){lt("H.264",n);break}case 27:-1===s.videoPid&&(s.videoPid=u);break;case 3:case 4:r.mpeg||r.mp3?-1===s.audioPid&&(s.audioPid=u,s.segmentAudioCodec="mp3"):n.log("MPEG audio found, not supported in this browser");break;case 193:if(!a){lt("AC-3",n);break}case 129:r.ac3?-1===s.audioPid&&(s.audioPid=u,s.segmentAudioCodec="ac3"):n.log("AC-3 audio found, not supported in this browser");break;case 6:if(-1===s.audioPid&&d>0)for(var l=t+5,c=d;c>2;){if(106===e[l])!0!==r.ac3?n.log("AC-3 audio found, not supported in this browser for now"):(s.audioPid=u,s.segmentAudioCodec="ac3");var h=e[l+1]+2;l+=h,c-=h}break;case 194:case 135:return dt(i,new Error("Unsupported EC-3 in M2TS found"),void 0,n),s;case 36:-1===s.videoPid&&(s.videoPid=u,s.segmentVideoCodec="hevc",n.log("HEVC in M2TS found"))}t+=d+5}return s}function dt(e,t,r,a){a.warn("parsing error: "+t.message),e.emit(y.ERROR,y.ERROR,{type:m.MEDIA_ERROR,details:v.FRAG_PARSING_ERROR,fatal:!1,levelRetry:r,error:t,reason:t.message})}function lt(e,t){t.log(e+" with AES-128-CBC encryption found in unencrypted stream")}function ct(e,t){var r,a,i,n,s,o=0,u=e.data;if(!e||0===e.size)return null;for(;u[0].length<19&&u.length>1;)u[0]=Ee(u[0],u[1]),u.splice(1,1);if(1===((r=u[0])[0]<<16)+(r[1]<<8)+r[2]){if((a=(r[4]<<8)+r[5])&&a>e.size-6)return null;var d=r[7];192&d&&(n=536870912*(14&r[9])+4194304*(255&r[10])+16384*(254&r[11])+128*(255&r[12])+(254&r[13])/2,64&d?n-(s=536870912*(14&r[14])+4194304*(255&r[15])+16384*(254&r[16])+128*(255&r[17])+(254&r[18])/2)>54e5&&(t.warn(Math.round((n-s)/9e4)+"s delta between PTS and DTS, align them"),n=s):s=n);var l=(i=r[8])+9;if(e.size<=l)return null;e.size-=l;for(var c=new Uint8Array(e.size),h=0,f=u.length;h<f;h++){var p=(r=u[h]).byteLength;if(l){if(l>p){l-=p;continue}r=r.subarray(l),p-=l,l=0}c.set(r,o),o+=p}return a&&(a-=i+3),{data:c,pts:n,dts:s,len:a}}return null}var ht=function(){function e(){}return e.getSilentFrame=function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}},e}(),ft=Math.pow(2,32)-1,pt=function(){function e(){}return e.init=function(){var t;for(t in e.types={avc1:[],avcC:[],hvc1:[],hvcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],dac3:[],"ac-3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]},e.types)e.types.hasOwnProperty(t)&&(e.types[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)]);var r=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),a=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);e.HDLR_TYPES={video:r,audio:a};var i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),n=new Uint8Array([0,0,0,0,0,0,0,0]);e.STTS=e.STSC=e.STCO=n,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);var s=new Uint8Array([105,115,111,109]),o=new Uint8Array([97,118,99,49]),u=new Uint8Array([0,0,0,1]);e.FTYP=e.box(e.types.ftyp,s,u,s,o),e.DINF=e.box(e.types.dinf,e.box(e.types.dref,i))},e.box=function(e){for(var t=8,r=arguments.length,a=new Array(r>1?r-1:0),i=1;i<r;i++)a[i-1]=arguments[i];for(var n=a.length,s=n;n--;)t+=a[n].byteLength;var o=new Uint8Array(t);for(o[0]=t>>24&255,o[1]=t>>16&255,o[2]=t>>8&255,o[3]=255&t,o.set(e,4),n=0,t=8;n<s;n++)o.set(a[n],t),t+=a[n].byteLength;return o},e.hdlr=function(t){return e.box(e.types.hdlr,e.HDLR_TYPES[t])},e.mdat=function(t){return e.box(e.types.mdat,t)},e.mdhd=function(t,r){r*=t;var a=Math.floor(r/(ft+1)),i=Math.floor(r%(ft+1));return e.box(e.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,255&t,a>>24,a>>16&255,a>>8&255,255&a,i>>24,i>>16&255,i>>8&255,255&i,85,196,0,0]))},e.mdia=function(t){return e.box(e.types.mdia,e.mdhd(t.timescale||0,t.duration||0),e.hdlr(t.type),e.minf(t))},e.mfhd=function(t){return e.box(e.types.mfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))},e.minf=function(t){return"audio"===t.type?e.box(e.types.minf,e.box(e.types.smhd,e.SMHD),e.DINF,e.stbl(t)):e.box(e.types.minf,e.box(e.types.vmhd,e.VMHD),e.DINF,e.stbl(t))},e.moof=function(t,r,a){return e.box(e.types.moof,e.mfhd(t),e.traf(a,r))},e.moov=function(t){for(var r=t.length,a=[];r--;)a[r]=e.trak(t[r]);return e.box.apply(null,[e.types.moov,e.mvhd(t[0].timescale||0,t[0].duration||0)].concat(a).concat(e.mvex(t)))},e.mvex=function(t){for(var r=t.length,a=[];r--;)a[r]=e.trex(t[r]);return e.box.apply(null,[e.types.mvex].concat(a))},e.mvhd=function(t,r){r*=t;var a=Math.floor(r/(ft+1)),i=Math.floor(r%(ft+1)),n=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,255&t,a>>24,a>>16&255,a>>8&255,255&a,i>>24,i>>16&255,i>>8&255,255&i,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return e.box(e.types.mvhd,n)},e.sdtp=function(t){var r,a,i=t.samples||[],n=new Uint8Array(4+i.length);for(r=0;r<i.length;r++)a=i[r].flags,n[r+4]=a.dependsOn<<4|a.isDependedOn<<2|a.hasRedundancy;return e.box(e.types.sdtp,n)},e.stbl=function(t){return e.box(e.types.stbl,e.stsd(t),e.box(e.types.stts,e.STTS),e.box(e.types.stsc,e.STSC),e.box(e.types.stsz,e.STSZ),e.box(e.types.stco,e.STCO))},e.avc1=function(t){var r,a,i,n=[],s=[];for(r=0;r<t.sps.length;r++)i=(a=t.sps[r]).byteLength,n.push(i>>>8&255),n.push(255&i),n=n.concat(Array.prototype.slice.call(a));for(r=0;r<t.pps.length;r++)i=(a=t.pps[r]).byteLength,s.push(i>>>8&255),s.push(255&i),s=s.concat(Array.prototype.slice.call(a));var o=e.box(e.types.avcC,new Uint8Array([1,n[3],n[4],n[5],255,224|t.sps.length].concat(n).concat([t.pps.length]).concat(s))),u=t.width,d=t.height,l=t.pixelRatio[0],c=t.pixelRatio[1];return e.box(e.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,u>>8&255,255&u,d>>8&255,255&d,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),o,e.box(e.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),e.box(e.types.pasp,new Uint8Array([l>>24,l>>16&255,l>>8&255,255&l,c>>24,c>>16&255,c>>8&255,255&c])))},e.esds=function(e){var t=e.config;return new Uint8Array([0,0,0,0,3,25,0,1,0,4,17,64,21,0,0,0,0,0,0,0,0,0,0,0,5,2].concat(t,[6,1,2]))},e.audioStsd=function(e){var t=e.samplerate||0;return new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount||0,0,16,0,0,0,0,t>>8&255,255&t,0,0])},e.mp4a=function(t){return e.box(e.types.mp4a,e.audioStsd(t),e.box(e.types.esds,e.esds(t)))},e.mp3=function(t){return e.box(e.types[".mp3"],e.audioStsd(t))},e.ac3=function(t){return e.box(e.types["ac-3"],e.audioStsd(t),e.box(e.types.dac3,t.config))},e.stsd=function(t){var r=t.segmentCodec;if("audio"===t.type){if("aac"===r)return e.box(e.types.stsd,e.STSD,e.mp4a(t));if("ac3"===r&&t.config)return e.box(e.types.stsd,e.STSD,e.ac3(t));if("mp3"===r&&"mp3"===t.codec)return e.box(e.types.stsd,e.STSD,e.mp3(t))}else{if(!t.pps||!t.sps)throw new Error("video track missing pps or sps");if("avc"===r)return e.box(e.types.stsd,e.STSD,e.avc1(t));if("hevc"===r&&t.vps)return e.box(e.types.stsd,e.STSD,e.hvc1(t))}throw new Error("unsupported "+t.type+" segment codec ("+r+"/"+t.codec+")")},e.tkhd=function(t){var r=t.id,a=(t.duration||0)*(t.timescale||0),i=t.width||0,n=t.height||0,s=Math.floor(a/(ft+1)),o=Math.floor(a%(ft+1));return e.box(e.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,s>>24,s>>16&255,s>>8&255,255&s,o>>24,o>>16&255,o>>8&255,255&o,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,i>>8&255,255&i,0,0,n>>8&255,255&n,0,0]))},e.traf=function(t,r){var a=e.sdtp(t),i=t.id,n=Math.floor(r/(ft+1)),s=Math.floor(r%(ft+1));return e.box(e.types.traf,e.box(e.types.tfhd,new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i])),e.box(e.types.tfdt,new Uint8Array([1,0,0,0,n>>24,n>>16&255,n>>8&255,255&n,s>>24,s>>16&255,s>>8&255,255&s])),e.trun(t,a.length+16+20+8+16+8+8),a)},e.trak=function(t){return t.duration=t.duration||4294967295,e.box(e.types.trak,e.tkhd(t),e.mdia(t))},e.trex=function(t){var r=t.id;return e.box(e.types.trex,new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))},e.trun=function(t,r){var a,i,n,s,o,u,d=t.samples||[],l=d.length,c=12+16*l,h=new Uint8Array(c);for(r+=8+c,h.set(["video"===t.type?1:0,0,15,1,l>>>24&255,l>>>16&255,l>>>8&255,255&l,r>>>24&255,r>>>16&255,r>>>8&255,255&r],0),a=0;a<l;a++)n=(i=d[a]).duration,s=i.size,o=i.flags,u=i.cts,h.set([n>>>24&255,n>>>16&255,n>>>8&255,255&n,s>>>24&255,s>>>16&255,s>>>8&255,255&s,o.isLeading<<2|o.dependsOn,o.isDependedOn<<6|o.hasRedundancy<<4|o.paddingValue<<1|o.isNonSync,61440&o.degradPrio,15&o.degradPrio,u>>>24&255,u>>>16&255,u>>>8&255,255&u],12+16*a);return e.box(e.types.trun,h)},e.initSegment=function(t){e.types||e.init();var r=e.moov(t);return Ee(e.FTYP,r)},e.hvc1=function(t){for(var r=t.params,a=[t.vps,t.sps,t.pps],i=new Uint8Array([1,r.general_profile_space<<6|(r.general_tier_flag?32:0)|r.general_profile_idc,r.general_profile_compatibility_flags[0],r.general_profile_compatibility_flags[1],r.general_profile_compatibility_flags[2],r.general_profile_compatibility_flags[3],r.general_constraint_indicator_flags[0],r.general_constraint_indicator_flags[1],r.general_constraint_indicator_flags[2],r.general_constraint_indicator_flags[3],r.general_constraint_indicator_flags[4],r.general_constraint_indicator_flags[5],r.general_level_idc,240|r.min_spatial_segmentation_idc>>8,255&r.min_spatial_segmentation_idc,252|r.parallelismType,252|r.chroma_format_idc,248|r.bit_depth_luma_minus8,248|r.bit_depth_chroma_minus8,0,parseInt(r.frame_rate.fps),3|r.temporal_id_nested<<2|r.num_temporal_layers<<3|(r.frame_rate.fixed?64:0),a.length]),n=i.length,s=0;s<a.length;s+=1){n+=3;for(var o=0;o<a[s].length;o+=1)n+=2+a[s][o].length}var u=new Uint8Array(n);u.set(i,0),n=i.length;for(var d=a.length-1,l=0;l<a.length;l+=1){u.set(new Uint8Array([32+l|(l===d?128:0),0,a[l].length]),n),n+=3;for(var c=0;c<a[l].length;c+=1)u.set(new Uint8Array([a[l][c].length>>8,255&a[l][c].length]),n),n+=2,u.set(a[l][c],n),n+=a[l][c].length}var h=e.box(e.types.hvcC,u),f=t.width,p=t.height,m=t.pixelRatio[0],v=t.pixelRatio[1];return e.box(e.types.hvc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,f>>8&255,255&f,p>>8&255,255&p,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),h,e.box(e.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),e.box(e.types.pasp,new Uint8Array([m>>24,m>>16&255,m>>8&255,255&m,v>>24,v>>16&255,v>>8&255,255&v])))},e}();pt.types=void 0,pt.HDLR_TYPES=void 0,pt.STTS=void 0,pt.STSC=void 0,pt.STCO=void 0,pt.STSZ=void 0,pt.VMHD=void 0,pt.SMHD=void 0,pt.STSD=void 0,pt.FTYP=void 0,pt.DINF=void 0;var mt="main",vt="audio";function yt(e,t){return function(e,t,r,a){var i=e*t*r;return Math.round(i)}(e,1e3,1/9e4)}var gt=null,St=null;function Tt(e,t,r,a){return{duration:t,size:r,cts:a,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:e?2:1,isNonSync:e?0:1}}}var Et=function(e){function t(t,r,a,i){var n;if((n=e.call(this,"mp4-remuxer",i)||this).observer=void 0,n.config=void 0,n.typeSupported=void 0,n.ISGenerated=!1,n._initPTS=null,n._initDTS=null,n.nextVideoTs=null,n.nextAudioTs=null,n.videoSampleDuration=null,n.isAudioContiguous=!1,n.isVideoContiguous=!1,n.videoTrackConfig=void 0,n.observer=t,n.config=r,n.typeSupported=a,n.ISGenerated=!1,null===gt){var s=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);gt=s?parseInt(s[1]):0}if(null===St){var o=navigator.userAgent.match(/Safari\/(\d+)/i);St=o?parseInt(o[1]):0}return n}o(t,e);var r=t.prototype;return r.destroy=function(){this.config=this.videoTrackConfig=this._initPTS=this._initDTS=null},r.resetTimeStamp=function(e){this.log("initPTS & initDTS reset"),this._initPTS=this._initDTS=e},r.resetNextTimestamp=function(){this.log("reset next timestamp"),this.isVideoContiguous=!1,this.isAudioContiguous=!1},r.resetInitSegment=function(){this.log("ISGenerated flag reset"),this.ISGenerated=!1,this.videoTrackConfig=void 0},r.getVideoStartPts=function(e){var t=!1,r=e[0].pts,a=e.reduce((function(e,a){var i=a.pts,n=i-e;return n<-4294967296&&(t=!0,n=(i=bt(i,r))-e),n>0?e:i}),r);return t&&this.debug("PTS rollover detected"),a},r.remux=function(e,t,r,a,i,n,s,o){var u,d,l,c,h,f,p=i,m=i,v=e.pid>-1,y=t.pid>-1,g=t.samples.length,S=e.samples.length>0,T=s&&g>0||g>1;if((!v||S)&&(!y||T)||this.ISGenerated||s){if(this.ISGenerated){var E,b,A,_,R=this.videoTrackConfig;(R&&(t.width!==R.width||t.height!==R.height||(null==(E=t.pixelRatio)?void 0:E[0])!==(null==(b=R.pixelRatio)?void 0:b[0])||(null==(A=t.pixelRatio)?void 0:A[1])!==(null==(_=R.pixelRatio)?void 0:_[1]))||!R&&T||null===this.nextAudioTs&&S)&&this.resetInitSegment()}this.ISGenerated||(l=this.generateIS(e,t,i,n));var k,w=this.isVideoContiguous,I=-1;if(T&&(I=function(e){for(var t=0;t<e.length;t++)if(e[t].key)return t;return-1}(t.samples),!w&&this.config.forceKeyFrameOnDiscontinuity))if(f=!0,I>0){this.warn("Dropped "+I+" out of "+g+" video samples due to a missing keyframe");var U=this.getVideoStartPts(t.samples);t.samples=t.samples.slice(I),t.dropped+=I,k=m+=(t.samples[0].pts-U)/t.inputTimeScale}else-1===I&&(this.warn("No keyframe found out of "+g+" video samples"),f=!1);if(this.ISGenerated){if(S&&T){var x=this.getVideoStartPts(t.samples),P=(bt(e.samples[0].pts,x)-x)/t.inputTimeScale;p+=Math.max(0,P),m+=Math.max(0,-P)}if(S){if(e.samplerate||(this.warn("regenerate InitSegment as audio detected"),l=this.generateIS(e,t,i,n)),d=this.remuxAudio(e,p,this.isAudioContiguous,n,y||T||o===vt?m:void 0),T){var D=d?d.endPTS-d.startPTS:0;t.inputTimeScale||(this.warn("regenerate InitSegment as video detected"),l=this.generateIS(e,t,i,n)),u=this.remuxVideo(t,m,w,D)}}else T&&(u=this.remuxVideo(t,m,w,0));u&&(u.firstKeyFrame=I,u.independent=-1!==I,u.firstKeyFramePTS=k)}}return this.ISGenerated&&this._initPTS&&this._initDTS&&(r.samples.length&&(h=At(r,i,this._initPTS,this._initDTS)),a.samples.length&&(c=_t(a,i,this._initPTS))),{audio:d,video:u,initSegment:l,independent:f,text:c,id3:h}},r.generateIS=function(e,t,r,a){var i,n,s,o=e.samples,u=t.samples,d=this.typeSupported,l={},c=this._initPTS,h=!c||a,f="audio/mp4",p=-1;if(h&&(i=n=1/0),e.config&&o.length){switch(e.timescale=e.samplerate,e.segmentCodec){case"mp3":d.mpeg?(f="audio/mpeg",e.codec=""):d.mp3&&(e.codec="mp3");break;case"ac3":e.codec="ac-3"}l.audio={id:"audio",container:f,codec:e.codec,initSegment:"mp3"===e.segmentCodec&&d.mpeg?new Uint8Array(0):pt.initSegment([e]),metadata:{channelCount:e.channelCount}},h&&(p=e.id,s=e.inputTimeScale,c&&s===c.timescale?h=!1:i=n=o[0].pts-Math.round(s*r))}if(t.sps&&t.pps&&u.length){if(t.timescale=t.inputTimeScale,l.video={id:"main",container:"video/mp4",codec:t.codec,initSegment:pt.initSegment([t]),metadata:{width:t.width,height:t.height}},h)if(p=t.id,s=t.inputTimeScale,c&&s===c.timescale)h=!1;else{var m=this.getVideoStartPts(u),v=Math.round(s*r);n=Math.min(n,bt(u[0].dts,m)-v),i=Math.min(i,m-v)}this.videoTrackConfig={width:t.width,height:t.height,pixelRatio:t.pixelRatio}}if(Object.keys(l).length)return this.ISGenerated=!0,h?(c&&this.warn("Timestamps at playlist time: "+(a?"":"~")+r+" "+i/s+" != initPTS: "+c.baseTime/c.timescale+" ("+c.baseTime+"/"+c.timescale+") trackId: "+c.trackId),this.log("Found initPTS at playlist time: "+r+" offset: "+i/s+" ("+i+"/"+s+") trackId: "+p),this._initPTS={baseTime:i,timescale:s,trackId:p},this._initDTS={baseTime:n,timescale:s,trackId:p}):i=s=void 0,{tracks:l,initPTS:i,timescale:s,trackId:p}},r.remuxVideo=function(e,t,r,a){var i,n,o=e.inputTimeScale,u=e.samples,d=[],l=u.length,c=this._initPTS,h=c.baseTime*o/c.timescale,f=this.nextVideoTs,p=8,g=this.videoSampleDuration,S=Number.POSITIVE_INFINITY,T=Number.NEGATIVE_INFINITY,E=!1;if(!r||null===f){var b=h+t*o,A=u[0].pts-bt(u[0].dts,u[0].pts);gt&&null!==f&&Math.abs(b-A-(f+h))<15e3?r=!0:f=b-A-h}for(var _=f+h,R=0;R<l;R++){var k=u[R];k.pts=bt(k.pts,_),k.dts=bt(k.dts,_),k.dts<u[R>0?R-1:R].dts&&(E=!0)}E&&u.sort((function(e,t){var r=e.dts-t.dts,a=e.pts-t.pts;return r||a})),i=u[0].dts;var w=(n=u[u.length-1].dts)-i,I=w?Math.round(w/(l-1)):g||e.inputTimeScale/30;if(r){var U=i-_,x=U>I,P=U<-1;if((x||P)&&(x?this.warn((e.segmentCodec||"").toUpperCase()+": "+yt(U)+" ms ("+U+"dts) hole between fragments detected at "+t.toFixed(3)):this.warn((e.segmentCodec||"").toUpperCase()+": "+yt(-U)+" ms ("+U+"dts) overlapping between fragments detected at "+t.toFixed(3)),!P||_>=u[0].pts||gt)){i=_;var D=u[0].pts-U;if(x)u[0].dts=i,u[0].pts=D;else for(var C=!0,L=0;L<u.length&&!(u[L].dts>D&&C);L++){var O=u[L].pts;if(u[L].dts-=U,u[L].pts-=U,L<u.length-1){var B=u[L+1].pts;C=B<=u[L].pts==B<=O}}this.log("Video: Initial PTS/DTS adjusted: "+yt(D)+"/"+yt(i)+", delta: "+yt(U)+" ms")}}for(var M=0,N=0,F=i=Math.max(0,i),G=0;G<l;G++){for(var V=u[G],K=V.units,Y=K.length,j=0,z=0;z<Y;z++)j+=K[z].data.length;N+=j,M+=Y,V.length=j,V.dts<F?(V.dts=F,F+=I/4|0||1):F=V.dts,S=Math.min(V.pts,S),T=Math.max(V.pts,T)}n=u[l-1].dts;var H,W=N+4*M+8;try{H=new Uint8Array(W)}catch(e){return void this.observer.emit(y.ERROR,y.ERROR,{type:m.MUX_ERROR,details:v.REMUX_ALLOC_ERROR,fatal:!1,error:e,bytes:W,reason:"fail allocating video mdat "+W})}var q=new DataView(H.buffer);q.setUint32(0,W),H.set(pt.types.mdat,4);for(var X=!1,Z=Number.POSITIVE_INFINITY,J=Number.POSITIVE_INFINITY,Q=Number.NEGATIVE_INFINITY,$=Number.NEGATIVE_INFINITY,ee=0;ee<l;ee++){for(var te=u[ee],re=te.units,ae=0,ie=0,ne=re.length;ie<ne;ie++){var se=re[ie],oe=se.data,ue=se.data.byteLength;q.setUint32(p,ue),p+=4,H.set(oe,p),p+=ue,ae+=4+ue}var de=void 0;if(ee<l-1)g=u[ee+1].dts-te.dts,de=u[ee+1].pts-te.pts;else{var le=this.config,ce=ee>0?te.dts-u[ee-1].dts:I;if(de=ee>0?te.pts-u[ee-1].pts:I,le.stretchShortVideoTrack&&null!==this.nextAudioTs){var he=Math.floor(le.maxBufferHole*o),fe=(a?S+a*o:this.nextAudioTs+h)-te.pts;fe>he?((g=fe-ce)<0?g=ce:X=!0,this.log("It is approximately "+fe/90+" ms to the next segment; using duration "+g/90+" ms for the last video frame.")):g=ce}else g=ce}var pe=Math.round(te.pts-te.dts);Z=Math.min(Z,g),Q=Math.max(Q,g),J=Math.min(J,de),$=Math.max($,de),d.push(Tt(te.key,g,ae,pe))}if(d.length)if(gt){if(gt<70){var me=d[0].flags;me.dependsOn=2,me.isNonSync=0}}else if(St&&$-J<Q-Z&&I/Q<.025&&0===d[0].cts){this.warn("Found irregular gaps in sample duration. Using PTS instead of DTS to determine MP4 sample duration.");for(var ve=i,ye=0,ge=d.length;ye<ge;ye++){var Se=ve+d[ye].duration,Te=ve+d[ye].cts;if(ye<ge-1){var Ee=Se+d[ye+1].cts;d[ye].duration=Ee-Te}else d[ye].duration=ye?d[ye-1].duration:I;d[ye].cts=0,ve=Se}}var be=n+(g=X||!g?I:g);this.nextVideoTs=f=be-h,this.videoSampleDuration=g,this.isVideoContiguous=!0;var Ae={data1:pt.moof(e.sequenceNumber++,i,s(e,{samples:d})),data2:H,startPTS:(S-h)/o,endPTS:(T+g-h)/o,startDTS:(i-h)/o,endDTS:f/o,type:"video",hasAudio:!1,hasVideo:!0,nb:d.length,dropped:e.dropped};return e.samples=[],e.dropped=0,Ae},r.getSamplesPerFrame=function(e){switch(e.segmentCodec){case"mp3":return 1152;case"ac3":return 1536;default:return 1024}},r.remuxAudio=function(e,t,r,a,i){var n=e.inputTimeScale,o=n/(e.samplerate?e.samplerate:n),u=this.getSamplesPerFrame(e),d=u*o,l=this._initPTS,c="mp3"===e.segmentCodec&&this.typeSupported.mpeg,h=[],f=void 0!==i,p=e.samples,g=c?0:8,S=this.nextAudioTs||-1,T=l.baseTime*n/l.timescale,E=T+t*n;if(this.isAudioContiguous=r=r||p.length&&S>0&&(a&&Math.abs(E-(S+T))<9e3||Math.abs(bt(p[0].pts,E)-(S+T))<20*d),p.forEach((function(e){e.pts=bt(e.pts,E)})),!r||S<0){if(p=p.filter((function(e){return e.pts>=0})),!p.length)return;S=0===i?0:a&&!f?Math.max(0,E-T):p[0].pts-T}if("aac"===e.segmentCodec)for(var b=this.config.maxAudioFramesDrift,A=0,_=S+T;A<p.length;A++){var R=p[A],k=R.pts,w=k-_,I=Math.abs(1e3*w/n);if(w<=-b*d&&f)0===A&&(this.warn("Audio frame @ "+(k/n).toFixed(3)+"s overlaps marker by "+Math.round(1e3*w/n)+" ms."),this.nextAudioTs=S=k-T,_=k);else if(w>=b*d&&I<1e4&&f){var U=Math.round(w/d);for(_=k-U*d;_<0&&U&&d;)U--,_+=d;0===A&&(this.nextAudioTs=S=_-T),this.warn("Injecting "+U+" audio frames @ "+((_-T)/n).toFixed(3)+"s due to "+Math.round(1e3*w/n)+" ms gap.");for(var x=0;x<U;x++){var P=ht.getSilentFrame(e.parsedCodec||e.manifestCodec||e.codec,e.channelCount);P||(this.log("Unable to get silent frame for given audio codec; duplicating last frame instead."),P=R.unit.subarray()),p.splice(A,0,{unit:P,pts:_}),_+=d,A++}}R.pts=_,_+=d}for(var D,C=null,L=null,O=0,B=p.length;B--;)O+=p[B].unit.byteLength;for(var M=0,N=p.length;M<N;M++){var F=p[M],G=F.unit,V=F.pts;if(null!==L){h[M-1].duration=Math.round((V-L)/o)}else{if(r&&"aac"===e.segmentCodec&&(V=S+T),C=V,!(O>0))return;O+=g;try{D=new Uint8Array(O)}catch(e){return void this.observer.emit(y.ERROR,y.ERROR,{type:m.MUX_ERROR,details:v.REMUX_ALLOC_ERROR,fatal:!1,error:e,bytes:O,reason:"fail allocating audio mdat "+O})}c||(new DataView(D.buffer).setUint32(0,O),D.set(pt.types.mdat,4))}D.set(G,g);var K=G.byteLength;g+=K,h.push(Tt(!0,u,K,0)),L=V}var Y=h.length;if(Y){var j=h[h.length-1];S=L-T,this.nextAudioTs=S+o*j.duration;var z=c?new Uint8Array(0):pt.moof(e.sequenceNumber++,C/o,s({},e,{samples:h}));e.samples=[];var H=(C-T)/n,W=S/n,q={data1:z,data2:D,startPTS:H,endPTS:W,startDTS:H,endDTS:W,type:"audio",hasAudio:!0,hasVideo:!1,nb:Y};return this.isAudioContiguous=!0,q}},t}(g);function bt(e,t){var r;if(null===t)return e;for(r=t<e?-8589934592:8589934592;Math.abs(e-t)>4294967296;)e+=r;return e}function At(e,t,r,a){var i=e.samples.length;if(i){for(var n=e.inputTimeScale,s=0;s<i;s++){var o=e.samples[s];o.pts=bt(o.pts-r.baseTime*n/r.timescale,t*n)/n,o.dts=bt(o.dts-a.baseTime*n/a.timescale,t*n)/n}var u=e.samples;return e.samples=[],{samples:u}}}function _t(e,t,r){var a=e.samples.length;if(a){for(var i=e.inputTimeScale,n=0;n<a;n++){var s=e.samples[n];s.pts=bt(s.pts-r.baseTime*i/r.timescale,t*i)/i}e.samples.sort((function(e,t){return e.pts-t.pts}));var o=e.samples;return e.samples=[],{samples:o}}}function Rt(e){if(void 0===e&&(e=!0),"undefined"!=typeof self)return(e||!self.MediaSource)&&self.ManagedMediaSource||self.MediaSource||self.WebKitMediaSource}function kt(e,t,r){var a;void 0===r&&(r=!0);var i=Rt(r);return null!=(a=null==i?void 0:i.isTypeSupported(function(e,t){return t+"/mp4;codecs="+e}(e,t)))&&a}var wt={};var It=/flac|opus|mp4a\.40\.34/i;function Ut(e,t){return void 0===t&&(t=!0),e.replace(It,(function(e){return function(e,t){if(void 0===t&&(t=!0),wt[e])return wt[e];for(var r={flac:["flac","fLaC","FLAC"],opus:["opus","Opus"],"mp4a.40.34":["mp3"]}[e],a=0;a<r.length;a++){var i;if(kt(r[a],"audio",t))return wt[e]=r[a],r[a];if("mp3"===r[a]&&null!=(i=Rt(t))&&i.isTypeSupported("audio/mpeg"))return""}return e}(e.toLowerCase(),t)}))}var xt,Pt=function(e){function t(t,r,a,i){var n;return(n=e.call(this,"passthrough-remuxer",i)||this).emitInitSegment=!1,n.audioCodec=void 0,n.videoCodec=void 0,n.initData=void 0,n.initPTS=null,n.initTracks=void 0,n.lastEndTime=null,n.isVideoContiguous=!1,n}o(t,e);var r=t.prototype;return r.destroy=function(){},r.resetTimeStamp=function(e){this.lastEndTime=null;var t=this.initPTS;t&&e&&t.baseTime===e.baseTime&&t.timescale===e.timescale||(this.initPTS=e)},r.resetNextTimestamp=function(){this.isVideoContiguous=!1,this.lastEndTime=null},r.resetInitSegment=function(e,t,r,a){this.audioCodec=t,this.videoCodec=r,this.generateInitSegment(e,a),this.emitInitSegment=!0},r.generateInitSegment=function(e,t){var r=this.audioCodec,a=this.videoCodec;if(null==e||!e.byteLength)return this.initTracks=void 0,void(this.initData=void 0);var i=this.initData=pe(e),n=i.audio,s=i.video;if(t)Te(e,t);else{var o=n||s;null!=o&&o.encrypted&&this.warn('Init segment with encrypted track with has no key ("'+o.codec+'")!')}n&&(r=Ct(n,re,this)),s&&(a=Ct(s,ae,this));var u={};n&&s?u.audiovideo={container:"video/mp4",codec:r+","+a,supplemental:s.supplemental,encrypted:s.encrypted,initSegment:e,id:"main"}:n?u.audio={container:"audio/mp4",codec:r,encrypted:n.encrypted,initSegment:e,id:"audio"}:s?u.video={container:"video/mp4",codec:a,supplemental:s.supplemental,encrypted:s.encrypted,initSegment:e,id:"main"}:this.warn("initSegment does not contain moov or trak boxes."),this.initTracks=u},r.remux=function(e,t,r,a,i,n){var s,o,u=this.initPTS,d=this.lastEndTime,l={audio:void 0,video:void 0,text:a,id3:r,initSegment:void 0};C(d)||(d=this.lastEndTime=i||0);var c=t.samples;if(!c.length)return l;var h={initPTS:void 0,timescale:void 0,trackId:void 0},f=this.initData;if(null!=(s=f)&&s.length||(this.generateInitSegment(c),f=this.initData),null==(o=f)||!o.length)return this.warn("Failed to generate initSegment."),l;this.emitInitSegment&&(h.tracks=this.initTracks,this.emitInitSegment=!1);var p=function(e,t,r){for(var a={},i=he(e,["moof","traf"]),n=0;n<i.length;n++){var s=i[n],o=he(s,["tfhd"])[0],u=de(o,4),d=t[u];if(d){a[u]||(a[u]={start:NaN,duration:0,sampleCount:0,timescale:d.timescale,type:d.type});var l=a[u],c=he(s,["tfdt"])[0];if(c){var h=c[0],f=de(c,4);1===h&&(f===ie?r.warn("[mp4-demuxer]: Ignoring assumed invalid signed 64-bit track fragment decode time"):(f*=ie+1,f+=de(c,8))),C(f)&&(!C(l.start)||f<l.start)&&(l.start=f)}var p=d.default,m=de(o,0)|(null==p?void 0:p.flags),v=(null==p?void 0:p.duration)||0;8&m&&(v=de(o,2&m?12:8));for(var y=he(s,["trun"]),g=l.start||0,S=0,T=v,E=0;E<y.length;E++){var b=y[E],A=de(b,4),_=l.sampleCount;l.sampleCount+=A;var R=1&b[3],k=4&b[3],w=1&b[2],I=2&b[2],U=4&b[2],x=8&b[2],P=8,D=A;for(R&&(P+=4),k&&A&&(1&b[P+1]||void 0!==l.keyFrameIndex||(l.keyFrameIndex=_),P+=4,w?(T=de(b,P),P+=4):T=v,I&&(P+=4),x&&(P+=4),g+=T,S+=T,D--);D--;)w?(T=de(b,P),P+=4):T=v,I&&(P+=4),U&&(1&b[P+1]||void 0===l.keyFrameIndex&&(l.keyFrameIndex=l.sampleCount-(D+1),l.keyFrameStart=g),P+=4),x&&(P+=4),g+=T,S+=T;!S&&v&&(S+=v*A)}l.duration+=S}}if(!Object.keys(a).some((function(e){return a[e].duration}))){for(var L=1/0,O=0,B=he(e,["sidx"]),M=0;M<B.length;M++){var N=fe(B[M]);if(null!=N&&N.references){L=Math.min(L,N.earliestPresentationTime/N.timescale);var F=N.references.reduce((function(e,t){return e+t.info.duration||0}),0);O=Math.max(O,F+N.earliestPresentationTime/N.timescale)}}O&&C(O)&&Object.keys(a).forEach((function(e){a[e].duration||(a[e].duration=O*a[e].timescale-a[e].start)}))}return a}(c,f,this),m=f.audio?p[f.audio.id]:null,v=f.video?p[f.video.id]:null,y=Dt(v,1/0),g=Dt(m,1/0),S=Dt(v,0,!0),T=Dt(m,0,!0),E=i,b=0,A=m&&(!v||!u&&g<y||u&&u.trackId===f.audio.id),_=A?m:v;if(_){var R=_.timescale,k=_.start-i*R,w=A?f.audio.id:f.video.id;E=_.start/R,b=A?T-g:S-y,!n&&u||!function(e,t,r,a){if(null===e)return!0;var i=Math.max(a,1),n=t-e.baseTime/e.timescale;return Math.abs(n-r)>i}(u,E,i,b)&&R===u.timescale||(u&&this.warn("Timestamps at playlist time: "+(n?"":"~")+i+" "+k/R+" != initPTS: "+u.baseTime/u.timescale+" ("+u.baseTime+"/"+u.timescale+") trackId: "+u.trackId),this.log("Found initPTS at playlist time: "+i+" offset: "+(E-i)+" ("+k+"/"+R+") trackId: "+w),u=null,h.initPTS=k,h.timescale=R,h.trackId=w)}else this.warn("No audio or video samples found for initPTS at playlist time: "+i);u?(h.initPTS=u.baseTime,h.timescale=u.timescale,h.trackId=u.trackId):(h.timescale&&void 0!==h.trackId&&void 0!==h.initPTS||(this.warn("Could not set initPTS"),h.initPTS=E,h.timescale=1,h.trackId=-1),this.initPTS=u={baseTime:h.initPTS,timescale:h.timescale,trackId:h.trackId});var I=E-u.baseTime/u.timescale,U=I+b;b>0?this.lastEndTime=U:(this.warn("Duration parsed from mp4 should be greater than zero"),this.resetNextTimestamp());var x=!!f.audio,P=!!f.video,D="";x&&(D+="audio"),P&&(D+="video");var L={data1:c,startPTS:I,startDTS:I,endPTS:U,endDTS:U,type:D,hasAudio:x,hasVideo:P,nb:1,dropped:0,encrypted:!!f.audio&&f.audio.encrypted||!!f.video&&f.video.encrypted};l.audio=x&&!P?L:void 0,l.video=P?L:void 0;var O=null==v?void 0:v.sampleCount;if(O){var B=v.keyFrameIndex,M=-1!==B;L.nb=O,L.dropped=0===B||this.isVideoContiguous?0:M?B:O,L.independent=M,L.firstKeyFrame=B,M&&v.keyFrameStart&&(L.firstKeyFramePTS=(v.keyFrameStart-u.baseTime)/u.timescale),this.isVideoContiguous||(l.independent=M),this.isVideoContiguous||(this.isVideoContiguous=M),L.dropped&&this.warn("fmp4 does not start with IDR: firstIDR "+B+"/"+O+" dropped: "+L.dropped+" start: "+(L.firstKeyFramePTS||"NA"))}return l.initSegment=h,l.id3=At(r,i,u,u),a.samples.length&&(l.text=_t(a,i,u)),l},t}(g);function Dt(e,t,r){return void 0===r&&(r=!1),void 0!==(null==e?void 0:e.start)?(e.start+(r?e.duration:0))/e.timescale:t}function Ct(e,t,r){var a=e.codec;if(a&&a.length>4)return a;if(t===re){if("ec-3"===a||"ac-3"===a||"alac"===a)return a;if("fLaC"===a||"Opus"===a){return Ut(a,!1)}return r.warn('Unhandled audio codec "'+a+'" in mp4 MAP'),a||"mp4a"}return r.warn('Unhandled video codec "'+a+'" in mp4 MAP'),a||"avc1"}try{xt=self.performance.now.bind(self.performance)}catch(e){xt=Date.now}var Lt=[{demux:Je,remux:Pt},{demux:nt,remux:Et},{demux:Fe,remux:Et},{demux:Ye,remux:Et}];Lt.splice(2,0,{demux:Ve,remux:Et});var Ot=function(){function e(e,t,r,a,i,n){this.asyncResult=!1,this.logger=void 0,this.observer=void 0,this.typeSupported=void 0,this.config=void 0,this.id=void 0,this.demuxer=void 0,this.remuxer=void 0,this.decrypter=void 0,this.probe=void 0,this.decryptionPromise=null,this.transmuxConfig=void 0,this.currentTransmuxState=void 0,this.observer=e,this.typeSupported=t,this.config=r,this.id=i,this.logger=n}var t=e.prototype;return t.configure=function(e){this.transmuxConfig=e,this.decrypter&&this.decrypter.reset()},t.push=function(e,t,r,a){var i=this,n=r.transmuxing;n.executeStart=xt();var s=new Uint8Array(e),o=this.currentTransmuxState,u=this.transmuxConfig;a&&(this.currentTransmuxState=a);var d,l=a||o,c=l.contiguous,h=l.discontinuity,f=l.trackSwitch,p=l.accurateTimeOffset,g=l.timeOffset,S=l.initSegmentChange,T=u.audioCodec,E=u.videoCodec,b=u.defaultInitPts,A=u.duration,_=u.initSegmentData,R=function(e,t){var r=null;e.byteLength>0&&null!=(null==t?void 0:t.key)&&null!==t.iv&&null!=t.method&&(r=t);return r}(s,t);if(R&&("AES-128"===(d=R.method)||"AES-256"===d||"AES-256-CTR"===d)){var k=this.getDecrypter(),w=function(e){switch(e){case"AES-128":case"AES-256":return je;case"AES-256-CTR":return ze;default:throw new Error("invalid full segment method "+e)}}(R.method);if(!k.isSync())return this.asyncResult=!0,this.decryptionPromise=k.webCryptoDecrypt(s,R.key.buffer,R.iv.buffer,w).then((function(e){var t=i.push(e,null,r);return i.decryptionPromise=null,t})),this.decryptionPromise;var I=k.softwareDecrypt(s,R.key.buffer,R.iv.buffer,w);if(r.part>-1){var U=k.flush();I=U?U.buffer:U}if(!I)return n.executeEnd=xt(),Bt(r);s=new Uint8Array(I)}var x=this.needsProbing(h,f);if(x){var P=this.configureTransmuxer(s);if(P)return this.logger.warn("[transmuxer] "+P.message),this.observer.emit(y.ERROR,y.ERROR,{type:m.MEDIA_ERROR,details:v.FRAG_PARSING_ERROR,fatal:!1,error:P,reason:P.message}),n.executeEnd=xt(),Bt(r)}(h||f||S||x)&&this.resetInitSegment(_,T,E,A,t),(h||S||x)&&this.resetInitialTimestamp(b),c||this.resetContiguity();var D=this.transmux(s,R,g,p,r);this.asyncResult=Mt(D);var C=this.currentTransmuxState;return C.contiguous=!0,C.discontinuity=!1,C.trackSwitch=!1,n.executeEnd=xt(),D},t.flush=function(e){var t=this,r=e.transmuxing;r.executeStart=xt();var a=this.decrypter,i=this.currentTransmuxState,n=this.decryptionPromise;if(n)return this.asyncResult=!0,n.then((function(){return t.flush(e)}));var s=[],o=i.timeOffset;if(a){var u=a.flush();u&&s.push(this.push(u.buffer,null,e))}var d=this.demuxer,l=this.remuxer;if(!d||!l){r.executeEnd=xt();var c=[Bt(e)];return this.asyncResult?Promise.resolve(c):c}var h=d.flush(o);return Mt(h)?(this.asyncResult=!0,h.then((function(r){return t.flushRemux(s,r,e),s}))):(this.flushRemux(s,h,e),this.asyncResult?Promise.resolve(s):s)},t.flushRemux=function(e,t,r){var a=t.audioTrack,i=t.videoTrack,n=t.id3Track,s=t.textTrack,o=this.currentTransmuxState,u=o.accurateTimeOffset,d=o.timeOffset;this.logger.log("[transmuxer.ts]: Flushed "+this.id+" sn: "+r.sn+(r.part>-1?" part: "+r.part:"")+" of "+(this.id===mt?"level":"track")+" "+r.level);var l=this.remuxer.remux(a,i,n,s,d,u,!0,this.id);e.push({remuxResult:l,chunkMeta:r}),r.transmuxing.executeEnd=xt()},t.resetInitialTimestamp=function(e){var t=this.demuxer,r=this.remuxer;t&&r&&(t.resetTimeStamp(e),r.resetTimeStamp(e))},t.resetContiguity=function(){var e=this.demuxer,t=this.remuxer;e&&t&&(e.resetContiguity(),t.resetNextTimestamp())},t.resetInitSegment=function(e,t,r,a,i){var n=this.demuxer,s=this.remuxer;n&&s&&(n.resetInitSegment(e,t,r,a),s.resetInitSegment(e,t,r,i))},t.destroy=function(){this.demuxer&&(this.demuxer.destroy(),this.demuxer=void 0),this.remuxer&&(this.remuxer.destroy(),this.remuxer=void 0)},t.transmux=function(e,t,r,a,i){return t&&"SAMPLE-AES"===t.method?this.transmuxSampleAes(e,t,r,a,i):this.transmuxUnencrypted(e,r,a,i)},t.transmuxUnencrypted=function(e,t,r,a){var i=this.demuxer.demux(e,t,!1,!this.config.progressive),n=i.audioTrack,s=i.videoTrack,o=i.id3Track,u=i.textTrack;return{remuxResult:this.remuxer.remux(n,s,o,u,t,r,!1,this.id),chunkMeta:a}},t.transmuxSampleAes=function(e,t,r,a,i){var n=this;return this.demuxer.demuxSampleAes(e,t,r).then((function(e){return{remuxResult:n.remuxer.remux(e.audioTrack,e.videoTrack,e.id3Track,e.textTrack,r,a,!1,n.id),chunkMeta:i}}))},t.configureTransmuxer=function(e){for(var t,r=this.config,a=this.observer,i=this.typeSupported,n=0,s=Lt.length;n<s;n++){var o;if(null!=(o=Lt[n].demux)&&o.probe(e,this.logger)){t=Lt[n];break}}if(!t)return new Error("Failed to find demuxer by probing fragment data");var u=this.demuxer,d=this.remuxer,l=t.remux,c=t.demux;d&&d instanceof l||(this.remuxer=new l(a,r,i,this.logger)),u&&u instanceof c||(this.demuxer=new c(a,r,i,this.logger),this.probe=c.probe)},t.needsProbing=function(e,t){return!this.demuxer||!this.remuxer||e||t},t.getDecrypter=function(){var e=this.decrypter;return e||(e=this.decrypter=new Xe(this.config)),e},e}();var Bt=function(e){return{remuxResult:{},chunkMeta:e}};function Mt(e){return"then"in e&&e.then instanceof Function}var Nt=[];function Ft(e,t,r){if(!((a=t.remuxResult).audio||a.video||a.text||a.id3||a.initSegment))return!1;var a,i=[],n=t.remuxResult,s=n.audio,o=n.video;return s&&Gt(i,s),o&&Gt(i,o),e.postMessage({event:"transmuxComplete",data:t,instanceNo:r},i),!0}function Gt(e,t){t.data1&&e.push(t.data1.buffer),t.data2&&e.push(t.data2.buffer)}function Vt(e,t,r,a){t.reduce((function(t,r){return Ft(e,r,a)||t}),!1)||e.postMessage({event:"transmuxComplete",data:t[0],instanceNo:a}),e.postMessage({event:"flush",data:r,instanceNo:a})}function Kt(e,t,r){self.postMessage({event:e,data:t,instanceNo:r})}self.addEventListener("message",(function(e){var t=e.data,r=t.instanceNo;if(void 0!==r){var a=Nt[r];if("reset"===t.cmd&&(delete Nt[t.resetNo],a&&a.destroy(),t.cmd="init"),"init"===t.cmd){var n=JSON.parse(t.config),o=new i;o.on(y.FRAG_DECRYPTED,Kt),o.on(y.ERROR,Kt);var u=function(e,t,r){var a=E();if("object"==typeof console&&!0===e||"object"==typeof e){var i=["debug","log","info","warn","error"];i.forEach((function(t){a[t]=b(t,e)}));try{a.log('Debug logs enabled for "'+t+'" in hls.js version 1.6.9')}catch(e){return E()}i.forEach((function(t){A[t]=b(t,e)}))}else s(A,a);return a}(n.debug,t.id);return function(e,t){var r=function(r){e[r]=function(){var e=Array.prototype.join.call(arguments," ");Kt("workerLog",{logType:r,message:e},t)}};for(var a in e)r(a)}(u,r),Nt[r]=new Ot(o,t.typeSupported,n,"",t.id,u),void Kt("init",null,r)}if(a)switch(t.cmd){case"configure":a.configure(t.config);break;case"demux":var d=a.push(t.data,t.decryptdata,t.chunkMeta,t.state);Mt(d)?d.then((function(e){Ft(self,e,r)})).catch((function(e){Kt(y.ERROR,{instanceNo:r,type:m.MEDIA_ERROR,details:v.FRAG_PARSING_ERROR,chunkMeta:t.chunkMeta,fatal:!1,error:e,err:e,reason:"transmuxer-worker push error"},r)})):Ft(self,d,r);break;case"flush":var l=t.chunkMeta,c=a.flush(l);Mt(c)?c.then((function(e){Vt(self,e,l,r)})).catch((function(e){Kt(y.ERROR,{type:m.MEDIA_ERROR,details:v.FRAG_PARSING_ERROR,chunkMeta:t.chunkMeta,fatal:!1,error:e,err:e,reason:"transmuxer-worker flush error"},r)})):Vt(self,c,l,r)}}}))}();
//# sourceMappingURL=hls.worker.js.map
