!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("preact")):"function"==typeof define&&define.amd?define(["exports","preact"],t):t((e||self).preactRenderToString={},e.preact)}(this,function(e,t){var r=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,n=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,o=/[\s\n\\/='"\0<>]/,i=/^xlink:?./,s=/["&<]/;function a(e){if(!1===s.test(e+=""))return e;for(var t=0,r=0,n="",o="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 60:o="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=o,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var l=function(e,t){return String(e).replace(/(\n+)/g,"$1"+(t||"\t"))},f=function(e,t,r){return String(e).length>(t||40)||!r&&-1!==String(e).indexOf("\n")||-1!==String(e).indexOf("<")},c={},u=/([A-Z])/g;function p(e){var t="";for(var n in e){var o=e[n];null!=o&&""!==o&&(t&&(t+=" "),t+="-"==n[0]?n:c[n]||(c[n]=n.replace(u,"-$1").toLowerCase()),t="number"==typeof o&&!1===r.test(n)?t+": "+o+"px;":t+": "+o+";")}return t||void 0}function d(e,t){return Array.isArray(t)?t.reduce(d,e):null!=t&&!1!==t&&e.push(t),e}function _(){this.__d=!0}function v(e,t){return{__v:e,context:t,props:e.props,setState:_,forceUpdate:_,__d:!0,__h:[]}}function g(e,t){var r=e.contextType,n=r&&t[r.__c];return null!=r?n?n.props.value:r.__:t}var h=[];function y(e,r,s,c,u,_){if(null==e||"boolean"==typeof e)return"";if("object"!=typeof e)return a(e);var m=s.pretty,b=m&&"string"==typeof m?m:"\t";if(Array.isArray(e)){for(var x="",S=0;S<e.length;S++)m&&S>0&&(x+="\n"),x+=y(e[S],r,s,c,u,_);return x}var k,w=e.type,C=e.props,O=!1;if("function"==typeof w){if(O=!0,!s.shallow||!c&&!1!==s.renderRootComponent){if(w===t.Fragment){var j=[];return d(j,e.props.children),y(j,r,s,!1!==s.shallowHighOrder,u,_)}var A,F=e.__c=v(e,r);t.options.__b&&t.options.__b(e);var T=t.options.__r;if(w.prototype&&"function"==typeof w.prototype.render){var H=g(w,r);(F=e.__c=new w(C,H)).__v=e,F._dirty=F.__d=!0,F.props=C,null==F.state&&(F.state={}),null==F._nextState&&null==F.__s&&(F._nextState=F.__s=F.state),F.context=H,w.getDerivedStateFromProps?F.state=Object.assign({},F.state,w.getDerivedStateFromProps(F.props,F.state)):F.componentWillMount&&(F.componentWillMount(),F.state=F._nextState!==F.state?F._nextState:F.__s!==F.state?F.__s:F.state),T&&T(e),A=F.render(F.props,F.state,F.context)}else for(var M=g(w,r),L=0;F.__d&&L++<25;)F.__d=!1,T&&T(e),A=w.call(e.__c,C,M);return F.getChildContext&&(r=Object.assign({},r,F.getChildContext())),t.options.diffed&&t.options.diffed(e),y(A,r,s,!1!==s.shallowHighOrder,u,_)}w=(k=w).displayName||k!==Function&&k.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!t){for(var r=-1,n=h.length;n--;)if(h[n]===e){r=n;break}r<0&&(r=h.push(e)-1),t="UnnamedComponent"+r}return t}(k)}var E,$,D="<"+w;if(C){var N=Object.keys(C);s&&!0===s.sortAttributes&&N.sort();for(var P=0;P<N.length;P++){var R=N[P],W=C[R];if("children"!==R){if(!o.test(R)&&(s&&s.allAttributes||"key"!==R&&"ref"!==R&&"__self"!==R&&"__source"!==R)){if("defaultValue"===R)R="value";else if("defaultChecked"===R)R="checked";else if("defaultSelected"===R)R="selected";else if("className"===R){if(void 0!==C.class)continue;R="class"}else u&&i.test(R)&&(R=R.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===R){if(C.for)continue;R="for"}"style"===R&&W&&"object"==typeof W&&(W=p(W)),"a"===R[0]&&"r"===R[1]&&"boolean"==typeof W&&(W=String(W));var q=s.attributeHook&&s.attributeHook(R,W,r,s,O);if(q||""===q)D+=q;else if("dangerouslySetInnerHTML"===R)$=W&&W.__html;else if("textarea"===w&&"value"===R)E=W;else if((W||0===W||""===W)&&"function"!=typeof W){if(!(!0!==W&&""!==W||(W=R,s&&s.xml))){D=D+" "+R;continue}if("value"===R){if("select"===w){_=W;continue}"option"===w&&_==W&&void 0===C.selected&&(D+=" selected")}D=D+" "+R+'="'+a(W)+'"'}}}else E=W}}if(m){var I=D.replace(/\n\s*/," ");I===D||~I.indexOf("\n")?m&&~D.indexOf("\n")&&(D+="\n"):D=I}if(D+=">",o.test(w))throw new Error(w+" is not a valid HTML tag name in "+D);var U,V=n.test(w)||s.voidElements&&s.voidElements.test(w),z=[];if($)m&&f($)&&($="\n"+b+l($,b)),D+=$;else if(null!=E&&d(U=[],E).length){for(var Z=m&&~D.indexOf("\n"),B=!1,G=0;G<U.length;G++){var J=U[G];if(null!=J&&!1!==J){var K=y(J,r,s,!0,"svg"===w||"foreignObject"!==w&&u,_);if(m&&!Z&&f(K)&&(Z=!0),K)if(m){var Q=K.length>0&&"<"!=K[0];B&&Q?z[z.length-1]+=K:z.push(K),B=Q}else z.push(K)}}if(m&&Z)for(var X=z.length;X--;)z[X]="\n"+b+l(z[X],b)}if(z.length||$)D+=z.join("");else if(s&&s.xml)return D.substring(0,D.length-1)+" />";return!V||U||$?(m&&~D.indexOf("\n")&&(D+="\n"),D=D+"</"+w+">"):D=D.replace(/>$/," />"),D}var m={shallow:!0};S.render=S;var b=function(e,t){return S(e,t,m)},x=[];function S(e,r,n){r=r||{};var o,i=t.options.__s;return t.options.__s=!0,o=n&&(n.pretty||n.voidElements||n.sortAttributes||n.shallow||n.allAttributes||n.xml||n.attributeHook)?y(e,r,n):j(e,r,!1,void 0),t.options.__c&&t.options.__c(e,x),t.options.__s=i,x.length=0,o}function k(e,t){return"className"===e?"class":"htmlFor"===e?"for":"defaultValue"===e?"value":"defaultChecked"===e?"checked":"defaultSelected"===e?"selected":t&&i.test(e)?e.toLowerCase().replace(/^xlink:?/,"xlink:"):e}function w(e,t){return"style"===e&&null!=t&&"object"==typeof t?p(t):"a"===e[0]&&"r"===e[1]&&"boolean"==typeof t?String(t):t}var C=Array.isArray,O=Object.assign;function j(e,r,i,s){if(null==e||!0===e||!1===e||""===e)return"";if("object"!=typeof e)return a(e);if(C(e)){for(var l="",f=0;f<e.length;f++)l+=j(e[f],r,i,s);return l}t.options.__b&&t.options.__b(e);var c=e.type,u=e.props;if("function"==typeof c){if(c===t.Fragment)return j(e.props.children,r,i,s);var p;p=c.prototype&&"function"==typeof c.prototype.render?function(e,r){var n=e.type,o=g(n,r),i=new n(e.props,o);e.__c=i,i.__v=e,i.__d=!0,i.props=e.props,null==i.state&&(i.state={}),null==i.__s&&(i.__s=i.state),i.context=o,n.getDerivedStateFromProps?i.state=O({},i.state,n.getDerivedStateFromProps(i.props,i.state)):i.componentWillMount&&(i.componentWillMount(),i.state=i.__s!==i.state?i.__s:i.state);var s=t.options.__r;return s&&s(e),i.render(i.props,i.state,i.context)}(e,r):function(e,r){var n,o=v(e,r),i=g(e.type,r);e.__c=o;for(var s=t.options.__r,a=0;o.__d&&a++<25;)o.__d=!1,s&&s(e),n=e.type.call(o,e.props,i);return n}(e,r);var d=e.__c;d.getChildContext&&(r=O({},r,d.getChildContext()));var _=j(p,r,i,s);return t.options.diffed&&t.options.diffed(e),_}var h,y,m="<";if(m+=c,u)for(var b in h=u.children,u){var x=u[b];if(!("key"===b||"ref"===b||"__self"===b||"__source"===b||"children"===b||"className"===b&&"class"in u||"htmlFor"===b&&"for"in u||o.test(b)))if(x=w(b=k(b,i),x),"dangerouslySetInnerHTML"===b)y=x&&x.__html;else if("textarea"===c&&"value"===b)h=x;else if((x||0===x||""===x)&&"function"!=typeof x){if(!0===x||""===x){x=b,m=m+" "+b;continue}if("value"===b){if("select"===c){s=x;continue}"option"!==c||s!=x||"selected"in u||(m+=" selected")}m=m+" "+b+'="'+a(x)+'"'}}var S=m;if(m+=">",o.test(c))throw new Error(c+" is not a valid HTML tag name in "+m);var A="",F=!1;if(y)A+=y,F=!0;else if("string"==typeof h)A+=a(h),F=!0;else if(C(h))for(var T=0;T<h.length;T++){var H=h[T];if(null!=H&&!1!==H){var M=j(H,r,"svg"===c||"foreignObject"!==c&&i,s);M&&(A+=M,F=!0)}}else if(null!=h&&!1!==h&&!0!==h){var L=j(h,r,"svg"===c||"foreignObject"!==c&&i,s);L&&(A+=L,F=!0)}if(t.options.diffed&&t.options.diffed(e),F)m+=A;else if(n.test(c))return S+" />";return m+"</"+c+">"}S.shallowRender=b,e.default=S,e.render=S,e.renderToStaticMarkup=S,e.renderToString=S,e.shallowRender=b});
//# sourceMappingURL=index.js.map
