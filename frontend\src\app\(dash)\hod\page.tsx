'use client';

// HOD Dashboard - Overview of teachers, students, and analytics

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  BookOpen, 
  MessageSquare, 
  TrendingUp,
  Bell,
  BarChart3,
  UserCheck,
  GraduationCap,
  Plus,
  Eye,
  AlertCircle
} from 'lucide-react';
import { useAnalytics, useDataLoaders } from '@/store/useUser';

export default function HODDashboard() {
  const { data: session } = useSession();
  const router = useRouter();
  const analytics = useAnalytics();
  const { loadAnalytics } = useDataLoaders();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      await loadAnalytics();
      setIsLoading(false);
    };
    loadData();
  }, [loadAnalytics]);

  const handleCreateAnnouncement = () => {
    router.push('/hod/announcements/create');
  };

  const handleViewTeachers = () => {
    router.push('/hod/teachers');
  };

  const handleViewAnalytics = () => {
    router.push('/hod/analytics');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome, {session?.user?.name}!
        </h1>
        <p className="text-purple-100">
          Monitor your institution's performance and manage teachers and students effectively.
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Users</p>
                <p className="text-2xl font-bold">{analytics?.totalUsers || 150}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <GraduationCap className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Students</p>
                <p className="text-2xl font-bold">{analytics?.totalStudents || 120}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <UserCheck className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Teachers</p>
                <p className="text-2xl font-bold">{analytics?.totalTeachers || 25}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <BookOpen className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Subjects</p>
                <p className="text-2xl font-bold">{analytics?.totalSubjects || 15}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Activity Overview</span>
            </CardTitle>
            <CardDescription>Current platform engagement</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Active Users Today</span>
                <span className="font-bold">{analytics?.activeUsersToday || 45}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Active Users This Week</span>
                <span className="font-bold">{analytics?.activeUsersThisWeek || 98}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total AI Chats</span>
                <span className="font-bold">{analytics?.totalChats || 1250}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Study Materials</span>
                <span className="font-bold">{analytics?.totalMaterials || 85}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Popular Subjects</span>
            </CardTitle>
            <CardDescription>Most active subjects by chat volume</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {(analytics?.popularSubjects || [
                { subjectId: '1', subjectName: 'Japanese Language', chatCount: 450 },
                { subjectId: '2', subjectName: 'Mathematics', chatCount: 320 },
                { subjectId: '3', subjectName: 'English Literature', chatCount: 280 },
              ]).map((subject, index) => (
                <div key={subject.subjectId} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-bold text-blue-600">
                      {index + 1}
                    </div>
                    <span className="font-medium">{subject.subjectName}</span>
                  </div>
                  <Badge variant="secondary">{subject.chatCount} chats</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common administrative tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button onClick={handleCreateAnnouncement} className="h-20 flex-col space-y-2">
              <Bell className="h-6 w-6" />
              <span>Create Announcement</span>
            </Button>
            <Button onClick={handleViewTeachers} variant="outline" className="h-20 flex-col space-y-2">
              <UserCheck className="h-6 w-6" />
              <span>Manage Teachers</span>
            </Button>
            <Button onClick={handleViewAnalytics} variant="outline" className="h-20 flex-col space-y-2">
              <BarChart3 className="h-6 w-6" />
              <span>View Analytics</span>
            </Button>
            <Button onClick={() => router.push('/hod/reports')} variant="outline" className="h-20 flex-col space-y-2">
              <Eye className="h-6 w-6" />
              <span>Generate Reports</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Announcements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Recent Announcements</span>
            </div>
            <Button onClick={handleCreateAnnouncement} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New
            </Button>
          </CardTitle>
          <CardDescription>Latest announcements to teachers and students</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="flex-1">
                <p className="font-medium">System Maintenance Scheduled</p>
                <p className="text-sm text-gray-600">Platform will be down for maintenance on Sunday 2-4 AM</p>
                <p className="text-xs text-gray-500 mt-1">Posted 2 hours ago</p>
              </div>
              <Badge variant="secondary">Urgent</Badge>
            </div>
            
            <div className="flex items-start space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <Bell className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <p className="font-medium">New AI Features Available</p>
                <p className="text-sm text-gray-600">Enhanced voice recognition and grammar analysis now live</p>
                <p className="text-xs text-gray-500 mt-1">Posted 1 day ago</p>
              </div>
              <Badge variant="outline">Info</Badge>
            </div>
            
            <div className="flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <GraduationCap className="h-5 w-5 text-green-600 mt-0.5" />
              <div className="flex-1">
                <p className="font-medium">Teacher Training Workshop</p>
                <p className="text-sm text-gray-600">AI Avatar customization workshop scheduled for next Friday</p>
                <p className="text-xs text-gray-500 mt-1">Posted 3 days ago</p>
              </div>
              <Badge variant="outline">Event</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Teacher Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Teacher Performance</CardTitle>
          <CardDescription>Top performing teachers this month</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              { name: 'Tanaka Sensei', subject: 'Japanese Language', students: 45, rating: 4.9 },
              { name: 'Smith Sensei', subject: 'Mathematics', students: 38, rating: 4.8 },
              { name: 'Johnson Sensei', subject: 'English Literature', students: 42, rating: 4.7 },
            ].map((teacher, index) => (
              <div key={teacher.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    {teacher.name.charAt(0)}
                  </div>
                  <div>
                    <p className="font-medium">{teacher.name}</p>
                    <p className="text-sm text-gray-600">{teacher.subject}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{teacher.students} students</p>
                  <p className="text-sm text-gray-600">★ {teacher.rating}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
